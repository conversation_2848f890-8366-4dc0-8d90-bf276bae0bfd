export default async function handler(req, res) {
  const { city, country } = req.query;

  if (!city || !country) {
    res.status(400).json({ error: 'City and Country are required' });
    return;
  }

  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
  const address = `${city}, ${country}`;

  try {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${apiKey}`
    );
    const data = await response.json();

    if (data.status === "OK" && data.results.length > 0) {
      const { lat, lng } = data.results[0].geometry.location;
      res.status(200).json({ lat, lng });
    } else {
      res.status(400).json({ error: "Geocode Failed", details: data.status });
    }
  } catch (error) {
    res.status(500).json({ error: "Internal Server Error", details: error.message });
  }
}
