// components/MultiDatePicker.js

import React, { useState } from 'react';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DateCalendar } from '@mui/x-date-pickers/DateCalendar';

import dayjs from 'dayjs';

const MultiDatePicker = () => {
 
 

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
     <DateCalendar
    //   sx={{
    //     '& .MuiPickersDay-root': { fontSize: '1rem', width: 50, height: 50 }, // Increase day size
    //     '& .MuiTypography-root': { fontSize: '1rem' }, // Increase header text size
    //     '& .MuiPickersCalendarHeader-root': { padding: '10px' }, // Adjust header padding
    //     '& .MuiPickersDay-today': { border: '2px solid #1976d2' }, // Highlight today’s date
    //   }}
    />
  </LocalizationProvider>
  );
};

export default MultiDatePicker;
