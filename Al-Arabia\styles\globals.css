/* :root {
  --background: #ffffff;
  --foreground: #171717;
} */

@font-face {
  font-family: "ArbFonts";
  src: url("/fonts/arbfonts-ge_thameen_book.woff") format("woff");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "ArbFontsBold";
  src: url("/fonts/arbfonts-ge_thameen_demibold.woff") format("woff");
  font-weight: bold;
  font-style: normal;
}

[data-aos] {
  -webkit-transform: translate3d(0, 0, 0);
  will-change: opacity, transform;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  font-family: var(--aller_rg);
  /* font-family: var(--aller_lt); */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #ececec;
  text-transform: none;

  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.rtl .main {
  font-family: var(--arbfonts), sans-serif;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
  transition: all 0.4s ease-out 0s;
  -moz-transition: all 0.4s ease-out 0s;
  -webkit-transition: all 0.4s ease-out 0s;
  -o-transition: all 0.4s ease-out 0s;
}

.responsiveImage {
  height: auto;
  max-width: 100%;
  display: block;
}

ul {
  margin: 0;
  padding: 0;
}

ul li {
  list-style: none;
}

p {
  color: #3b3064;
  font-size: 15px;
  line-height: 24px;
  text-transform: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

#font_size {
  font-size: 100%;
}

@media only screen and (max-width: 1440px) {
  #font_size {
    font-size: 90%;
  }
}

@media (min-resolution: 120dpi) {
  #font_size {
    font-size: 90%;
  }
}

@media only screen and (max-width: 1024px) {
  #font_size {
    font-size: 80%;
  }

  #container_txt {
    min-height: unset;
  }
}

@media only screen and (max-width: 1000px) {
  #font_size {
    font-size: 60%;
  }
}

@media only screen and (max-width: 700px) {
  #font_size {
    font-size: 70%;
  }

  .svg-container {
    width: 118%;
    left: -11%;
    top: -7%;
  }

  .rtl .svg-container {
    left: 7%;
  }

  /* .rtl  .svg-container{} */

  .project_second_slider_section .project_swiper .swiper-slide-active {
    min-height: unset;
  }

  p {
    font-size: 13px;
    line-height: 22px;
  }
}

@media only screen and (max-width: 1024px) {
  .menu-ul .main-logo {
    display: none;
  }
}

@media only screen and (max-width: 700px) {
  /* .menu-ul .main-logo {
    display: none;
  } */

  .tooltip::before {
    display: none;
  }

  .project_swiper .swiper-slide.swiper-slide-prev,
  .project_swiper .swiper-slide.swiper-slide-next {
    margin-top: 0;
  }

  .project_swiper .swiper-slide.swiper-slide-next {
    right: unset;
  }

  .project_swiper .swiper-slide {
    margin-top: 0 !important;
  }
}

.swipper_home .mySwiper.swiper_slides_sec {
  padding-top: 20px;
}

.swipper_home .swiper-slide {
  position: relative;
  width: var(--swiper-width);
  transform: scale(0.84);
  transition: all 0.3s ease-in-out;
  overflow: hidden;
}

.swipper_home .swiper-slide {
  /* transform: scale(0.84) translateZ(0) translateY(50px) !important; */
  transform: scale(0.79) translateZ(0) translateY(50px) !important;
}

.swipper_home .mySwiper.swiper_slides_sec .swiper-slide-prev {
  transform: scale(0.79) translateZ(0) translateY(50px) translateX(50px) !important;
}

.swipper_home .mySwiper.swiper_slides_sec .swiper-slide-next {
  transform: scale(0.79) translateZ(0) translateY(50px) translateX(-50px) !important;
}

.rtl .swipper_home .mySwiper.swiper_slides_sec .swiper-slide-prev {
  transform: scale(0.79) translateZ(0) translateY(50px) translateX(-50px) !important;
}

.rtl .swipper_home .mySwiper.swiper_slides_sec .swiper-slide-next {
  transform: scale(0.79) translateZ(0) translateY(50px) translateX(50px) !important;
}

@media screen and (max-width: 1600px) {
  .swipper_home .mySwiper.swiper_slides_sec .swiper-slide-prev {
    transform: scale(0.79) translateZ(0) translateY(50px) translateX(20px) !important;
  }

  .swipper_home .mySwiper.swiper_slides_sec .swiper-slide-next {
    transform: scale(0.79) translateZ(0) translateY(50px) translateX(-20px) !important;
  }

  .rtl .swipper_home .mySwiper.swiper_slides_sec .swiper-slide-prev {
    transform: scale(0.79) translateZ(0) translateY(50px) translateX(-20px) !important;
  }

  .rtl .swipper_home .mySwiper.swiper_slides_sec .swiper-slide-next {
    transform: scale(0.79) translateZ(0) translateY(50px) translateX(20px) !important;
  }
}

@media screen and (max-width: 1440px) {
  .swipper_home .mySwiper.swiper_slides_sec .swiper-slide-prev {
    transform: scale(0.79) translateZ(0) translateY(50px) translateX(20px) !important;
  }

  .swipper_home .mySwiper.swiper_slides_sec .swiper-slide-next {
    transform: scale(0.79) translateZ(0) translateY(50px) translateX(-20px) !important;
  }
}

@media screen and (max-width: 1000px) {
  .swipper_home .mySwiper.swiper_slides_sec .swiper-slide-prev {
    transform: scale(0.79) translateZ(0) translateY(50px) translateX(0px) !important;
  }

  .swipper_home .mySwiper.swiper_slides_sec .swiper-slide-next {
    transform: scale(0.79) translateZ(0) translateY(50px) translateX(0px) !important;
  }

  .rtl .swipper_home .mySwiper.swiper_slides_sec .swiper-slide-prev {
    transform: scale(0.79) translateZ(0) translateY(50px) translateX(0px) !important;
  }

  .rtl .swipper_home .mySwiper.swiper_slides_sec .swiper-slide-next {
    transform: scale(0.79) translateZ(0) translateY(50px) translateX(0px) !important;
  }
}

.swipper_home .swiper-slide-active {
  /* transform: scale(1) !important; */
  transform: scale(1.055) !important;
  opacity: 1 !important;
}

/* .swiper-slide-active {
  transform: scale(1) translateZ(0) !important;
} */

.custom-scrollbar .swiper-scrollbar-drag {
  background: #6758b6;
  border-radius: 10px;
  z-index: 100;
  min-width: 20px;
  max-width: 50px;
  cursor: pointer;

  /* transition: all 0.4s ease-out 0s;
  -moz-transition: all 0.4s ease-out 0s;
  -webkit-transition: all 0.4s ease-out 0s;
  -o-transition: all 0.4s ease-out 0s; */
  position: relative;
}

.custom-scrollbar .swiper-scrollbar-drag::after {
  content: "";
  position: absolute;
  /* top: 50%;
  left: 50%;
  transform: translate(-50%, -50%); */
  top: 0%;
  left: 50%;
  transform: translateX(-50%);
  background: #6758b6;
  border-radius: 10px;
  width: 60px;
  height: 100%;
}

.custom-scrollbar.custom-scrollbar-white .swiper-scrollbar-drag {
  background-color: #fff;
}

.custom-scrollbar.custom-scrollbar-white .swiper-scrollbar-drag::after {
  background: #ffffff;
}

/* .custom-scrollbar .swiper-scrollbar-drag:hover {
  background: #836fe4;
} */

.custom-scrollbar.custom-scrollbar-white .swiper-scrollbar-drag:hover {
  background-color: #fff;
}

.custom-scrollbar.custom-scrollbar-white::after {
  background: rgba(255, 255, 255, 0.3) !important;
}

.gm-style-iw-d {
  color: #000;
}

.gm-ui-hover-effect {
  outline: none;
  width: 35px !important;
  height: 35px !important;
}

.gm-ui-hover-effect>span {
  padding: 0;
  margin: 0 !important;
}

/* styles/globals.css */
.parallax-container {
  position: relative;
  will-change: transform;
  overflow: hidden;
}

.tooltip {
  z-index: 99;
  position: absolute;
  background-color: #ececec !important;
  color: #000 !important;
  padding: 25px !important;
  border: 1px solid #c7c7c7;
  border-radius: 5px;
  /* pointer-events: none; */
  width: 260px !important;
  height: auto;
  white-space: inherit;
  opacity: 1 !important;
  font-size: 15px !important;
  color: #3b3064 !important;
  left: 0 !important;
  cursor: pointer !important;
  top: 70px;
}

.tooltip::before {
  top: 50%;
  content: "";
  position: absolute;
  width: 15px;
  height: 15px;
  background-color: #ececec;
  /* Same as tooltip background */
  transform: rotate(45deg);
  top: -7px;
  /* Positioning */
  left: 30px;
  margin-left: -5px;
}

@media screen and (max-width: 1500px) {
  .tooltip {
    padding: 10px !important;
    max-width: 200px !important;
    width: 150% !important;
    left: unset !important;
    right: 0 !important;
    top: 70px;
  }

  .tooltip p {
    font-size: 13px !important;
    line-height: 140%;
  }

  .tooltip::before {
    left: unset;
    margin-left: unset;
    right: 35%;
    margin-right: -5px;
  }
}

.project_swiper .swiper-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80%;
  height: 600px;
}

.project_swiper .swiper-slide {
  display: flex !important;
  justify-content: flex-start !important;
  align-items: center !important;
  transition: transform 0.3s ease-in-out;
}

.project_swiper .swiper-slide img {
  max-width: 100% !important;
  width: auto;
  height: auto !important;
  max-height: 100%;
}

/* Make center slide bigger */
.project_swiper .swiper-slide-active {
  transform: scale(2.4) !important;
}

/* Make side slides smaller */
.project_swiper .swiper-slide {
  opacity: 0.6;
  margin-top: 545px;
  transform: scale(1) !important;
}

.project_swiper .swiper-slide.swiper-slide-prev,
.project_swiper .swiper-slide.swiper-slide-next {
  margin-top: 470px;
}

.rdrDayHovered .rdrNumber {
  border: 0 !important;
}

.testtt {
  color: #836fe4;
}

.project_second_slider_section .project_swiper .swiper-slide {
  margin-top: 445px;
}

.project_second_slider_section .project_swiper .swiper-slide.swiper-slide-prev,
.project_second_slider_section .project_swiper .swiper-slide.swiper-slide-next {
  margin-top: 370px;
}

.project_second_slider_section .project_swiper .swiper-slide-active {
  margin-top: 0;
}

.project_swiper .swiper-slide.swiper-slide-prev {
  left: -50px;
}

.project_swiper .swiper-slide.next-slide {
  justify-content: flex-end !important;
}

.project_swiper .swiper-slide.swiper-slide-next {
  right: -50px;
}

.project_swiper .swiper-slide .project_slide_img {
  border: 1px solid #a097c8 !important;
  padding: 10px;
  width: 150px;
  height: 150px;
  overflow: hidden;
  background-color: #42376f;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Make center slide bigger */
.project_swiper .swiper-slide-active {
  transform: scale(2.2) !important;
}

.project_swiper .swiper-slide-active {
  min-height: 700px;
  opacity: 0.6;
  margin-top: 0;
}

.project_second_slider_section .project_swiper .swiper-slide-active {
  min-height: 500px;
}

.project_swiper .swiper-slide-active .project_slide_img {
  border: none !important;
  padding: 0;
  width: auto;
  height: auto;
  background-color: transparent;
}

.project_slider_btn .swiper-button {
  width: 30px !important;
  height: 30px !important;
  background-color: transparent !important;
  border: none;
  font-size: 0;
  color: #fff;
  cursor: pointer;
  background-repeat: no-repeat;
  background-position: center;
}

.project_slider_btn .swiper-button.next {
  background-image: url(../public/images/next.png) !important;
}

.project_slider_btn .swiper-button.prev {
  background-image: url(../public/images/prev.png) !important;
}

.project_slider_btn.dubai .swiper-button.next {
  background-image: url(../public/images/dubai_slider_arrow.svg) !important;
}

.project_slider_btn.dubai .swiper-button.prev {
  background-image: url(../public/images/dubai_slider_arrow.svg) !important;
  transform: scaleX(-1);
}

.project_slider_btn.mezah_btn .swiper-button.next {
  background-image: url(../public/images/next.png) !important;
}

.project_slider_btn.mezah_btn .swiper-button.prev {
  background-image: url(../public/images/prev.png) !important;
  transform: scaleX(1) !important;
}

.project_swiper.project_swiper2 .swiper-slide .project_slide_img {
  background-color: transparent;
}

/* .projectSlider .swiper-slide img{
     width: auto !important;
     height: auto !important;
} */

.project_partner .swiper-wrapper {
  align-items: center;
}

.project_partner .swiper-slide {
  display: flex;
  /* justify-content: center; */
}

.project_partner .swiper-slide.swiper-slide-active {
  justify-content: flex-start;
}

.white_marquee {
  background-color: #ececec !important;
}

.white_marquee p {
  color: #3b3064 !important;
}

.white_text h3,
.white_text p {
  color: #fff !important;
}

:root {
  --fcc-flip-duration: 0.5s;
  /* transition duration when flip card */
  --fcc-spacing: 6px !important;
  /* space between unit times and separators */
  --fcc-digit-block-width: 24px !important;
  /* width of digit card */
  --fcc-digit-block-height: 50px !important;
  /* height of digit card, highly recommend in even number */
  --fcc-digit-block-radius: 0px !important;
  /* border radius of digit card */
  --fcc-digit-block-spacing: 0px !important;
  /* space between blocks in each unit of time */
  --fcc-digit-font-size: 30px !important;
  /* font size of digit */
  --fcc-digit-color: #6758b6 !important;
  /* color of digit */
  --fcc-label-font-size: 15px;
  /* text-transform: capitalize !important; */
  /* font size of label */
  --fcc-label-color: #ffffff !important;
  /* color of label */
  --fcc-background: linear-gradient(180deg,
      rgba(226, 223, 237, 1) 0%,
      rgba(194, 188, 221, 1) 100%) !important;
  --fcc-divider-color: #6758b6 !important;
  /* color of divider */
  --fcc-divider-height: 1px !important;
  /* height of divider */
  --fcc-separator-size: 0px !important;
  /* size of colon */
  --fcc-separator-color: red;
  /* color of colon */
  --fcc-shadow: none !important;
  --fcc-separator: none !important;
}

.main-logo img {
  display: block;
}

.news_swiper {
  width: 100%;
}

.news_swiper .swiper-pagination.swiper-pagination-bullets {
  left: 0 !important;
  bottom: 0 !important;
  width: unset !important;
}

.more_news_swiper .swiper-pagination-bullet,
.news_swiper .swiper-pagination-bullet {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: #6758b6 !important;
  margin: 0 5px !important;
  height: 10px !important;
  width: 10px !important;
}

.more_news_swiper .swiper-pagination-bullet-active,
.news_swiper .swiper-pagination-bullet-active {
  position: relative;
}

.more_news_swiper .swiper-pagination-bullet-active::after,
.news_swiper .swiper-pagination-bullet-active::after {
  border: 1px solid #6758b6;
  position: absolute;
  content: "";
  border-radius: 50%;

  height: calc(100% + 5px);
  width: calc(100% + 5px);
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  /* background-color: #6758b6 !important; */
}

.custom_tabs .MuiTabs-flexContainer.css-162tvoi,
.custom_tabs .css-1wxkzlj-MuiTabs-flexContainer {
  justify-content: space-between !important;
  /* overflow-y: auto !important; */
  scrollbar-width: none;
  /* border-bottom: 2px solid rgba(0, 0, 0, 0.12) !important; */
}

.custom_tabs .css-1gh31go,
.custom_tabs .css-1gh31go.Mui-disabled,
.custom_tabs .css-1mf9qzl-MuiButtonBase-root-MuiTabScrollButton-root,
.custom_tabs .css-1mf9qzl-MuiButtonBase-root-MuiTabScrollButton-root.Mui-disabled {
  display: none !important;
}

.custom_tabs .MuiButtonBase-root.MuiTab-root.MuiTab-textColorPrimary.css-6p016c,
.custom_tabs .css-1usuzwp-MuiButtonBase-root-MuiTab-root {
  font-family: var(--aller_rg) !important;
  text-transform: capitalize !important;
  /* padding: 20px 30px; */
  /* padding: 30px 0 !important; */
  min-height: 83px !important;
  color: rgba(59, 48, 100, 0.9) !important;
  font-size: 18px !important;
  font-weight: 400 !important;
  letter-spacing: 0 !important;
  flex-grow: 1 !important;
  transition: all 0.3s ease;
}

.custom_tabs.d_flex_tab .MuiButtonBase-root.MuiTab-root.MuiTab-textColorPrimary.css-6p016c,
.custom_tabs.d_flex_tab .css-1usuzwp-MuiButtonBase-root-MuiTab-root {
  flex-direction: row;
}

.rtl .custom_tabs .MuiButtonBase-root.MuiTab-root.MuiTab-textColorPrimary.css-6p016c,
.rtl .custom_tabs .css-1usuzwp-MuiButtonBase-root-MuiTab-root {
  font-family: var(--arbfonts) !important;
}

.custom_tabs .sticky_tab.MuiButtonBase-root.MuiTab-root.MuiTab-textColorPrimary.css-6p016c,
.custom_tabs .sticky_tab.css-1usuzwp-MuiButtonBase-root-MuiTab-root {
  min-height: 60px !important;
}

.custom_tabs .MuiButtonBase-root.MuiTab-root.MuiTab-textColorPrimary.Mui-selected,
.custom_tabs .css-1usuzwp-MuiButtonBase-root-MuiTab-root.Mui-selected {
  color: #3b3064 !important;
  background-color: #e2dfed !important;
}

.custom_tabs .css-1gsv261 {
  border-bottom: 0px solid rgba(0, 0, 0, 0.12) !important;
}

/* .test{} */

.custom_tabs .css-1usuzwp-MuiButtonBase-root-MuiTab-root {
  /* border-bottom: 2px solid rgba(0, 0, 0, 0.12); */
}

.custom_tabs .MuiTabs-indicator.css-jkmkwa,
.custom_tabs .css-1qltlow-MuiTabs-indicator {
  background-color: #675aa7 !important;
}

.custom_tabs .MuiTabPanel-root.css-19kzrtu,
.custom_tabs .css-13xfq8m-MuiTabPanel-root {
  padding: 0 !important;
}

@media screen and (max-width: 1440px) {

  .custom_tabs .MuiButtonBase-root.MuiTab-root.MuiTab-textColorPrimary.css-6p016c,
  .custom_tabs .css-1usuzwp-MuiButtonBase-root-MuiTab-root {
    min-height: 65px !important;

    /* padding: 25px 0 !important; */
  }

  .custom_tabs .sticky_tab.MuiButtonBase-root.MuiTab-root.MuiTab-textColorPrimary.css-6p016c,
  .custom_tabs .sticky_tab.css-1usuzwp-MuiButtonBase-root-MuiTab-root {
    min-height: 55px !important;
  }
}

@media screen and (max-width: 1280px) {

  .custom_tabs .MuiButtonBase-root.MuiTab-root.MuiTab-textColorPrimary.css-6p016c,
  .custom_tabs .css-1usuzwp-MuiButtonBase-root-MuiTab-root {
    padding: 15px 25px;
    font-size: 17px;
  }
}

@media screen and (max-width: 1024px) {

  .more_news_swiper .swiper-pagination-bullet,
  .news_swiper .swiper-pagination-bullet {
    height: 8px !important;
    width: 8px !important;
  }
}

@media screen and (max-width: 820px) {

  .custom_tabs .MuiButtonBase-root.MuiTab-root.MuiTab-textColorPrimary.css-6p016c,
  .custom_tabs .css-1usuzwp-MuiButtonBase-root-MuiTab-root {
    padding: 15px 20px !important;
    font-size: 16px !important;
  }

  .slider_anim.dubai,
  .slidernew.maalem {
    padding-left: 6%;
    padding-right: 6%;
  }
}

@media screen and (max-width: 700px) {

  .custom_tabs .MuiButtonBase-root.MuiTab-root.MuiTab-textColorPrimary.css-6p016c,
  .custom_tabs .css-1usuzwp-MuiButtonBase-root-MuiTab-root {
    padding: 10px 15px !important;
    font-size: 15px !important;
  }

  .news_swiper .swiper-pagination {
    position: unset !important;
    margin-top: 10px;
  }

  .custom_tabs .MuiButtonBase-root.MuiTab-root.MuiTab-textColorPrimary.css-6p016c,
  .custom_tabs .css-1usuzwp-MuiButtonBase-root-MuiTab-root {
    min-height: 50px !important;

    /* padding: 25px 0 !important; */
  }

  .custom_tabs .sticky_tab.MuiButtonBase-root.MuiTab-root.MuiTab-textColorPrimary.css-6p016c,
  .custom_tabs .sticky_tab.css-1usuzwp-MuiButtonBase-root-MuiTab-root {
    min-height: 50px !important;
  }
}

.more_news_swiper .swiper-pagination {
  display: none;
}

@media screen and (max-width: 500px) {

  .custom_tabs .MuiButtonBase-root.MuiTab-root.MuiTab-textColorPrimary.css-6p016c,
  .custom_tabs .css-1usuzwp-MuiButtonBase-root-MuiTab-root {
    padding: 10px 10px !important;
    font-size: 14px !important;
  }

  .more_news_swiper .swiper-pagination {
    display: block;
    position: unset !important;
    margin: auto;
    margin-top: 10px;
  }
}

.dark_bg {
  background-color: #0e0e0e;
}

.ImageBlockSlide .swiper-button-next,
.ImageBlockSlide .swiper-button-prev {
  display: none !important;
}

.ImageBlockSlide {
  margin-bottom: 10px;
}

.css-14dl35y,
.ModalBlockSlideSection .css-4nmryk-MuiBackdrop-root-MuiModal-backdrop {
  /* background-color: #00000085 !important; */
  background: rgba(59, 48, 100, 0.8) !important;
}

.css-14dl35y,
.modal_map_section .css-4nmryk-MuiBackdrop-root-MuiModal-backdrop {
  background: rgba(0, 0, 0, 0.692) !important;
}

.css-14dl35y,
.sliders_popup_sec .css-4nmryk-MuiBackdrop-root-MuiModal-backdrop {
  background: rgba(0, 0, 0, 0.767) !important;
}

.latest_model_swiper.swiper-button-disabled {
  opacity: 0.3;
}

.riyadh_season_swiper {
  overflow: visible !important;
}

.hover_slide_swiper {
  overflow: visible !important;
}

.latest_model_swiper .swiper-pagination-bullets {
  bottom: 8% !important;
}

.latest_model_swiper .swiper-pagination-bullet {
  opacity: 0.4 !important;
  background-color: rgb(255, 255, 255) !important;
}

.latest_model_swiper .swiper-pagination-bullet.swiper-pagination-bullet-active {
  opacity: 1 !important;
}

.custom_dropdown {
  display: flex;
}

.custom_dropdown .css-1l3b12y,
.custom_dropdown .css-1y30b8s,
.custom_dropdown .css-d551zc-MuiSvgIcon-root-MuiSelect-icon,
.custom_dropdown .css-lohd6h-MuiSvgIcon-root-MuiSelect-icon {
  position: relative !important;
  display: none !important;
}

.custom_dropdown .css-98ra8i,
.custom_dropdown .css-sc8y68-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root {
  color: #3b3064;
  font-family: var(--aller_rg);
  border: 0 !important;
}

.custom_dropdown .MuiOutlinedInput-notchedOutline,
.custom_dropdown .css-98ra8i,
.custom_dropdown .css-1ll44ll-MuiOutlinedInput-notchedOutline,
.custom_dropdown .css-sc8y68-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root {
  border: 0 !important;
  border-radius: 0 !important;
}

.custom_dropdown .MuiOutlinedInput-notchedOutline,
.custom_dropdown .css-1ll44ll-MuiOutlinedInput-notchedOutline {
  border-bottom: 1px solid #3b30646e !important;
}

.custom_dropdown.career_dropdown .MuiOutlinedInput-notchedOutline,
.custom_dropdowncareer_dropdown .css-1ll44ll-MuiOutlinedInput-notchedOutline {
  border-bottom: 0px solid #3b306400 !important;
}

/* .career_dropdown::before {
  top: 50%;
  right: 4px;
  width: 13px;
  height: 10px;
  position: absolute;
  content: "";
  z-index: 8;
  background-image: url("/images/drop_down_arrow.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  pointer-events: none;
} */

.black_color.career_dropdown::before {
  filter: invert(1) saturate(0);
}

.custom_dropdown .css-5dycmn {
  font-family: var(--aller_rg);
}

.custom_dropdown .css-mp9f0v.css-mp9f0v.css-mp9f0v,
.custom_dropdown .css-w76bbz-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input {
  padding-left: 2px !important;
  color: #3b3064 !important;
  font-weight: 500 !important;
}

.black_color.custom_dropdown .css-mp9f0v.css-mp9f0v.css-mp9f0v,
.black_color.custom_dropdown .css-w76bbz-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input {
  color: #ffffff !important;
}

.award_stack_swiper .swiper-slide-visible {
  opacity: 1 !important;
  transition: opacity 1s ease-in-out !important;
}

.rtl .news_swiper .swiper-pagination.swiper-pagination-bullets {
  left: unset !important;
  right: 0 !important;
}

@media screen and (max-width: 860px) {

  .custom_dropdown .css-mp9f0v.css-mp9f0v.css-mp9f0v,
  .custom_dropdown .css-w76bbz-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input {
    font-size: 13px;
  }
}

.menu-active {
  overflow: hidden;
}

.project_partner_slider img {
  max-width: 100%;
  height: auto;
}

.disable-hover {
  pointer-events: none;
}

.disable-hover:hover {
  background-color: transparent !important;
  /* Override hover effect */
}

.css-749mnm,
.css-pa188r-MuiModal-root-MuiPopover-root-MuiMenu-root {
  position: absolute !important;
  /* z-index: 500 !important; */
  /* pointer-events: none !important; */
}

.css-d1xm6m,
.css-1tktgsa-MuiPaper-root-MuiPopover-paper-MuiMenu-paper {
  pointer-events: all !important;
}

/* --------location popup section start---------- */

.location_popup_hover .gm-style-iw-chr {
  /* justify-content: flex-end !important; */
  display: none;
}

.location_popup_hover .gm-style-iw-ch {
  visibility: hidden;
  display: none !important;
}

.location_popup_hover .gm-style .gm-style-iw-c {
  padding: 0 !important;
  background-color: rgba(255, 0, 0, 0) !important;
  box-shadow: none !important;
  -webkit-transform: translate3d(-50%, -14%, 0);
  transform: translate3d(-50%, -14%, 0);
  pointer-events: none !important;
}

.location_popup_hover .gm-style .gm-style-iw-d {
  padding: 0 !important;
  overflow: auto !important;
  /* background-color: rgb(255, 206, 206) !important; */
}

.location_popup_hover .gm-style .gm-style-iw-tc {
  display: none !important;
}

.location_popup_hover .location_popup_img {
  height: auto;
  width: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.location_product_sec {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column-reverse;
}

.location_popup_hover .location_popup_img h5 {
  font-size: 16px !important;
  font-family: var(--aller_rg);
}

.location_popup_hover .location_popup_img>img {
  object-fit: contain;
  width: 50px;
  height: auto;
}

/* --------location popup section end---------- */

/* .d_mob_none {
  display: block;
}

@media screen and (max-width: 500px) {
  .d_mob_none {
    display: none;
  }
}

.d_none {
  visibility: hidden;
}

@media screen and (max-width: 500px) {
  .d_none {
    display: block;
  }
} */

.my-masonry-grid {
  display: -webkit-box;
  /* Not needed if autoprefixing */
  display: -ms-flexbox;
  /* Not needed if autoprefixing */
  display: flex;
  margin-left: -5px;
  /* gutter size offset */
  width: 100%;
  /* align-items: end; */
}

.my-masonry-grid_column {
  padding-left: 5px;
  /* gutter size */
  background-clip: padding-box;
}

/* Style your items */
.my-masonry-grid_column>div {
  /* change div to reference your elements you put in <Masonry> */
  margin-bottom: 5px;
}

.my-masonry-grid_column:nth-child(1) {
  width: 40% !important;
}

.my-masonry-grid_column:nth-child(2) {
  width: 20% !important;
}

.my-masonry-grid_column:nth-child(3) {
  width: 40% !important;
}

@media screen and (max-width: 600px) {
  .my-masonry-grid_column:nth-child(1) {
    width: 50% !important;
  }

  .my-masonry-grid_column:nth-child(2) {
    width: 50% !important;
  }

  .my-masonry-grid_column:nth-child(3) {
    width: 50% !important;
  }

  .my-masonry-grid {
    margin-left: -2px;
  }
}

.css-w94xpu,
.css-s2t35c-MuiTabs-scroller {
  overflow-x: auto !important;
}

.rtl .base_font {
  /* font-family: var(--aller_rg) !important; */
  /* font-family: var(--arbfonts) !important; */
  font-family: var(--arbfontsBase) !important;
}
.rtl .base_font1 {
   font-family: var(--arbfonts) !important;
 }

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Hide arrows in Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

.rtl .flipcard_sec .fcc__label {
  font-family: "ArbFonts" !important;
}

.flipcard_sec ._2EBbg {
  color: black !important;
  font-size: 25px !important;
  height: 40px;
  width: 20px;
}

.flipcard_sec ._1GZQ7 {
  border-bottom: 1px solid black !important;
}


@media screen and (max-width: 700px) {
  .flipcard_sec .fcc {
    gap: 5px;
    justify-content: center;
  }

  .flipcard_sec ._2EBbg {
    font-size: 20px;
    height: 40px;
    width: 20px;
  }
}

@media screen and (max-width: 500px) {


  .flipcard_sec ._2EBbg {
    font-size: 18px !important;
    height: 35px;
    width: 18px;
  }
}

img {
  -webkit-user-drag: none;
  user-drag: none;
}

.news_swiper_max_height {
  /* min-height: 375px; */
}


.news_date_section {
  display: flex !important;
  gap: 5px;
}

.news_date_section span {
  padding: 0 !important;
}

.rtl .news_date_section {
  font-family: "ArbFonts" !important;
}

.rtl .news_date_section span {
  padding: 0 !important;
  font-family: "aller" !important;
}

.rtl .news_date_section span:first-child {
  padding: 0 !important;
}