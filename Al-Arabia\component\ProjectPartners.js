import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/scrollbar";
import { Scrollbar, Autoplay, Navigation } from "swiper/modules";
import comon from "@/styles/comon.module.scss";
import Image from "next/image";
import { useRouter } from "next/router";
import parse from 'html-react-parser';
const ProjectPartners = ({ slideView = 6, images }) => {
   // console.log("images", images);
    const router = useRouter();
    return (
        <div>
            <section className={`${comon.pt_65} ${comon.pb_65}`}>
                <div className={`${comon.wrap} ${comon.project_wrap}`}>
                    <div
                        className={`${comon.title_02} ${comon.mb_30}`}
                        data-aos="fade-in"
                        data-aos-duration="1000"
                    >
                        <h3>{parse(images.title)}</h3>
                    </div>

                    <div
                        className={`${comon.p_relative} ${comon.w_100}`}
                        data-aos="fade-in"
                        data-aos-delay="300"
                        data-aos-duration="1000"
                    >
                        <Swiper
                            slidesPerView={3}
                            spaceBetween={10}
                            dir={router.locale == "ar" || router.locale == "rtl" ? "rtl" : "ltr"}
                            speed={1200}
                            navigation={{
                                prevEl: ".custom_prev_4",
                                nextEl: ".custom_next_4",
                            }}
                            modules={[Navigation, Autoplay]}
                            loop={true}
                            autoplay={{
                                delay: 2500,
                                disableOnInteraction: false,
                            }}
                            breakpoints={{
                                580: { slidesPerView: 3, spaceBetween: 20 },
                                768: { slidesPerView: 4, spaceBetween: 40 },
                                1024: { slidesPerView: 6, spaceBetween: 50 },
                                1366: { slidesPerView: slideView, spaceBetween: 50 },
                            }}
                            className="projectSlider"
                        >
                            {images.list.concat(images.list).map((img, index) => (
                                <SwiperSlide key={index}>
                                    <div className={`${comon.project_partners_img} ${comon.project_partners_img_new}  `}>
                                        <Image
                                            src={img.logo}
                                            fill
                                            style={{ objectFit: "cover" }}
                                            alt="Project Partner"
                                            quality={100}
                                        />
                                        <Image
                                            className={`${comon.w_100} ${comon.holder}`}
                                            src="/images/partner_holder.png"
                                            width={672}
                                            height={447}
                                            alt="button image"
                                            style={{
                                                height: "auto",
                                                width: "100%",
                                                display: "block",
                                            }}
                                            quality={100}
                                        />
                                    </div>
                                </SwiperSlide>
                            ))}
                        </Swiper>

                        <span
                            className={`${comon.custom_but}  ${comon.custom_but_prev} custom_prev_4`}
                        >
                            <Image
                                className={`${comon.w_100} ${comon.holder}`}
                                src="/images/arrow_prev.svg"
                                width={7}
                                height={14}
                                alt="button image"
                                quality={100}
                            />
                        </span>
                        <span
                            className={`${comon.custom_but} ${comon.custom_but_next} custom_next_4`}
                        >
                            <Image
                                className={`${comon.w_100} ${comon.holder}`}
                                src="/images/arrow_nxt.svg"
                                width={7}
                                height={14}
                                alt="button image"
                                quality={100}
                            />
                        </span>
                    </div>
                </div>
            </section>
        </div>
    );
};

export default ProjectPartners;