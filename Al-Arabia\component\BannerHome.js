import React, { useEffect, useRef, useState } from "react";
import { gsap } from "gsap";
import Link from "next/link";
import style from "@/styles/banner.module.scss";
import rtl from "@/styles/rtl.module.scss";
import parse from "html-react-parser";
import Image from "next/image";
import { useRouter } from "next/router";
import ArabicRoundText from "./ArabicRoundText";

const BannerHome = ({ bannerData, isOverHeight = false }) => {
  useEffect(() => {
    const bannerTxtBlock = document.querySelector(
      `.${style.banner_txt_block} `
    );
    const videoBlock = document.querySelector(".video_block_home");

    if (bannerTxtBlock && videoBlock) {
      const tl = gsap.timeline();

      // Step 1: Animate the text block
      tl.fromTo(
        bannerTxtBlock,
        { opacity: 0, y: 50 }, // Start fully transparent and slightly below
        { opacity: 1, y: 0, duration: 1.5, ease: "power2.out" } // Fade in and move to position
      );

      // Step 2: Animate the video block after the text animation
      tl.fromTo(
        videoBlock,
        { opacity: 0 }, // Start fully transparent
        { opacity: 1, duration: 2, ease: "power2.out" } // Fade in
      );

      // Step 3: Scale and move the video block
      tl.fromTo(
        videoBlock,
        {
          transform: "translate3d(0px, -10.8364px, 0px) scale3d(0.2, 0.2, 1)",
        },
        {
          transform: "translate3d(0px, 0px, 0px) scale3d(1, 1, 1)",
          duration: 3,
          ease: "power2.out",
        },
        "+=0.5" // Optional delay of 0.5 seconds between fade-in and scale
      );
    }
  }, []);


  const route = useRouter()

  const [isMobile, setIsMobile] = useState(false);
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 767);
    };
    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => {
      window.removeEventListener("resize", checkScreenSize);
    };
  }, []);

  const textRef = useRef(null);

  const rawMessage = bannerData?.banner_text_1?.animation_text || "";
  const isArabic = route.locale !== 'en';

  const repeatCount = 4; // repeat Arabic text to fill path
  const message = isArabic ? rawMessage.repeat(repeatCount) : rawMessage;

  const [fontSize, setFontSize] = useState(14); // you can dynamically calculate this if needed
  const radius = fontSize <= 12 ? 30 : 37; // tweak radius for small fonts
  const rotationStep = message.length > 0 ? 360 / message.length : 0;

  // Only for English – span rotation effect
  useEffect(() => {
    if (!isArabic && textRef.current && message.length > 0) {
      textRef.current.innerHTML = message
        .split("")
        .map((char, i) => {
          if (char === " ") {
            return `<span style="transform: rotate(${i * rotationStep}deg); opacity: 0;">&nbsp;</span>`;
          }
          return `<span style="transform: rotate(${i * rotationStep}deg); display: inline-block;">${char}</span>`;
        })
        .join("");
    }
  }, [isArabic, message, rotationStep]);


  return (
    <section
      className={`${style.banner_main}  ${isOverHeight == true ? style.height_banner : ""
        }  ${rtl.test} `}
    >
      <div className={`${style.wrap} ${style.h_100} ${style.d_flex}`}>
        <div
          className={`${style.mt_auto} ${style.banner_bot_box} ${style.explore_btn} ${rtl.explore_btn}`}
        >
          {/* <Link href={bannerData.banner_text_1.banner_logo_link} className={`${style.banner_logo_box}`}>
            <Image
              src={bannerData.banner_text_1.banner_logo_1}
              width={82}
              height={107}
              alt="Footer Logo"
              style={{
                height: "auto",
                maxWidth: "100%",
                display: "block",
              }}
              quality={100}
            />

            <div className={`${style.text_round}`}>
              <Image
                src={bannerData.banner_text_1.banner_logo_2}
                width={82}
                height={107}
                alt="Footer Logo"
                quality={100}
              />
            </div>
          </Link> */}
          {route.locale == 'en' ?
            <Link
              href={bannerData.banner_text_1.banner_logo_link}
              className={`${style.banner_logo_box} ${style.banner_logo_text_box}`}
            >
              <Image
                src={bannerData.banner_text_1.banner_logo_1}
                width={82}
                height={107}
                alt="Footer Logo"
                style={{
                  height: "auto",
                  maxWidth: "100%",
                  display: "block",
                }}
                quality={100}
              />

              <div className={`${style.text_sec}`}>
                <p ref={textRef}></p>
              </div>
            </Link>

            :

            // <Link
            //   href={bannerData.banner_text_1.banner_logo_link}
            //   className={`${style.banner_logo_box} ${style.banner_logo_text_box}`}
            // >
            //   <Image
            //     src={bannerData.banner_text_1.banner_logo_1}
            //     width={82}
            //     height={107}
            //     alt="Footer Logo"
            //     style={{
            //       height: "auto",
            //       maxWidth: "100%",
            //       display: "block",
            //     }}
            //     quality={100}
            //   />

            //   <div className={style.text_sec}>
            //     <svg viewBox="0 0 100 100" width="100" height="100" style={{ fill: 'white' }}>
            //       <defs>
            //         <path
            //           id="circlePath"
            //           d={`
            //     M 50, 50
            //     m -${radius}, 0
            //     a ${radius},${radius } 0 1,1 ${radius * 2},0
            //     a ${radius},${radius} 0 1,1 -${radius * 2},0
            //   `}
            //         />
            //       </defs>
            //       <text textAnchor="middle">
            //         <textPath
            //           xlinkHref="#circlePath"
            //           startOffset="50%"
            //         >
            //           {message}
            //         </textPath>
            //       </text>
            //     </svg>
            //   </div>
            // </Link>
            <ArabicRoundText bannerData={bannerData} />

          }

        </div>
        <div className={`${style.banner_txt_block} ${rtl.banner_txt_block}`}>
          <h2>
            <span className={`${style.span}`}>
              {bannerData.banner_text_1.banner_title_1}
            </span>
            {parse(bannerData.banner_text_1.banner_title_2)}
          </h2>
        </div>
      </div>

      <div className={`${style.banner_video_main}`}>
        <video
          className={`${style.video_block} video_block video_block_home`}
          autoPlay
          muted
          loop
          playsInline={true}
          width="100%"
          height="auto"
        >
          {isMobile ? (
            <source
              src={bannerData.banner_video.url || bannerData.video.url}
              type="video/mp4"
            />
          ) : (
            <source src={bannerData.banner_video.url} type="video/mp4" />
          )}
        </video>
      </div>
    </section>
  );
};

export default BannerHome;
