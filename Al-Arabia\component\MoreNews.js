"use client"; // Ensures the component is client-side rendered

import React from "react";
import comon from "@/styles/comon.module.scss";
import Link from "next/link";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import rtl from "@/styles/rtl.module.scss";
// Import Swiper styles
import "swiper/css";
import "swiper/css/pagination";
import parse from 'html-react-parser';
// import required modules
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import { useRouter } from "next/router";
const MoreNews = ({related}) => {
    const router = useRouter();
  const {locale} = useRouter();
    const monthNames = {
        en: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
        ar: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
      };

console.log(related)
    return (
        <div className={`${comon.more_news_section} `}>
            <div
                className={`${comon.wrap} ${comon.more_news_container}  ${comon.pt_65} ${comon.pb_65}  `}
            >
                <h3>{locale=="ar" ? 'المزيد من الأخبار' : 'More News'}</h3>

                <div className={comon.more_news_swiper_sec}>
                    <Swiper
                        slidesPerView={1}
                        spaceBetween={20}
                        loop="true"
                        autoplay={{ delay: 3000 }}
                        speed={1000}
                        pagination={{ clickable: true }}
                        navigation={{
                            nextEl: ".right_arrow",
                            prevEl: ".left_arrow",
                        }}
                        dir={locale == "ar" || locale == "rtl" ? "rtl" : "ltr"}

                        className="mySwiper more_news_swiper"
                        modules={[Navigation, Autoplay, Pagination]}

                        breakpoints={{
                            400: {
                                slidesPerView: 1,
                                spaceBetween: 10,

                            },
                            500: {
                                slidesPerView: 3,
                                spaceBetween: 15,

                            },
                            800: {
                                slidesPerView: 4,
                                spaceBetween: 25,

                            }
                        }}
                    >
                        {related?.map((item,index) => {

                            
                    const fixedDate = new Date(item.date);
                    const day = fixedDate.getDate();
                    const year = fixedDate.getFullYear();
                    const month = monthNames[locale === 'ar' ? 'ar' : 'en'][fixedDate.getMonth()];
                                                                
                    const formattedFixedDate = `${year}`;
                            return (                        
                            <SwiperSlide key={index}>
                                <div className={comon.more_news_swiper_card}>
                                    <Link href={`/news/${item.slug}`} className={`${comon.card_img}  `}>
                                        <Image
                                            src={item._embedded["wp:featuredmedia"][0].source_url}
                                            height={500}
                                            width={500}
                                            alt=""
                                            style={{minHeight:'160px', maxHeight:'160px'}}
                                            quality={100}
                                        />

                                        <span  className={`${comon.news_date_block} ${rtl.news_date_block} base_font`}
                                        // style={locale === 'ar' ? { fontFamily: 'Aller' } : {}}
                                        >{formattedFixedDate}</span>
                                    </Link>
                                    <Link href={`/news/${item.slug}`}   >

                                        <h4 className="limitH2"
                                        style={{
                                            display: "-webkit-box",
                                            WebkitLineClamp: 2,
                                            WebkitBoxOrient: "vertical",
                                            overflow: "hidden",
                                          }}
                                        >
                                            {item.title.rendered && parse(item.title.rendered)}
                                        </h4>
                                    </Link>
                                </div>
                            </SwiperSlide>
                        )})}
                    </Swiper >
                    
                    {related.length > 4 && (
                        <>
                    <div className={`${comon.swiper_arrow} ${comon.left} left_arrow`}>
                        <Image
                            src={"/images/swiper_arrow.svg"}
                            height={50}
                            width={50}
                            alt=""
                            quality={100}
                        />
                    </div>

                    <div className={`${comon.swiper_arrow} ${comon.right} right_arrow `}>
                        <Image
                            src={"/images/swiper_arrow.svg"}
                            height={50}
                            width={50}
                            alt=""
                            quality={100}
                        />
                    </div>
                    </>
                    )}
                </div >
            </div >
        </div >
    );
};

export default MoreNews;
