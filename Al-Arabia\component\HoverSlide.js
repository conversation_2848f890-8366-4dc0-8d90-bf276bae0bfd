import React, { useEffect, useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import Image from "next/image";
import "swiper/css";
import "swiper/css/pagination";
import rtl from "@/styles/rtl.module.scss";
import "swiper/css/navigation";
import AOS from "aos";
import "aos/dist/aos.css";
import { useRouter } from "next/router";
import {
  Scrollbar,
  Autoplay,
  Navigation,
  Pagination,
  FreeMode,
  Thumbs,
} from "swiper/modules";
import { Box, Modal } from "@mui/material";
import style from "@/styles/HoverSlide.module.scss";
import comon from "@/styles/comon.module.scss";
import parse from "html-react-parser";
import {
  GoogleMap,
  Marker,
  InfoWindow,
  useLoadScript,
} from "@react-google-maps/api";
require("dotenv").config();

const mapStyles = [
  {
    featureType: "administrative",
    elementType: "geometry",
    stylers: [{ color: "#d3d3d3" }],
  },
  {
    featureType: "administrative",
    elementType: "labels.text.fill",
    stylers: [{ color: "#aaa5d8" }],
    stylers: [{ visibility: "off" }],
  },
  {
    featureType: "administrative",
    elementType: "labels.text.stroke",
    stylers: [{ color: "#4a4671" }],
    stylers: [{ visibility: "off" }],
  },
  {
    featureType: "landscape",
    elementType: "all",
    stylers: [{ color: "#B8BAB9" }],
  },
  {
    featureType: "poi",
    elementType: "all",
    stylers: [{ visibility: "off" }],
  },
  {
    featureType: "road",
    elementType: "all",
    stylers: [{ saturation: -100 }, { lightness: 45 }],
  },
  {
    featureType: "road",
    elementType: "geometry",
    stylers: [{ color: "#d3d3d3" }],
  },
  {
    featureType: "road",
    elementType: "labels.icon",
    stylers: [
      { visibility: "off" },
      { hue: "#747876" },
      { saturation: "30" },
      { lightness: "-17" },
    ],
  },
  {
    featureType: "road",
    elementType: "labels.text",
    stylers: [{ visibility: "off" }],
  },
  {
    featureType: "transit",
    elementType: "all",
    stylers: [{ visibility: "off" }],
  },
  {
    featureType: "water",
    elementType: "geometry.fill",
    stylers: [{ color: "#8b8a8a" }],
  },
  {
    featureType: "water",
    elementType: "geometry.stroke",
    stylers: [{ color: "#747876" }, { visibility: "on" }],
  },
  {
    featureType: "water",
    elementType: "labels.text",
    stylers: [{ visibility: "off" }],
  },
];
const containerStyle = {
  width: "100%",
  height: "100%",
};

const locations = {
  saudiArabia: [
    { name: "Riyadh", coords: { lat: 24.7136, lng: 46.6753 } },
    { name: "Jeddah", coords: { lat: 21.4858, lng: 39.1925 } },
    { name: "Dammam", coords: { lat: 26.3927, lng: 49.9777 } },
    { name: "Makkah", coords: { lat: 21.3891, lng: 39.8579 } },
    { name: "Taif", coords: { lat: 21.4373, lng: 40.5127 } },
    { name: "Madinah", coords: { lat: 24.5247, lng: 39.5692 } },
    { name: "Tabuk", coords: { lat: 28.3834, lng: 36.5662 } },
    { name: "Buraydah", coords: { lat: 26.325, lng: 43.9748 } },
    { name: "Al Jubail", coords: { lat: 27.0046, lng: 49.6603 } },
    { name: "Dhahran", coords: { lat: 26.2361, lng: 50.0393 } },
  ],
  uae: [
    { name: "Dubai", coords: { lat: 25.276987, lng: 55.296249 } },
    { name: "Abu Dhabi", coords: { lat: 24.453884, lng: 54.377344 } },
    { name: "Sharjah", coords: { lat: 25.3463, lng: 55.4209 } },
  ],
  Egypt: [
    { name: "Pyramids Of Giza", coords: { lat: 29.9792, lng: 31.1342 } },
    { name: "Great Sphinx of Giza", coords: { lat: 29.9753, lng: 31.1376 } },
    { name: "Siwa Oasis", coords: { lat: 29.2032, lng: 25.5199 } },
    { name: "Cairo", coords: { lat: 30.0478, lng: 31.2336 } },
  ],
};

const HoverSlide = ({
  popUpAction = true,
  season,
  imageSlides,
  PopUpMap = false,
  extra_expand = false,
  textOverlay = false,
  extra_width_arrows = false,
  black_bg_arrows = false,
  extra_width_arrows_top_space = false,
}) => {
  const [open, setOpen] = useState(false);
  const [thumbsSwiper, setThumbsSwiper] = useState(null);
  const [isClient, setIsClient] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  useEffect(() => {
    setIsClient(true);
    AOS.init();
  }, []);
  const { locale } = useRouter();

  const [fetchedData, setFetchedData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  useEffect(() => {
    const fetchRearrangedData = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/${locale === "ar" ? "map-locations-ar" : "map-locations"
          }`
        );
        const json = await response.json();

        if (json.success && Array.isArray(json.locations)) {
          setFetchedData(json.locations);
        } else {
          setError("Map data is missing or invalid.");
        }
      } catch (err) {
        setError("Failed to load map data.");
      } finally {
        setLoading(false);
      }
    };
    fetchRearrangedData();
  }, [season, locale]);

  const [selectedCountry, setSelectedCountry] = useState("saudiArabia");
  const [activeLocations, setActiveLocations] = useState();
  const [center, setCenter] = useState();
  const [matchedCoords, setMatchedCoords] = useState([]);
  const [matchedCity, setMatchedCity] = useState("");
  useEffect(() => {
    if (PopUpMap && matchedCoords.length > 0) {
      const formattedLocations = matchedCoords.map((loc, index) => ({
        name: `Location ${index + 1}`,
        coords: {
          lat: parseFloat(loc.lat),
          lng: parseFloat(loc.long),
        },
      }));
      setActiveLocations(formattedLocations);
      setCenter(formattedLocations[0].coords);
    }
  }, [PopUpMap, matchedCoords]);

  const { isLoaded } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY,
  });
  const [zoom, setZoom] = useState(13);
  const [activeInfoWindow, setActiveInfoWindow] = useState(null);
  const mapRef = useRef(null);
  const [customMarkerIcon, setCustomMarkerIcon] = useState(null);
  const scrollRef = useRef(null);

  const handleOpen = (item) => {
    setSelectedItem(item);
    setOpen(true);
    if (PopUpMap) {
      var x = item.popup_data.locations.map((loc) => ({
        name: loc.location_name.trim() || "Unknown",
        coords: {
          lat: parseFloat(loc.lat),
          lng: parseFloat(loc.long),
        },
      }));
      setActiveLocations(x);
      setCenter(x[0].coords);
      setZoom(13);
      if (mapRef.current) {
        mapRef.current.panTo(x[0].coords);
      }
    }
  };

  const handleClose = () => {
    setOpen(false);
    setSelectedItem(null);
  };

  useEffect(() => {
    if (isLoaded && typeof window !== "undefined" && window.google) {
      setCustomMarkerIcon({
        url: "/images/location_pin11.svg",
        scaledSize: new window.google.maps.Size(50, 50),
        origin: new window.google.maps.Point(0, 0),
        anchor: new window.google.maps.Point(20, 20),
      });
    }
  }, [isLoaded]);

  const handleCountryClick = (country) => {
    setSelectedCountry(country);
    const countryLocations = locations[country];
    setActiveLocations(countryLocations);
    const firstLocation = countryLocations[0].coords;
    animateMapTo(firstLocation, 14);
    setActiveInfoWindow(null);
  };

  const handleLocationClick = (location) => {
    const coords = {
      lat: location.lat,
      lng: location.long,
    };
    setActiveInfoWindow("tuwaiq");
    if (mapRef.current) {
      mapRef.current.setZoom(20);
      mapRef.current.panTo(coords);
    }
  };

  const animateMapTo = (coords, zoomLevel) => {
    if (mapRef.current) {
      mapRef.current.panTo(coords);
      mapRef.current.setZoom(zoomLevel);
    }
    setCenter(coords);
    setZoom(zoomLevel);
  };

  useEffect(() => {
    if (!selectedItem?.popup_data?.text || !Array.isArray(fetchedData)) return;
    const normalizeText = (str, locale) =>
      (str || "")
        .replace(
          locale === "ar" ? /\s*من\s+العربية\s*/g : /by\s+al\s*arabia/gi,
          ""
        )
        .trim()
        .toLowerCase();

    const selectedText = normalizeText(selectedItem.popup_data.text, locale);
    for (const country of fetchedData) {
      for (const city of country.cities || []) {
        const filteredCoords = city.co_ords?.filter((coord) => {
          const normalizedName = normalizeText(coord.name, locale);
          return normalizedName === selectedText;
        });
        if (filteredCoords && filteredCoords.length > 0) {
          setMatchedCity(city.city_name);
          setMatchedCoords(filteredCoords);
          return;
        }
      }
    }
    setMatchedCity("");
    setMatchedCoords([]);
  }, [selectedItem, fetchedData, locale]);

  if (!isLoaded) return <div>Loading...</div>;
  if (!isClient) return null;

  return (
    <div>
      <div
        className={`${extra_width_arrows == false && comon.wrap} ${comon.mt_40
          }`}
      >
        <Swiper
          spaceBetween={15}
          slidesPerView={1.5}
          speed={2000}
          lazy={false}
          pagination={{ clickable: true }}
          loop={true}
          navigation={{
            prevEl: ".prev_btn1",
            nextEl: ".next_btn1",
          }}
          className="mySwiper hover_slide_swiper"
          modules={[Autoplay, Navigation]}
          breakpoints={{
            769: { slidesPerView: 4 },
            1024: { slidesPerView: 4, spaceBetween: 20 },
            1601: { slidesPerView: extra_width_arrows == true ? 4.5 : 3.5 },
          }}
        >
          {[...season, ...season].map((item, index) => (
            <SwiperSlide
              key={index}
              onClick={() => handleOpen(item)}
              className={
                `${style.season_image_item} ${style.season_image_card} ` +
                `${extra_expand ? style.extra_expand + " " : ""}` +
                `${popUpAction && PopUpMap ? style.cursor_pointer + " " : ""}` +
                `${style.season_image_item_mob}`
              }
            >
              <div className={`${style.season_image} ${rtl.season_image}`}>
                <Image
                  src={item.image}
                  fill
                  style={{ objectFit: "cover" }}
                  alt={item.text}
                  quality={100}
                />
              </div>
              {item.text && (
                <div
                  className={`${style.text}  ${textOverlay == true && style.text_overlay
                    }`}
                >
                  <h3 className={`${style.arabic_format}`}>{parse(item.text)}</h3>
                </div>
              )}
            </SwiperSlide>
          ))}
          {extra_width_arrows == true && (
            <div
              className={`${black_bg_arrows == true && "black_bg"} ${extra_width_arrows_top_space == true && "padding__top_space"
                }   thumb_swiper_slide_btn top_adjusted hover_slide_btn`}
            >
              <div className="prev_btn prev_btn1 swiper_btn ">
                <Image
                  src="/images/next_icon1.svg"
                  height={30}
                  width={30}
                  alt="next"
                  quality={100}
                />
              </div>
              <div className="next_btn next_btn1 swiper_btn">
                <Image
                  src="/images/next_icon1.svg"
                  height={30}
                  width={30}
                  alt="prev"
                  quality={100}
                />
              </div>
            </div>
          )}
        </Swiper>
      </div>
      {/* Modal Sliders*/}
      {popUpAction && selectedItem?.gallerys.length && (
        <Modal
          className="ModalBlockSlideSection"
          open={open}
          data-lenis-prevent="true"
          onClose={() => {
            setThumbsSwiper(false);
            setOpen(false);
          }}
        >
          <Box className={comon.popup_container}>
            <button
              className={comon.popup_close}
              onClick={() => {
                setThumbsSwiper(false);
                setOpen(false);
              }}
            >
              <Image
                src="/images/close_btn.svg"
                height={30}
                width={30}
                alt="Close"
                quality={100}
              />
            </button>
            <Swiper
              spaceBetween={10}
              onSwiper={setThumbsSwiper}
              navigation={{
                prevEl: ".prev_btn",
                nextEl: ".next_btn",
              }}
              thumbs={{
                swiper:
                  thumbsSwiper && !thumbsSwiper.destroyed ? thumbsSwiper : null,
              }}
              modules={[FreeMode, Navigation, Thumbs]}
              className="mySwiper2 ImageBlockSlide"
            >
              {selectedItem &&
                selectedItem.gallerys?.map((url, index1) => (
                  <SwiperSlide key={`${index1}-${index1}`}>
                    <div className={comon.swiper_image}>
                      <img src={url} alt={`Slide ${index1 + 1}`} />
                    </div>
                  </SwiperSlide>
                ))}
              <div className="thumb_swiper_slide_btn">
                <div className="prev_btn swiper_btn ">
                  <Image
                    src="/images/next_icon1.svg"
                    height={30}
                    width={30}
                    alt="next"
                    quality={100}
                  />
                </div>
                <div className="next_btn swiper_btn">
                  <Image
                    src="/images/next_icon1.svg"
                    height={30}
                    width={30}
                    alt="prev"
                    quality={100}
                  />
                </div>
              </div>
            </Swiper>
            <Swiper
              onSwiper={setThumbsSwiper}
              spaceBetween={10}
              slidesPerView={3}
              freeMode
              watchSlidesProgress
              modules={[FreeMode, Navigation, Thumbs]}
              className="mySwiper ImageBlockSlideThumbs"
              breakpoints={{
                500: { slidesPerView: 4 },
              }}
            >
              {selectedItem &&
                selectedItem.gallerys.map((url, index1) => (
                  <SwiperSlide key={`${index1}-${index1}`}>
                    <div className={comon.swiper_image}>
                      <img src={url} alt={`Slide ${index1 + 1}`} />
                    </div>
                  </SwiperSlide>
                ))}
            </Swiper>
          </Box>
        </Modal>
      )}
      {/* Modal Map*/}
      {PopUpMap && (
        <Modal
          className="modal_map_section"
          open={open}
          data-lenis-prevent="true"
          onClose={handleClose}
        >
          <Box className={`${comon.popup_container} ${comon.map_pop_up}`}>
            <button
              className={`${comon.popup_close} ${comon.small_close}`}
              onClick={handleClose}
            >
              <Image
                src="/images/close_btn.svg"
                height={30}
                width={30}
                alt="Close"
                quality={100}
              />
            </button>
            <div className={style.map_section}>
              <div
                className={`${comon.zoom_btn_sec} ${comon.black_theme_map_btn}`}
              >
                <button
                  onClick={() => {
                    if (mapRef.current) {
                      const currentZoom = mapRef.current.getZoom();
                      mapRef.current.setZoom(currentZoom + 1);
                    }
                  }}
                >
                  <div className={comon.zoom_img}>
                    <Image src="/images/zoom-in.svg" height={30} width={30} alt="Zoom In" />
                  </div>
                </button>

                <button
                  onClick={() => {
                    if (mapRef.current) {
                      const currentZoom = mapRef.current.getZoom();
                      mapRef.current.setZoom(currentZoom - 1);
                    }
                  }}
                >
                  <div className={comon.zoom_img}>
                    <Image src="/images/zoom-out.svg" height={30} width={30} alt="Zoom Out" />
                  </div>
                </button>
              </div>
              <div
                className={`${comon.p_relative} ${comon.h_100} ${style.map_block_04}`}
                ref={scrollRef}
              >
                {selectedItem?.popup_data.locations && (
                  <GoogleMap
                    mapContainerStyle={containerStyle}
                    center={center}
                    zoom={zoom}
                    options={{
                      styles: mapStyles,
                      disableDefaultUI: true,
                      gestureHandling: 'greedy',
                      scrollwheel: false
                    }}
                    onLoad={(map) => (mapRef.current = map)}
                  >
                    {matchedCoords &&
                      matchedCoords.map((location, i) => {
                        if (!location.lat || !location.long) return null;
                        const coords = {
                          lat: location.lat,
                          lng: location.long,
                        };
                        if (coords) {
                          return (
                            <Marker
                              key={`marker-${i}`}
                              position={coords}
                              icon={customMarkerIcon}
                              onClick={() => handleLocationClick(location)}
                              onMouseOver={() =>
                                setActiveInfoWindow(
                                  `lat:${coords.lat}, lng: ${coords.lng}`
                                )
                              }
                              onMouseOut={() => setActiveInfoWindow(null)}
                            ></Marker>
                          );
                        }
                      })}
                  </GoogleMap>
                )}
                {selectedItem && (
                  <div className={style.map_content}>
                    <div className={style.content_img}>
                      <Image
                        src={selectedItem.popup_data.popup_image}
                        height={270}
                        width={700}
                        alt=""
                        style={{ maxHeight: "240px" }}
                        quality={100}
                      />
                    </div>
                    <div className={style.content_text}>
                      <span>{selectedItem.popup_data.sub_title}</span>
                      <h4>{parse(selectedItem.popup_data.text)}</h4>
                      <>{parse(selectedItem.popup_data.description)}</>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Box>
        </Modal>
      )}
    </div>
  );
};

export default HoverSlide;
