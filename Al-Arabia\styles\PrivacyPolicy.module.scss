@import "variable", "base", "mixin";

.privacy_policy_container {
    background-color: white;

    .privacy_policy_section {
        width: 100%;

        .content_sec {
            h2 {
                margin: 15px 0 10px 0;
                // color: #3b3064;
                color: #6659b0;

                font-size: 30px;

                @include max(1366) {
                    font-size: 30px;
                }

                @include max(1024) {
                    font-size: 27px;
                    margin: 10px 0;
                }

                @include max(700) {
                    font-size: 23px;
                }

                @include max(500) {
                    font-size: 20px;
                }
            }

            h3 {
                margin: 15px 0 10px 0;

                color: #6659b0;

                font-size: 26px;

                @include max(1366) {
                    font-size: 23px;
                }

                @include max(1024) {
                    font-size: 20px;
                    margin: 10px 0;
                }

                @include max(700) {
                    font-size: 18px;
                }

                @include max(500) {
                    font-size: 16px;
                    margin: 5px 0;

                }
            }

            p {
                padding-bottom: 25px;

                @include max(1024) {
                    padding-bottom: 10px;
                }
            }

            ul {
                li {
                    // color: #3b3064;
                    // color: #6659b0;
                    color: rgb(105, 102, 135);
                    font-size: 14px;
                    line-height: 150%;

                    padding-bottom: 10px;

                    @include max(1024) {
                        font-size: 13px;
                    }
                }
            }

            table,
            th,
            td {
                border: 1px solid #bbbbbb;
                border-collapse: collapse;
            }

            table {
                max-width: 1100px;
                width: 100%;
                margin: 30px auto;

                tbody {
                    tr {
                        &:first-child {
                            td {
                                background-color: #6659b0;
                                color: rgb(255, 255, 255);
                                vertical-align: middle;
                            }
                        }

                        td {
                            // background-color: white;
                            // border: 1px solid rebeccapurple;
                            padding: 13px 10px;
                            font-size: 14px;
                            font-weight: 400;
                            text-align: center;
                            line-height: 150%;
                            color: black;
                            vertical-align: top;

                            @include max(1024) {
                                padding: 10px 5px;
                                font-size: 13px;
                            }

                            strong {

                                @include max(1024) {
                                    font-weight: normal;
                                }
                            }
                        }
                    }
                }

                @include max(1280) {
                    margin: 20px auto;
                }

                @include max(700) {
                    margin: 10px auto;
                }
            }
        }
    }
}