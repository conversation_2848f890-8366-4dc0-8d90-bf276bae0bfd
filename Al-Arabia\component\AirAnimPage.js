import React, { useEffect, useRef, useState } from "react";
import { gsap } from "gsap";
import { MotionPathPlugin } from "gsap/dist/MotionPathPlugin";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";
import style from "@/styles/AirAnimPageNew.module.scss";

gsap.registerPlugin(MotionPathPlugin, ScrollTrigger);

const AirAnimPage = () => {
    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
        // Function to check if the screen is mobile size
        const checkScreenSize = () => {
            setIsMobile(window.innerWidth <= 500); // Mobile breakpoint
        };

        // Check screen size on load
        checkScreenSize();

        // Add event listener for resize
        window.addEventListener("resize", checkScreenSize);

        // Cleanup event listener on unmount
        return () => {
            window.removeEventListener("resize", checkScreenSize);
        };
    }, []);

    const airplaneRef = useRef(null);
    const containerRef1 = useRef(null);
    const pathRef = useRef(null);
    const pathRef1 = useRef(null);

    console.log(" pathRef.current", pathRef.current);
    console.log(" pathRef1.current", pathRef1.current);

    useEffect(() => {
        const path = pathRef.current;
        const path1 = pathRef1.current;
        const airplane = airplaneRef.current;
        const container = containerRef1.current;
        const section = document.getElementById("motion_start");

        if (!path && !path1) {
            console.error("No valid path found!", { path, path1 });
            return;
        }

        if (!airplane || !container) {
            console.error("Missing refs:", { airplane, container });
            return;
        }

        const mm = gsap.matchMedia();

        mm.add("(min-width: 601px)", () => {
            gsap.set(airplane, { rotate: "+=225", scale: 1 });

            gsap.to(airplane, {
                motionPath: {
                    path,
                    align: path,
                    alignOrigin: [0.5, 0.5],
                    autoRotate: true,
                    start: 0,
                    end: 1,
                },
                ease: "power1.inOut",
                scrollTrigger: {
                    trigger: section,
                    start: "top 120%",
                    end: "bottom -110%",
                    scrub: 1,
                    markers: false,
                },

                onStart: () => {
                    gsap.set(airplane, { opacity: 1 });
                },
                onUpdate: () => {
                    gsap.set(airplane, { rotate: "+=225" });
                },
            });
        });
        mm.add("screen and (max-width: 768px) and (min-width: 601px)", () => {
            gsap.set(airplane, { rotate: "+=225", scale: 1.7 });

            gsap.to(airplane, {
                motionPath: {
                    path,
                    align: path,
                    alignOrigin: [0.5, 0.5],
                    autoRotate: true,
                    start: 0,
                    end: 1,
                },
                ease: "power1.inOut",
                scrollTrigger: {
                    trigger: section,
                    start: "top 120%",
                    end: "bottom -110%",
                    scrub: 1,
                    markers: false,
                },

                onStart: () => {
                    gsap.set(airplane, { opacity: 1 });
                },
                onUpdate: () => {
                    gsap.set(airplane, { rotate: "+=225" });
                },
            });
        });

        mm.add("screen and (max-width: 600px) and (min-width: 501px)", () => {
            gsap.set(airplane, { rotate: "+=225", scale: 2 }); // Scale down for smaller screens

            gsap.to(airplane, {
                motionPath: {
                    path,
                    align: path,
                    alignOrigin: [0.5, 0.5],
                    autoRotate: true,
                    start: 0,
                    end: 1,
                },
                ease: "power1.inOut",
                scrollTrigger: {
                    trigger: section,
                    start: "top 75%",
                    end: "bottom 100%",
                    scrub: 1,
                    markers: false,
                },

                onStart: () => {
                    gsap.set(airplane, { opacity: 1 });
                },
                onUpdate: () => {
                    gsap.set(airplane, { rotate: "+=225" });
                },
            });
        });

        mm.add(" screen and (max-width: 500px) and (min-width: 400px)   ", () => {
            gsap.set(airplane, { rotate: "+=225", scale: 2.5 }); // Scale down for smaller screens

            gsap.to(airplane, {
                motionPath: {
                    path: path,
                    align: path,
                    alignOrigin: [0.5, 0.5],
                    autoRotate: true,
                },
                ease: "power1.inOut",
                scrollTrigger: {
                    trigger: section,
                    start: "top 55%",
                    end: "bottom 170%",
                    scrub: 1,
                },
                onStart: () => {
                    gsap.set(airplane, { opacity: 1 });
                },
                onUpdate: () => {
                    gsap.set(airplane, { rotate: "+=225" });
                },
            });
        });
        mm.add("(max-width: 399px )", () => {
            gsap.set(airplane, { rotate: "+=225", scale: 2.5 }); // Scale down for smaller screens

            gsap.to(airplane, {
                motionPath: {
                    path: path,
                    align: path,
                    alignOrigin: [0.5, 0.5],
                    autoRotate: true,
                },
                ease: "power1.inOut",
                scrollTrigger: {
                    trigger: section,
                    start: "top 50%",
                    end: "bottom 170%",
                    scrub: 1,
                },
                onStart: () => {
                    gsap.set(airplane, { opacity: 1 });
                },
                onUpdate: () => {
                    gsap.set(airplane, { rotate: "+=225" });
                },
            });
        });

        return () => {
            mm.revert(); // Cleanup on unmount
        };
    }, [isMobile]);

    return (
        <div>
            <div className={`${style.airplane_wrap}`} ref={containerRef1}>
                {isMobile == false ? (
                    <svg
                        width="1700"
                        height="4758"
                        viewBox="0 0 1700 4758"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <g ref={airplaneRef}>
                            <path
                                class="st0"
                                d="M32,33.2c-0.1-0.2-0.2-0.4-0.2-0.5c0-0.1-0.1-0.2-0.1-0.3c-0.2-0.5-0.5-1.1-0.7-1.6c-0.1-0.1-0.1-0.2-0.2-0.3
		c-0.6-1.3-1.2-2.7-1.8-4.1c-1-2.2-2-4.5-3-6.7C23.6,14.4,21.4,9.2,19.1,4c-0.7,0.3-1.2,0.8-1.7,1.3c-0.1,0.1-0.2,0.2-0.3,0.3
		c-0.6,0.6-1,1.1-1.2,1.9c0.1,1.4,0.5,2.8,0.9,4.2c0.1,0.2,0.1,0.4,0.2,0.7c0.2,0.6,0.3,1.2,0.5,1.7c0.2,0.6,0.3,1.2,0.5,1.8
		c0.3,1.2,0.6,2.3,0.9,3.5c0.1,0,0.2,0,0.3-0.1c0.1,0,0.2-0.1,0.4-0.1c0.2,0,0.2,0,0.4-0.1c1.1-0.2,2.2-0.1,3.2,0.4
		c1,0.7,1.7,1.4,2,2.6c0.2,1.6,0.1,2.8-0.9,4c-0.5,0.6-1,1.1-1.6,1.6c-0.1,0.1-0.2,0.1-0.2,0.2c-0.2,0.2-0.4,0.4-0.7,0.6
		c-0.4,0.5-0.3,0.7-0.2,1.3c0.1,0.5,0.2,1,0.3,1.5c0,0.1,0.1,0.2,0.1,0.3c0.1,0.4,0.2,0.7,0.3,1.1c0.1,0.3,0.1,0.5,0.2,0.8
		c0.1,0.5,0.3,1.1,0.4,1.6c0.2,0.7,0.3,1.4,0.5,2c0.1,0.5,0.3,1,0.4,1.6c0.1,0.3,0.1,0.5,0.2,0.8c0.1,0.3,0.2,0.7,0.3,1
		c0,0.2,0,0.2,0.1,0.3c0.1,0.5,0.2,0.7,0.5,1.1c1-0.9,2-1.8,3-2.7c1-0.9,1.9-1.8,2.9-2.6c0.1-0.1,0.2-0.2,0.4-0.3
		c0.2-0.2,0.5-0.4,0.7-0.6c0.1-0.1,0.2-0.2,0.3-0.3c0.1-0.1,0.2-0.2,0.3-0.2c0.2-0.3,0.2-0.3,0.2-0.7C32.4,34.1,32.2,33.6,32,33.2z"
                            />
                            <path
                                class="st0"
                                d="M58.8,13.3c0.1-0.1,0.3-0.2,0.4-0.3c0.2-0.1,0.3-0.2,0.5-0.3c0.2-0.1,0.3-0.2,0.5-0.3c0.4-0.3,0.8-0.5,1.1-0.8
		c-0.7-1.3-1.4-2.5-2.2-3.8c-0.1-0.2-0.2-0.4-0.3-0.5c-0.7-1.2-1.5-2.4-2.2-3.6c-0.1,0.1-0.2,0.1-0.2,0.2c-0.2,0.2-0.4,0.3-0.6,0.5
		C55.3,4.7,55,5,54.9,5.5c0,2.6,1.4,6.2,2.8,8.2C58.2,13.8,58.4,13.5,58.8,13.3z"
                            />
                            <path
                                class="st0"
                                d="M65.9,21.5c0.3,0.1,0.3,0.1,0.5,0.2c0.3,0.1,0.3,0.1,0.5,0.2c0.2,0.1,0.4,0.1,0.5,0.2c4,1.9,4,1.9,7.5,0.2
		c-0.4-0.4-0.8-0.7-1.2-0.9c-0.2-0.1-0.3-0.2-0.5-0.3c-0.2-0.1-0.3-0.2-0.5-0.3c-0.3-0.2-0.7-0.4-1-0.6c-0.1-0.1-0.2-0.1-0.3-0.2
		c-0.9-0.6-1.9-1.1-2.8-1.7c-0.2-0.1-0.2-0.1-0.5-0.3c-0.4-0.2-0.7-0.4-1.1-0.6c-0.5,0.6-1,1.3-1.5,2c-0.1,0.1-0.2,0.2-0.2,0.3
		c-0.1,0.1-0.1,0.2-0.2,0.3c-0.1,0.1-0.1,0.2-0.2,0.3c-0.2,0.3-0.2,0.3,0,0.7C65.4,21.3,65.4,21.3,65.9,21.5z"
                            />
                            <path
                                class="st0"
                                d="M21.9,24.9c0.1-0.1,0.2-0.2,0.3-0.3c0.5-0.5,0.4-0.9,0.4-1.6c-0.1-0.5-0.3-0.8-0.7-1.1
		c-0.5-0.3-0.9-0.4-1.5-0.3c-0.9,0.5-1.5,1.2-2.2,1.9c-0.1,0.1-0.2,0.2-0.4,0.4c-0.4,0.4-0.8,0.8-1.2,1.2c-0.3,0.3-0.5,0.5-0.8,0.8
		c-0.7,0.7-1.3,1.3-2,2c0.1,0.2,0.3,0.3,0.4,0.5c0.1,0.1,0.2,0.2,0.2,0.3c0.3,0.4,0.6,0.7,1,1c0.1,0.1,0.2,0.2,0.4,0.4
		c0.4,0.3,0.5,0.4,0.9,0c0.1-0.1,0.2-0.2,0.3-0.3c0.1-0.1,0.2-0.2,0.4-0.4c0.1-0.1,0.3-0.3,0.4-0.4c0.1-0.1,0.3-0.3,0.4-0.4
		c0.3-0.3,0.6-0.6,0.8-0.8c0.4-0.4,0.9-0.9,1.3-1.3c0.3-0.3,0.5-0.5,0.8-0.8c0.1-0.1,0.3-0.3,0.4-0.4C21.7,25.1,21.8,25,21.9,24.9z"
                            />
                            <path
                                class="st0"
                                d="M64.1,54.9c-0.8-0.4-1.7-0.7-2.5-1.1c-0.1-0.1-0.3-0.1-0.4-0.2c-0.5-0.2-1.1-0.5-1.6-0.7
		c-1.7-0.7-3.3-1.5-5-2.2c-1.1-0.5-2.2-1-3.3-1.4c-0.8-0.4-1.7-0.7-2.5-1.1c-0.6-0.2-1.1-0.5-1.7-0.7c-0.3-0.1-0.5-0.2-0.8-0.4
		c-0.4-0.2-0.7-0.3-1.1-0.5c-0.1,0-0.2-0.1-0.3-0.1c-0.4-0.2-0.7-0.3-1.2-0.3c-0.5,0.2-0.8,0.7-1.1,1.1c-0.1,0.1-0.2,0.2-0.2,0.3
		c-0.5,0.5-1,1.1-1.5,1.7c-0.4,0.5-0.9,1-1.4,1.5c-0.9,1-0.9,1-1.9,2c-0.1,0.1-0.2,0.2-0.3,0.3c-0.3,0.3-0.5,0.7-0.7,1.1
		c2.3,0.7,4.7,1.3,7,1.9c0.4,0.1,0.9,0.2,1.3,0.3c0.5,0.1,1.1,0.3,1.6,0.4c0.2,0.1,0.4,0.1,0.6,0.2c0.3,0.1,0.6,0.1,0.9,0.2
		c0.2,0,0.3,0.1,0.5,0.1c0.5,0.1,0.8,0.1,1.2-0.1c0.3-0.2,0.3-0.2,0.6-0.6c0.2-0.2,0.2-0.2,0.3-0.3c0.2-0.2,0.4-0.5,0.7-0.7
		c1.1-1.2,2.2-1.9,3.9-2c1.2,0,2.1,0.3,3.1,1.1c1,1.1,1.3,2.1,1.3,3.6c-0.1,0.6-0.2,1.1-0.4,1.7c1.4,0.4,2.7,0.7,4.1,1.1
		c0.6,0.2,1.3,0.3,1.9,0.5c0.6,0.2,1.2,0.3,1.8,0.5c0.2,0.1,0.5,0.1,0.7,0.2c0.3,0.1,0.7,0.2,1,0.3c0.2,0,0.4,0.1,0.6,0.1
		c1.2,0.3,2,0.4,3-0.3c0.4-0.4,0.8-0.8,1.2-1.2c0.1-0.1,0.2-0.2,0.3-0.3c0.5-0.5,0.5-0.5,0.8-1.1c-0.1,0-0.2-0.1-0.3-0.2
		C70.9,57.9,67.5,56.4,64.1,54.9z"
                            />
                            <path
                                class="st0"
                                d="M54.7,56.1c-0.3,0.2-0.3,0.2-0.6,0.5c-0.1,0.1-0.2,0.2-0.3,0.3c-0.5,0.5-1,1-1.5,1.5c-0.1,0.1-0.2,0.2-0.3,0.3
		c-0.3,0.3-0.7,0.7-1,1c-0.2,0.2-0.5,0.5-0.7,0.7c-0.6,0.6-1.2,1.2-1.7,1.7c0.2,0.4,0.4,0.8,0.8,1.1c0.1,0.1,0.2,0.2,0.3,0.3
		c0.1,0.1,0.2,0.2,0.3,0.3c0.1,0.1,0.2,0.2,0.3,0.3c0.7,0.8,0.7,0.8,1.4,0.1c0.1-0.1,0.2-0.2,0.4-0.4c0.6-0.5,1.1-1.1,1.7-1.6
		c0.3-0.3,0.6-0.6,0.9-0.9c0.4-0.4,0.8-0.8,1.3-1.2c0.1-0.1,0.3-0.3,0.4-0.4c0.7-0.7,1.1-1.2,1.1-2.2c-0.1-0.5-0.3-0.8-0.7-1.2
		C55.9,55.9,55.4,55.9,54.7,56.1z"
                            />
                            <path
                                class="st1"
                                fill="#3B3064"
                                d="M78,58.8c-0.2-0.6-0.6-0.9-1.1-1.2c-0.1-0.1-0.3-0.1-0.4-0.2c-0.2-0.1-0.3-0.1-0.5-0.2
		c-0.2-0.1-0.3-0.1-0.5-0.2c-0.4-0.2-0.9-0.4-1.3-0.6c-0.1,0-0.2-0.1-0.3-0.1c0.4-0.4,0.7-0.7,0.9-1.2c0-0.6-0.1-0.9-0.4-1.3
		c-0.4-0.3-0.4-0.3-0.9-0.4c-0.6,0.1-0.6,0.1-1.3,0.8c-0.3,0.3-0.6,0.6-1,0.8c-0.8-0.1-0.8-0.1-1.5-0.4c-0.1-0.1-0.3-0.1-0.4-0.2
		c-0.4-0.2-0.9-0.4-1.3-0.6c0.2-0.3,0.5-0.6,0.7-1c0.2-0.5,0.2-0.8,0-1.3c-0.4-0.5-0.6-0.6-1.3-0.7c-0.6,0.1-0.6,0.1-1.4,0.9
		c-0.3,0.4-0.3,0.4-0.7,0.7c-1-0.1-1-0.1-1.9-0.6c-0.1-0.1-0.2-0.1-0.3-0.2c-0.4-0.2-0.8-0.4-1.2-0.6c0.3-0.3,0.7-0.7,0.9-1.1
		c0-0.6-0.1-0.9-0.4-1.4c-0.4-0.2-0.4-0.2-0.9-0.3c-0.7,0.1-0.7,0.1-1.4,0.8c-0.3,0.3-0.5,0.6-0.9,0.7c-0.5,0-0.5,0-1-0.3
		c-0.1-0.1-0.2-0.1-0.3-0.2c-0.6-0.3-1.2-0.6-1.8-0.9c0.5-0.5,0.9-0.9,1-1.5C57,46.4,57,46.4,56.6,46c-0.4-0.3-0.4-0.3-1-0.4
		c-0.6,0.1-0.7,0.1-1.1,0.5c-0.1,0.1-0.3,0.3-0.4,0.4c-0.2,0.2-0.2,0.2-0.4,0.4c-0.1,0.1-0.2,0.2-0.3,0.3c-0.7-0.2-1.3-0.4-1.9-0.7
		c-0.1,0-0.2-0.1-0.3-0.1c-0.3-0.2-0.7-0.3-1-0.5c0.4-0.4,0.7-0.7,0.9-1.3c0-0.5-0.1-0.8-0.3-1.3c-0.4-0.2-0.4-0.2-0.7-0.3
		c-0.7,0-1.1,0-1.6,0.5c-0.1,0.1-0.3,0.3-0.4,0.4c-0.2,0.2-0.2,0.2-0.4,0.4c-0.1,0.1-0.2,0.2-0.3,0.3c-0.6-0.2-1.1-0.3-1.7-0.5
		c0.3-0.6,0.3-0.6,0.6-1c0.5-0.5,0.9-1,1.3-1.5c0.4-0.5,0.8-0.9,1.2-1.4c0.7-0.8,1.4-1.6,2.1-2.4c0.3-0.4,0.7-0.8,1-1.2
		c0.6-0.6,1.1-1.3,1.7-1.9c0.5-0.6,0.9-1.1,1.4-1.7c0.6-0.7,1.2-1.4,1.8-2.2c0.3-0.4,0.6-0.8,0.9-1.2c0.9-1,1.7-2.1,2.6-3.2
		c0.1-0.1,0.1-0.1,0.2-0.3c0.3-0.4,0.7-0.8,1-1.3c0.1-0.1,0.1-0.1,0.2-0.3c0.1-0.2,0.2-0.3,0.4-0.5c0.3-0.4,0.5-0.7,1-1
		c0.4,0,0.6,0.1,1,0.3c2.9,1.4,6,2.7,9.2,2.7c1-0.1,1.6-0.5,2.3-1.2c0.1-0.1,0.1-0.1,0.2-0.2c0.2-0.2,0.3-0.3,0.5-0.5
		c0.1-0.2,0.3-0.3,0.4-0.5c0.2-0.2,0.4-0.4,0.6-0.6c0.5-0.6,0.5-1,0.5-1.8c-0.1-0.6-0.4-0.9-0.9-1.2c-0.1-0.1-0.3-0.2-0.4-0.2
		c-0.9-0.6-1.8-1.1-2.7-1.6c-1.9-1.1-3.7-2.2-5.6-3.4c0.6-1.4,1.1-2.8,0.9-4.3c-0.2-0.7-0.6-1.1-1.2-1.5c-1.2-0.4-2.3-0.2-3.5,0.3
		c-0.1,0.1-0.1,0.1-0.3,0.1c-0.2,0.1-0.4,0.1-0.6,0.2c-0.2,0.1-0.3,0.1-0.4,0.2c-0.5-0.7-0.9-1.3-1.3-2c-0.1-0.1-0.2-0.3-0.3-0.4
		c-0.6-0.9-1.2-1.9-1.7-2.9c-0.1-0.2-0.2-0.4-0.4-0.6c-0.2-0.4-0.5-0.8-0.7-1.2c-0.1-0.2-0.2-0.4-0.3-0.6c-0.1-0.1-0.1-0.2-0.2-0.3
		c-0.3-0.5-0.3-0.5-0.6-0.9c-0.4-0.4-0.6-0.6-1.2-0.7c-0.1,0-0.3,0-0.4,0c-0.9,0-0.9,0-2,1.1c-0.1,0.1-0.2,0.2-0.4,0.4
		c-1.1,1-2,1.9-2,3.5c0,0.2,0,0.2,0,0.3c0,2.1,0,2.1,2,6.9c0,0.1,0.1,0.2,0.1,0.3c0.1,0.2,0.2,0.4,0.3,0.6c0.3,0.7,0.5,1.3,0.6,2
		c-2.8,2.1-5.5,4.3-8.2,6.5c-0.5,0.4-0.9,0.8-1.4,1.2c-0.7,0.6-1.4,1.1-2,1.7c-0.4,0.3-0.8,0.7-1.2,1c-0.8,0.6-1.5,1.3-2.3,2
		c-0.5,0.4-0.9,0.9-1.4,1.3c-0.8,0.7-1.6,1.3-2.4,2.1c-0.6,0.6-1.2,1.1-1.9,1.6c-0.2-0.6-0.3-1.1-0.5-1.7c0.2-0.2,0.3-0.3,0.5-0.5
		c0.5-0.5,1-0.9,1.2-1.6c0-0.5-0.1-0.8-0.3-1.3c-0.4-0.2-0.4-0.2-1-0.3c-0.5,0-0.8,0.1-1.2,0.5c-0.1,0.1-0.3,0.3-0.4,0.4
		c-0.5-1.1-1-2.1-1.4-3.2c0.1-0.1,0.1-0.1,0.2-0.2c0.1-0.1,0.2-0.2,0.3-0.3c0.4-0.4,0.8-0.8,1.1-1.2c0.1-0.6,0.1-0.6,0-1.1
		c-0.4-0.5-0.6-0.7-1.2-0.9c-0.5,0.1-0.6,0.2-1,0.5c-0.4,0.4-0.4,0.4-0.7,0.4c-0.1-0.3-0.2-0.6-0.4-0.8c-0.1-0.1-0.1-0.2-0.2-0.3
		c-0.3-0.6-0.5-1.1-0.7-1.7c0-0.3,0-0.3,0.2-0.6c0.5-0.4,1-0.9,1.3-1.4c0.1-0.6,0.1-0.6,0-1.1c-0.3-0.5-0.6-0.6-1.1-0.8
		c-0.6,0-0.7,0-1.1,0.4c-0.4,0.4-0.4,0.4-0.7,0.4c-0.1-0.3-0.2-0.6-0.4-0.8c-0.1-0.1-0.1-0.2-0.2-0.3c-0.3-0.6-0.5-1.1-0.7-1.7
		c0-0.3,0-0.3,0.2-0.6c0.5-0.4,1-0.9,1.3-1.4c0.1-0.5,0.1-0.5,0-0.9c-0.3-0.5-0.5-0.7-1.1-0.9c-0.6,0-0.9,0.1-1.4,0.5
		c-0.1,0.1-0.3,0.2-0.4,0.4c-0.1-0.3-0.2-0.5-0.4-0.8c-0.1-0.1-0.1-0.2-0.2-0.3c-0.3-0.6-0.5-1.2-0.7-1.8c-0.1-0.4-0.1-0.4,0.2-0.7
		c0.7-0.5,1.3-1.1,1.5-1.9c-0.1-0.4-0.1-0.4-0.4-0.9C24.4,4,24.4,4,23.8,3.9c-0.5,0-0.7,0.2-1.1,0.5c-0.1,0.1-0.3,0.3-0.4,0.4
		c-0.1-0.3-0.3-0.7-0.5-1c-0.1-0.1-0.1-0.3-0.2-0.4c-0.1-0.3-0.2-0.6-0.4-0.8C21,1.7,20.6,1,19.8,0.6c-0.1,0-0.2,0-0.3,0
		c-0.1,0-0.2,0-0.3,0C18.4,0.7,18,1,17.6,1.5c-0.1,0.1-0.2,0.2-0.2,0.2C17.2,1.9,17,2,16.9,2.2c-0.2,0.3-0.5,0.5-0.8,0.7
		c-0.9,0.9-1.7,1.7-2.4,2.7c-0.8,1.5-0.8,1.5,0.1,4.9c0.1,0.3,0.2,0.6,0.2,0.9c0.1,0.5,0.3,1.1,0.4,1.6c0.1,0.5,0.3,1.1,0.4,1.6
		c0,0.1,0.1,0.2,0.1,0.3c0.1,0.5,0.3,1.1,0.4,1.6c0.2,0.8,0.4,1.6,0.7,2.4c0.2,0.6,0.4,1.1,0.5,1.7c-0.6,1.6-0.6,1.6-2,2.8
		c-0.2,0.2-0.5,0.4-0.7,0.6c-0.3,0.3-0.6,0.6-1,0.9c-0.8,0.7-2.1,1.6-2.3,2.8c0.1,0.6,0.3,0.9,0.7,1.4c0.1,0.1,0.3,0.3,0.4,0.4
		c0.2,0.2,0.3,0.3,0.5,0.5c0.2,0.2,0.5,0.5,0.7,0.7c0.6,0.6,1.3,1.3,1.9,1.9c0.1,0.1,0.2,0.2,0.3,0.3c0.4,0.4,0.7,0.5,1.2,0.5
		c0.8-0.1,0.8-0.1,1.5-0.9c0.1-0.1,0.2-0.2,0.3-0.3c0.2-0.2,0.3-0.3,0.5-0.5c0.1-0.1,0.3-0.3,0.4-0.4c0.1,0.3,0.2,0.6,0.3,0.9
		c0,0.2,0.1,0.3,0.1,0.5c0.1,0.3,0.1,0.6,0.2,0.9c0.1,0.2,0.1,0.4,0.2,0.6c0.1,0.4,0.2,0.9,0.3,1.3c0.1,0.4,0.2,0.9,0.3,1.3
		c0.1,0.2,0.1,0.4,0.2,0.7c0,0.2,0,0.2,0.1,0.3c0.2,0.7,0.4,1.5,0.6,2.2c0.2,0.6,0.3,1.2,0.5,1.9c0,0.1,0.1,0.3,0.1,0.4
		c0.1,0.2,0.1,0.5,0.2,0.7c0.1,0.2,0.1,0.4,0.2,0.6c0.1,0.5,0.1,0.5-0.2,0.8c-0.1,0.1-0.2,0.2-0.3,0.3c-0.1,0.1-0.2,0.1-0.3,0.2
		c-0.9,0.7-0.9,0.7-2.8,2.7c-0.2,0.2-0.4,0.4-0.6,0.6c-0.4,0.4-0.7,0.7-1.1,1.1c-0.6,0.6-1.2,1.2-1.8,1.8c-1.3,1.3-2.5,2.5-3.8,3.9
		c-0.1,0.1-0.2,0.3-0.4,0.4c-1.3,1.5-2.6,2.9-3.9,4.5C3.7,64,0.3,68.6,0.8,74.2C1,75.4,1.6,76.3,2.6,77c5,3.5,12.2-2.2,16-5.3
		c0.2-0.1,0.4-0.3,0.5-0.4c0.9-0.7,1.8-1.5,2.6-2.2c0.4-0.4,0.8-0.7,1.2-1.1c0.8-0.7,1.6-1.4,2.3-2.2c0.6-0.6,1.1-1.1,1.7-1.7
		c0.5-0.4,0.9-0.8,1.3-1.3c1-1.2,1-1.2,2.2-2.2c0.8-0.8,1.6-1.5,2.3-2.4c0.3-0.3,0.5-0.6,0.8-0.9c0.1-0.1,0.2-0.2,0.2-0.2
		c0.2-0.2,0.5-0.5,0.7-0.7c0.6-0.1,0.6-0.1,1.3,0.1c0.1,0,0.2,0.1,0.4,0.1c0.4,0.1,0.8,0.2,1.2,0.3c0.3,0.1,0.6,0.2,0.9,0.2
		c0.6,0.2,1.2,0.3,1.8,0.5c0.9,0.2,1.8,0.5,2.7,0.7c1.5,0.4,3,0.8,4.5,1.2c-0.1,0.1-0.3,0.3-0.4,0.4c-0.2,0.2-0.3,0.3-0.5,0.5
		c-0.1,0.1-0.2,0.2-0.3,0.3c-0.5,0.5-0.8,0.9-0.8,1.6c0.1,0.7,0.3,1,0.9,1.5c0.1,0.1,0.2,0.2,0.3,0.3c0.2,0.2,0.4,0.4,0.5,0.6
		c0.3,0.3,0.6,0.6,0.9,0.9c0.2,0.2,0.4,0.4,0.6,0.6c0.1,0.1,0.2,0.2,0.3,0.3c0.7,0.7,1.4,1.5,2.4,1.6c0.5-0.1,0.6-0.1,1-0.4
		c0.1-0.1,0.2-0.2,0.3-0.3c0.1-0.1,0.2-0.2,0.3-0.3c0.2-0.2,0.5-0.5,0.7-0.7c0.4-0.4,0.7-0.8,1.1-1.1c0.4-0.4,0.7-0.8,1.1-1.1
		c0.1-0.1,0.2-0.2,0.4-0.4c0.1-0.1,0.2-0.2,0.3-0.3c0.1-0.1,0.2-0.2,0.3-0.3c0.3-0.3,0.3-0.3,0.6-0.5c0.4-0.3,0.5-0.3,1-0.3
		c0.2,0,0.3,0.1,0.4,0.1c0.2,0,0.3,0.1,0.5,0.1c0.4,0.1,0.7,0.2,1.1,0.3c0.4,0.1,0.8,0.2,1.2,0.3c0.5,0.1,1,0.3,1.5,0.4
		c0.1,0,0.2,0.1,0.3,0.1c1,0.3,2,0.5,2.9,0.8c0.3,0.1,0.3,0.1,0.6,0.1c0.6,0.1,1.1,0.3,1.7,0.4c0.3,0.1,0.3,0.1,0.5,0.1
		c0.8,0.2,1.5,0.4,2.3,0.4c1.9-0.1,2.9-1,4.2-2.3c0.3-0.3,0.5-0.5,0.8-0.8c0.2-0.2,0.3-0.3,0.5-0.5c0.1-0.1,0.2-0.2,0.2-0.2
		c0.6-0.6,1.2-1.2,1.3-2c0-0.1,0-0.2,0-0.3C78,59.1,78,58.9,78,58.8z M65,20.4c0.1-0.1,0.1-0.2,0.2-0.3c0.1-0.1,0.1-0.2,0.2-0.3
		c0.1-0.1,0.2-0.2,0.2-0.3c0.5-0.7,1-1.4,1.5-2c0.4,0.2,0.7,0.4,1.1,0.6c0.2,0.1,0.2,0.1,0.5,0.3c0.9,0.6,1.9,1.1,2.8,1.7
		c0.1,0.1,0.2,0.1,0.3,0.2c0.3,0.2,0.7,0.4,1,0.6c0.2,0.1,0.3,0.2,0.5,0.3c0.2,0.1,0.3,0.2,0.5,0.3c0.5,0.3,0.9,0.5,1.2,0.9
		c-3.5,1.7-3.5,1.7-7.5-0.2c-0.2-0.1-0.4-0.2-0.5-0.2c-0.3-0.1-0.3-0.1-0.5-0.2c-0.3-0.1-0.3-0.1-0.5-0.2c-0.4-0.2-0.4-0.2-0.8-0.4
		C64.8,20.7,64.8,20.7,65,20.4z M54.9,5.5C55,5,55.3,4.7,55.7,4.3c0.2-0.2,0.4-0.3,0.6-0.5c0.1-0.1,0.2-0.1,0.2-0.2
		c0.7,1.2,1.5,2.4,2.2,3.6c0.1,0.2,0.2,0.4,0.3,0.5c0.8,1.2,1.5,2.5,2.2,3.8c-0.4,0.3-0.7,0.5-1.1,0.8c-0.2,0.1-0.3,0.2-0.5,0.3
		c-0.2,0.1-0.3,0.2-0.5,0.3c-0.1,0.1-0.3,0.2-0.4,0.3c-0.4,0.2-0.6,0.5-1,0.5C56.3,11.7,54.9,8.1,54.9,5.5z M13.1,72.1
		c-0.6,0.4-0.9,0.4-1.6,0.4c-1.7-0.3-3.1-1.1-4.1-2.5c-0.8-1.2-1.4-2.4-1.1-3.9c0.3-0.5,0.4-0.6,1-0.8c0.6,0,0.9,0,1.3,0.4
		c0.1,0.3,0.2,0.7,0.3,1c0.3,1.1,0.8,1.9,1.8,2.5c0.5,0.2,1,0.3,1.5,0.4c0.6,0.2,0.8,0.4,1.1,0.9C13.4,71.3,13.3,71.6,13.1,72.1z
		 M21.1,25.6c-0.3,0.3-0.5,0.5-0.8,0.8c-0.4,0.4-0.9,0.9-1.3,1.3c-0.3,0.3-0.6,0.6-0.8,0.8c-0.1,0.1-0.3,0.3-0.4,0.4
		c-0.1,0.1-0.3,0.3-0.4,0.4c-0.1,0.1-0.2,0.2-0.4,0.4c-0.1,0.1-0.2,0.2-0.3,0.3c-0.4,0.3-0.5,0.3-0.9,0c-0.1-0.1-0.2-0.2-0.4-0.4
		c-0.3-0.3-0.7-0.7-1-1c-0.1-0.1-0.2-0.2-0.2-0.3c-0.1-0.2-0.3-0.3-0.4-0.5c0.7-0.7,1.3-1.3,2-2c0.3-0.3,0.5-0.5,0.8-0.8
		c0.4-0.4,0.8-0.8,1.2-1.2c0.1-0.1,0.3-0.3,0.4-0.4c0.7-0.7,1.4-1.4,2.2-1.9c0.6-0.1,1,0,1.5,0.3c0.4,0.3,0.6,0.6,0.7,1.1
		c0,0.7,0,1.1-0.4,1.6c-0.1,0.1-0.2,0.2-0.3,0.3c-0.1,0.1-0.2,0.2-0.4,0.4C21.4,25.4,21.3,25.5,21.1,25.6z M32.3,35.3
		c-0.1,0.1-0.2,0.2-0.3,0.2c-0.1,0.1-0.2,0.2-0.3,0.3c-0.2,0.2-0.5,0.4-0.7,0.6c-0.1,0.1-0.2,0.2-0.4,0.3c-1,0.9-1.9,1.8-2.9,2.6
		c-1,0.9-1.9,1.8-3,2.7c-0.3-0.3-0.4-0.6-0.5-1.1c0-0.2,0-0.2-0.1-0.3c-0.1-0.3-0.2-0.7-0.3-1c-0.1-0.3-0.1-0.5-0.2-0.8
		c-0.1-0.5-0.3-1-0.4-1.6c-0.2-0.7-0.3-1.4-0.5-2c-0.1-0.5-0.3-1.1-0.4-1.6c-0.1-0.3-0.1-0.5-0.2-0.8c-0.1-0.4-0.2-0.7-0.3-1.1
		c0-0.1-0.1-0.2-0.1-0.3c-0.1-0.5-0.2-1-0.3-1.5c-0.1-0.6-0.1-0.8,0.2-1.3c0.2-0.2,0.5-0.4,0.7-0.6c0.1-0.1,0.2-0.1,0.2-0.2
		c0.6-0.5,1.1-1,1.6-1.6c1-1.2,1.1-2.5,0.9-4c-0.3-1.2-1-1.9-2-2.6c-1-0.5-2.1-0.6-3.2-0.4c-0.2,0-0.2,0-0.4,0.1
		c-0.1,0-0.3,0.1-0.4,0.1c-0.1,0-0.2,0-0.3,0.1c-0.3-1.2-0.6-2.3-0.9-3.5c-0.2-0.6-0.3-1.2-0.5-1.8c-0.2-0.6-0.3-1.2-0.5-1.7
		c-0.1-0.2-0.1-0.4-0.2-0.7c-0.4-1.4-0.8-2.8-0.9-4.2c0.2-0.8,0.6-1.3,1.2-1.9c0.1-0.1,0.2-0.2,0.3-0.3c0.5-0.5,1-1,1.7-1.3
		c2.3,5.2,4.6,10.4,6.9,15.7c1,2.2,2,4.5,3,6.7c0.6,1.4,1.2,2.7,1.8,4.1c0.1,0.1,0.1,0.2,0.2,0.3c0.2,0.5,0.5,1.1,0.7,1.6
		c0,0.1,0.1,0.2,0.1,0.3c0.1,0.2,0.2,0.4,0.2,0.5c0.2,0.5,0.4,0.9,0.5,1.4C32.6,35,32.6,35,32.3,35.3z M56,59.7
		c-0.1,0.1-0.3,0.3-0.4,0.4c-0.4,0.4-0.8,0.8-1.3,1.2c-0.3,0.3-0.6,0.6-0.9,0.9c-0.6,0.5-1.1,1.1-1.7,1.6c-0.1,0.1-0.2,0.2-0.4,0.4
		c-0.7,0.6-0.7,0.6-1.4-0.1c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.2-0.2-0.3-0.3c-0.3-0.3-0.6-0.7-0.8-1.1
		c0.6-0.6,1.2-1.2,1.7-1.7c0.2-0.2,0.5-0.5,0.7-0.7c0.3-0.3,0.7-0.7,1-1c0.1-0.1,0.2-0.2,0.3-0.3c0.5-0.5,1-1,1.5-1.5
		c0.1-0.1,0.2-0.2,0.3-0.3c0.3-0.3,0.3-0.3,0.6-0.5c0.7-0.1,1.2-0.1,1.8,0.3c0.3,0.4,0.6,0.7,0.7,1.2C57.1,58.6,56.7,59.1,56,59.7z
		 M73.9,60.7c-0.1,0.1-0.2,0.2-0.3,0.3c-0.4,0.4-0.8,0.8-1.2,1.2c-1,0.7-1.8,0.6-3,0.3c-0.2,0-0.4-0.1-0.6-0.1
		c-0.3-0.1-0.7-0.2-1-0.3c-0.2-0.1-0.5-0.1-0.7-0.2c-0.6-0.2-1.2-0.3-1.8-0.5c-0.6-0.2-1.3-0.3-1.9-0.5c-1.4-0.4-2.7-0.7-4.1-1.1
		c0.1-0.6,0.3-1.1,0.4-1.7c0-1.4-0.2-2.5-1.3-3.6c-0.9-0.8-1.8-1.1-3.1-1.1c-1.7,0-2.8,0.8-3.9,2c-0.2,0.2-0.4,0.5-0.7,0.7
		c-0.2,0.2-0.2,0.2-0.3,0.3c-0.3,0.3-0.3,0.3-0.6,0.6c-0.4,0.1-0.7,0.2-1.2,0.1c-0.2,0-0.3-0.1-0.5-0.1c-0.3-0.1-0.6-0.1-0.9-0.2
		c-0.2-0.1-0.4-0.1-0.6-0.2c-0.5-0.1-1.1-0.3-1.6-0.4c-0.4-0.1-0.9-0.2-1.3-0.3c-2.3-0.6-4.7-1.2-7-1.9c0.2-0.4,0.4-0.7,0.7-1.1
		c0.1-0.1,0.2-0.2,0.3-0.3c0.9-1,0.9-1,1.9-2c0.5-0.5,0.9-1,1.4-1.5c0.5-0.6,1-1.1,1.5-1.7c0.1-0.1,0.2-0.2,0.2-0.3
		c0.3-0.4,0.7-0.8,1.1-1.1c0.4,0,0.8,0.1,1.2,0.3c0.1,0,0.2,0.1,0.3,0.1c0.4,0.2,0.7,0.3,1.1,0.5c0.3,0.1,0.5,0.2,0.8,0.4
		c0.6,0.2,1.1,0.5,1.7,0.7c0.8,0.4,1.7,0.7,2.5,1.1c1.1,0.5,2.2,1,3.3,1.4c1.7,0.7,3.3,1.5,5,2.2c0.5,0.2,1.1,0.5,1.6,0.7
		c0.1,0.1,0.3,0.1,0.4,0.2c0.8,0.4,1.7,0.7,2.5,1.1c3.4,1.5,6.8,3,10.3,4.5c0.1,0.1,0.2,0.1,0.3,0.2C74.5,60.2,74.5,60.2,73.9,60.7z
		"
                            />
                        </g>
                        <g>
                            <path
                                ref={pathRef}
                                className="d_none"
                                opacity="1"
                                d="M1672.17 231.675C1658.17 240.675 1615.9 242.675 1558.9 178.675C1487.64 98.6749 1423.69 138.175 1425.52 188.675C1427.35 239.175 1511.85 206.173 1455.67 145.673C1399.48 85.1733 1290.69 40.5169 1170.65 143.132C908 367.634 121 461.632 121 850.132C121 1315.63 1523.63 1362.13 1523.63 1850.63C1523.63 2378.13 267.629 2028.13 294.129 2598.13C325.954 3282.67 1504.61 2562.41 1532.11 3177.41C1560.61 3814.85 229.654 3482.41 275.106 3985.41C320.106 4483.41 1586.11 4393.41 1985.61 4110.91"
                                stroke="#3B3064"
                                stroke-width="1.5"
                                stroke-dasharray="6 6"
                            />
                        </g>
                    </svg>
                ) : (
                    <svg
                        width="1700"
                        height="4758"
                        viewBox="0 0 1700 4758"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <g ref={airplaneRef}>
                            <path
                                class="st0"
                                d="M32,33.2c-0.1-0.2-0.2-0.4-0.2-0.5c0-0.1-0.1-0.2-0.1-0.3c-0.2-0.5-0.5-1.1-0.7-1.6c-0.1-0.1-0.1-0.2-0.2-0.3
                          c-0.6-1.3-1.2-2.7-1.8-4.1c-1-2.2-2-4.5-3-6.7C23.6,14.4,21.4,9.2,19.1,4c-0.7,0.3-1.2,0.8-1.7,1.3c-0.1,0.1-0.2,0.2-0.3,0.3
                          c-0.6,0.6-1,1.1-1.2,1.9c0.1,1.4,0.5,2.8,0.9,4.2c0.1,0.2,0.1,0.4,0.2,0.7c0.2,0.6,0.3,1.2,0.5,1.7c0.2,0.6,0.3,1.2,0.5,1.8
                          c0.3,1.2,0.6,2.3,0.9,3.5c0.1,0,0.2,0,0.3-0.1c0.1,0,0.2-0.1,0.4-0.1c0.2,0,0.2,0,0.4-0.1c1.1-0.2,2.2-0.1,3.2,0.4
                          c1,0.7,1.7,1.4,2,2.6c0.2,1.6,0.1,2.8-0.9,4c-0.5,0.6-1,1.1-1.6,1.6c-0.1,0.1-0.2,0.1-0.2,0.2c-0.2,0.2-0.4,0.4-0.7,0.6
                          c-0.4,0.5-0.3,0.7-0.2,1.3c0.1,0.5,0.2,1,0.3,1.5c0,0.1,0.1,0.2,0.1,0.3c0.1,0.4,0.2,0.7,0.3,1.1c0.1,0.3,0.1,0.5,0.2,0.8
                          c0.1,0.5,0.3,1.1,0.4,1.6c0.2,0.7,0.3,1.4,0.5,2c0.1,0.5,0.3,1,0.4,1.6c0.1,0.3,0.1,0.5,0.2,0.8c0.1,0.3,0.2,0.7,0.3,1
                          c0,0.2,0,0.2,0.1,0.3c0.1,0.5,0.2,0.7,0.5,1.1c1-0.9,2-1.8,3-2.7c1-0.9,1.9-1.8,2.9-2.6c0.1-0.1,0.2-0.2,0.4-0.3
                          c0.2-0.2,0.5-0.4,0.7-0.6c0.1-0.1,0.2-0.2,0.3-0.3c0.1-0.1,0.2-0.2,0.3-0.2c0.2-0.3,0.2-0.3,0.2-0.7C32.4,34.1,32.2,33.6,32,33.2z"
                            />
                            <path
                                class="st0"
                                d="M58.8,13.3c0.1-0.1,0.3-0.2,0.4-0.3c0.2-0.1,0.3-0.2,0.5-0.3c0.2-0.1,0.3-0.2,0.5-0.3c0.4-0.3,0.8-0.5,1.1-0.8
                          c-0.7-1.3-1.4-2.5-2.2-3.8c-0.1-0.2-0.2-0.4-0.3-0.5c-0.7-1.2-1.5-2.4-2.2-3.6c-0.1,0.1-0.2,0.1-0.2,0.2c-0.2,0.2-0.4,0.3-0.6,0.5
                          C55.3,4.7,55,5,54.9,5.5c0,2.6,1.4,6.2,2.8,8.2C58.2,13.8,58.4,13.5,58.8,13.3z"
                            />
                            <path
                                class="st0"
                                d="M65.9,21.5c0.3,0.1,0.3,0.1,0.5,0.2c0.3,0.1,0.3,0.1,0.5,0.2c0.2,0.1,0.4,0.1,0.5,0.2c4,1.9,4,1.9,7.5,0.2
                          c-0.4-0.4-0.8-0.7-1.2-0.9c-0.2-0.1-0.3-0.2-0.5-0.3c-0.2-0.1-0.3-0.2-0.5-0.3c-0.3-0.2-0.7-0.4-1-0.6c-0.1-0.1-0.2-0.1-0.3-0.2
                          c-0.9-0.6-1.9-1.1-2.8-1.7c-0.2-0.1-0.2-0.1-0.5-0.3c-0.4-0.2-0.7-0.4-1.1-0.6c-0.5,0.6-1,1.3-1.5,2c-0.1,0.1-0.2,0.2-0.2,0.3
                          c-0.1,0.1-0.1,0.2-0.2,0.3c-0.1,0.1-0.1,0.2-0.2,0.3c-0.2,0.3-0.2,0.3,0,0.7C65.4,21.3,65.4,21.3,65.9,21.5z"
                            />
                            <path
                                class="st0"
                                d="M21.9,24.9c0.1-0.1,0.2-0.2,0.3-0.3c0.5-0.5,0.4-0.9,0.4-1.6c-0.1-0.5-0.3-0.8-0.7-1.1
                          c-0.5-0.3-0.9-0.4-1.5-0.3c-0.9,0.5-1.5,1.2-2.2,1.9c-0.1,0.1-0.2,0.2-0.4,0.4c-0.4,0.4-0.8,0.8-1.2,1.2c-0.3,0.3-0.5,0.5-0.8,0.8
                          c-0.7,0.7-1.3,1.3-2,2c0.1,0.2,0.3,0.3,0.4,0.5c0.1,0.1,0.2,0.2,0.2,0.3c0.3,0.4,0.6,0.7,1,1c0.1,0.1,0.2,0.2,0.4,0.4
                          c0.4,0.3,0.5,0.4,0.9,0c0.1-0.1,0.2-0.2,0.3-0.3c0.1-0.1,0.2-0.2,0.4-0.4c0.1-0.1,0.3-0.3,0.4-0.4c0.1-0.1,0.3-0.3,0.4-0.4
                          c0.3-0.3,0.6-0.6,0.8-0.8c0.4-0.4,0.9-0.9,1.3-1.3c0.3-0.3,0.5-0.5,0.8-0.8c0.1-0.1,0.3-0.3,0.4-0.4C21.7,25.1,21.8,25,21.9,24.9z"
                            />
                            <path
                                class="st0"
                                d="M64.1,54.9c-0.8-0.4-1.7-0.7-2.5-1.1c-0.1-0.1-0.3-0.1-0.4-0.2c-0.5-0.2-1.1-0.5-1.6-0.7
                          c-1.7-0.7-3.3-1.5-5-2.2c-1.1-0.5-2.2-1-3.3-1.4c-0.8-0.4-1.7-0.7-2.5-1.1c-0.6-0.2-1.1-0.5-1.7-0.7c-0.3-0.1-0.5-0.2-0.8-0.4
                          c-0.4-0.2-0.7-0.3-1.1-0.5c-0.1,0-0.2-0.1-0.3-0.1c-0.4-0.2-0.7-0.3-1.2-0.3c-0.5,0.2-0.8,0.7-1.1,1.1c-0.1,0.1-0.2,0.2-0.2,0.3
                          c-0.5,0.5-1,1.1-1.5,1.7c-0.4,0.5-0.9,1-1.4,1.5c-0.9,1-0.9,1-1.9,2c-0.1,0.1-0.2,0.2-0.3,0.3c-0.3,0.3-0.5,0.7-0.7,1.1
                          c2.3,0.7,4.7,1.3,7,1.9c0.4,0.1,0.9,0.2,1.3,0.3c0.5,0.1,1.1,0.3,1.6,0.4c0.2,0.1,0.4,0.1,0.6,0.2c0.3,0.1,0.6,0.1,0.9,0.2
                          c0.2,0,0.3,0.1,0.5,0.1c0.5,0.1,0.8,0.1,1.2-0.1c0.3-0.2,0.3-0.2,0.6-0.6c0.2-0.2,0.2-0.2,0.3-0.3c0.2-0.2,0.4-0.5,0.7-0.7
                          c1.1-1.2,2.2-1.9,3.9-2c1.2,0,2.1,0.3,3.1,1.1c1,1.1,1.3,2.1,1.3,3.6c-0.1,0.6-0.2,1.1-0.4,1.7c1.4,0.4,2.7,0.7,4.1,1.1
                          c0.6,0.2,1.3,0.3,1.9,0.5c0.6,0.2,1.2,0.3,1.8,0.5c0.2,0.1,0.5,0.1,0.7,0.2c0.3,0.1,0.7,0.2,1,0.3c0.2,0,0.4,0.1,0.6,0.1
                          c1.2,0.3,2,0.4,3-0.3c0.4-0.4,0.8-0.8,1.2-1.2c0.1-0.1,0.2-0.2,0.3-0.3c0.5-0.5,0.5-0.5,0.8-1.1c-0.1,0-0.2-0.1-0.3-0.2
                          C70.9,57.9,67.5,56.4,64.1,54.9z"
                            />
                            <path
                                class="st0"
                                d="M54.7,56.1c-0.3,0.2-0.3,0.2-0.6,0.5c-0.1,0.1-0.2,0.2-0.3,0.3c-0.5,0.5-1,1-1.5,1.5c-0.1,0.1-0.2,0.2-0.3,0.3
                          c-0.3,0.3-0.7,0.7-1,1c-0.2,0.2-0.5,0.5-0.7,0.7c-0.6,0.6-1.2,1.2-1.7,1.7c0.2,0.4,0.4,0.8,0.8,1.1c0.1,0.1,0.2,0.2,0.3,0.3
                          c0.1,0.1,0.2,0.2,0.3,0.3c0.1,0.1,0.2,0.2,0.3,0.3c0.7,0.8,0.7,0.8,1.4,0.1c0.1-0.1,0.2-0.2,0.4-0.4c0.6-0.5,1.1-1.1,1.7-1.6
                          c0.3-0.3,0.6-0.6,0.9-0.9c0.4-0.4,0.8-0.8,1.3-1.2c0.1-0.1,0.3-0.3,0.4-0.4c0.7-0.7,1.1-1.2,1.1-2.2c-0.1-0.5-0.3-0.8-0.7-1.2
                          C55.9,55.9,55.4,55.9,54.7,56.1z"
                            />
                            <path
                                class="st1"
                                fill="#3B3064"
                                d="M78,58.8c-0.2-0.6-0.6-0.9-1.1-1.2c-0.1-0.1-0.3-0.1-0.4-0.2c-0.2-0.1-0.3-0.1-0.5-0.2
                          c-0.2-0.1-0.3-0.1-0.5-0.2c-0.4-0.2-0.9-0.4-1.3-0.6c-0.1,0-0.2-0.1-0.3-0.1c0.4-0.4,0.7-0.7,0.9-1.2c0-0.6-0.1-0.9-0.4-1.3
                          c-0.4-0.3-0.4-0.3-0.9-0.4c-0.6,0.1-0.6,0.1-1.3,0.8c-0.3,0.3-0.6,0.6-1,0.8c-0.8-0.1-0.8-0.1-1.5-0.4c-0.1-0.1-0.3-0.1-0.4-0.2
                          c-0.4-0.2-0.9-0.4-1.3-0.6c0.2-0.3,0.5-0.6,0.7-1c0.2-0.5,0.2-0.8,0-1.3c-0.4-0.5-0.6-0.6-1.3-0.7c-0.6,0.1-0.6,0.1-1.4,0.9
                          c-0.3,0.4-0.3,0.4-0.7,0.7c-1-0.1-1-0.1-1.9-0.6c-0.1-0.1-0.2-0.1-0.3-0.2c-0.4-0.2-0.8-0.4-1.2-0.6c0.3-0.3,0.7-0.7,0.9-1.1
                          c0-0.6-0.1-0.9-0.4-1.4c-0.4-0.2-0.4-0.2-0.9-0.3c-0.7,0.1-0.7,0.1-1.4,0.8c-0.3,0.3-0.5,0.6-0.9,0.7c-0.5,0-0.5,0-1-0.3
                          c-0.1-0.1-0.2-0.1-0.3-0.2c-0.6-0.3-1.2-0.6-1.8-0.9c0.5-0.5,0.9-0.9,1-1.5C57,46.4,57,46.4,56.6,46c-0.4-0.3-0.4-0.3-1-0.4
                          c-0.6,0.1-0.7,0.1-1.1,0.5c-0.1,0.1-0.3,0.3-0.4,0.4c-0.2,0.2-0.2,0.2-0.4,0.4c-0.1,0.1-0.2,0.2-0.3,0.3c-0.7-0.2-1.3-0.4-1.9-0.7
                          c-0.1,0-0.2-0.1-0.3-0.1c-0.3-0.2-0.7-0.3-1-0.5c0.4-0.4,0.7-0.7,0.9-1.3c0-0.5-0.1-0.8-0.3-1.3c-0.4-0.2-0.4-0.2-0.7-0.3
                          c-0.7,0-1.1,0-1.6,0.5c-0.1,0.1-0.3,0.3-0.4,0.4c-0.2,0.2-0.2,0.2-0.4,0.4c-0.1,0.1-0.2,0.2-0.3,0.3c-0.6-0.2-1.1-0.3-1.7-0.5
                          c0.3-0.6,0.3-0.6,0.6-1c0.5-0.5,0.9-1,1.3-1.5c0.4-0.5,0.8-0.9,1.2-1.4c0.7-0.8,1.4-1.6,2.1-2.4c0.3-0.4,0.7-0.8,1-1.2
                          c0.6-0.6,1.1-1.3,1.7-1.9c0.5-0.6,0.9-1.1,1.4-1.7c0.6-0.7,1.2-1.4,1.8-2.2c0.3-0.4,0.6-0.8,0.9-1.2c0.9-1,1.7-2.1,2.6-3.2
                          c0.1-0.1,0.1-0.1,0.2-0.3c0.3-0.4,0.7-0.8,1-1.3c0.1-0.1,0.1-0.1,0.2-0.3c0.1-0.2,0.2-0.3,0.4-0.5c0.3-0.4,0.5-0.7,1-1
                          c0.4,0,0.6,0.1,1,0.3c2.9,1.4,6,2.7,9.2,2.7c1-0.1,1.6-0.5,2.3-1.2c0.1-0.1,0.1-0.1,0.2-0.2c0.2-0.2,0.3-0.3,0.5-0.5
                          c0.1-0.2,0.3-0.3,0.4-0.5c0.2-0.2,0.4-0.4,0.6-0.6c0.5-0.6,0.5-1,0.5-1.8c-0.1-0.6-0.4-0.9-0.9-1.2c-0.1-0.1-0.3-0.2-0.4-0.2
                          c-0.9-0.6-1.8-1.1-2.7-1.6c-1.9-1.1-3.7-2.2-5.6-3.4c0.6-1.4,1.1-2.8,0.9-4.3c-0.2-0.7-0.6-1.1-1.2-1.5c-1.2-0.4-2.3-0.2-3.5,0.3
                          c-0.1,0.1-0.1,0.1-0.3,0.1c-0.2,0.1-0.4,0.1-0.6,0.2c-0.2,0.1-0.3,0.1-0.4,0.2c-0.5-0.7-0.9-1.3-1.3-2c-0.1-0.1-0.2-0.3-0.3-0.4
                          c-0.6-0.9-1.2-1.9-1.7-2.9c-0.1-0.2-0.2-0.4-0.4-0.6c-0.2-0.4-0.5-0.8-0.7-1.2c-0.1-0.2-0.2-0.4-0.3-0.6c-0.1-0.1-0.1-0.2-0.2-0.3
                          c-0.3-0.5-0.3-0.5-0.6-0.9c-0.4-0.4-0.6-0.6-1.2-0.7c-0.1,0-0.3,0-0.4,0c-0.9,0-0.9,0-2,1.1c-0.1,0.1-0.2,0.2-0.4,0.4
                          c-1.1,1-2,1.9-2,3.5c0,0.2,0,0.2,0,0.3c0,2.1,0,2.1,2,6.9c0,0.1,0.1,0.2,0.1,0.3c0.1,0.2,0.2,0.4,0.3,0.6c0.3,0.7,0.5,1.3,0.6,2
                          c-2.8,2.1-5.5,4.3-8.2,6.5c-0.5,0.4-0.9,0.8-1.4,1.2c-0.7,0.6-1.4,1.1-2,1.7c-0.4,0.3-0.8,0.7-1.2,1c-0.8,0.6-1.5,1.3-2.3,2
                          c-0.5,0.4-0.9,0.9-1.4,1.3c-0.8,0.7-1.6,1.3-2.4,2.1c-0.6,0.6-1.2,1.1-1.9,1.6c-0.2-0.6-0.3-1.1-0.5-1.7c0.2-0.2,0.3-0.3,0.5-0.5
                          c0.5-0.5,1-0.9,1.2-1.6c0-0.5-0.1-0.8-0.3-1.3c-0.4-0.2-0.4-0.2-1-0.3c-0.5,0-0.8,0.1-1.2,0.5c-0.1,0.1-0.3,0.3-0.4,0.4
                          c-0.5-1.1-1-2.1-1.4-3.2c0.1-0.1,0.1-0.1,0.2-0.2c0.1-0.1,0.2-0.2,0.3-0.3c0.4-0.4,0.8-0.8,1.1-1.2c0.1-0.6,0.1-0.6,0-1.1
                          c-0.4-0.5-0.6-0.7-1.2-0.9c-0.5,0.1-0.6,0.2-1,0.5c-0.4,0.4-0.4,0.4-0.7,0.4c-0.1-0.3-0.2-0.6-0.4-0.8c-0.1-0.1-0.1-0.2-0.2-0.3
                          c-0.3-0.6-0.5-1.1-0.7-1.7c0-0.3,0-0.3,0.2-0.6c0.5-0.4,1-0.9,1.3-1.4c0.1-0.6,0.1-0.6,0-1.1c-0.3-0.5-0.6-0.6-1.1-0.8
                          c-0.6,0-0.7,0-1.1,0.4c-0.4,0.4-0.4,0.4-0.7,0.4c-0.1-0.3-0.2-0.6-0.4-0.8c-0.1-0.1-0.1-0.2-0.2-0.3c-0.3-0.6-0.5-1.1-0.7-1.7
                          c0-0.3,0-0.3,0.2-0.6c0.5-0.4,1-0.9,1.3-1.4c0.1-0.5,0.1-0.5,0-0.9c-0.3-0.5-0.5-0.7-1.1-0.9c-0.6,0-0.9,0.1-1.4,0.5
                          c-0.1,0.1-0.3,0.2-0.4,0.4c-0.1-0.3-0.2-0.5-0.4-0.8c-0.1-0.1-0.1-0.2-0.2-0.3c-0.3-0.6-0.5-1.2-0.7-1.8c-0.1-0.4-0.1-0.4,0.2-0.7
                          c0.7-0.5,1.3-1.1,1.5-1.9c-0.1-0.4-0.1-0.4-0.4-0.9C24.4,4,24.4,4,23.8,3.9c-0.5,0-0.7,0.2-1.1,0.5c-0.1,0.1-0.3,0.3-0.4,0.4
                          c-0.1-0.3-0.3-0.7-0.5-1c-0.1-0.1-0.1-0.3-0.2-0.4c-0.1-0.3-0.2-0.6-0.4-0.8C21,1.7,20.6,1,19.8,0.6c-0.1,0-0.2,0-0.3,0
                          c-0.1,0-0.2,0-0.3,0C18.4,0.7,18,1,17.6,1.5c-0.1,0.1-0.2,0.2-0.2,0.2C17.2,1.9,17,2,16.9,2.2c-0.2,0.3-0.5,0.5-0.8,0.7
                          c-0.9,0.9-1.7,1.7-2.4,2.7c-0.8,1.5-0.8,1.5,0.1,4.9c0.1,0.3,0.2,0.6,0.2,0.9c0.1,0.5,0.3,1.1,0.4,1.6c0.1,0.5,0.3,1.1,0.4,1.6
                          c0,0.1,0.1,0.2,0.1,0.3c0.1,0.5,0.3,1.1,0.4,1.6c0.2,0.8,0.4,1.6,0.7,2.4c0.2,0.6,0.4,1.1,0.5,1.7c-0.6,1.6-0.6,1.6-2,2.8
                          c-0.2,0.2-0.5,0.4-0.7,0.6c-0.3,0.3-0.6,0.6-1,0.9c-0.8,0.7-2.1,1.6-2.3,2.8c0.1,0.6,0.3,0.9,0.7,1.4c0.1,0.1,0.3,0.3,0.4,0.4
                          c0.2,0.2,0.3,0.3,0.5,0.5c0.2,0.2,0.5,0.5,0.7,0.7c0.6,0.6,1.3,1.3,1.9,1.9c0.1,0.1,0.2,0.2,0.3,0.3c0.4,0.4,0.7,0.5,1.2,0.5
                          c0.8-0.1,0.8-0.1,1.5-0.9c0.1-0.1,0.2-0.2,0.3-0.3c0.2-0.2,0.3-0.3,0.5-0.5c0.1-0.1,0.3-0.3,0.4-0.4c0.1,0.3,0.2,0.6,0.3,0.9
                          c0,0.2,0.1,0.3,0.1,0.5c0.1,0.3,0.1,0.6,0.2,0.9c0.1,0.2,0.1,0.4,0.2,0.6c0.1,0.4,0.2,0.9,0.3,1.3c0.1,0.4,0.2,0.9,0.3,1.3
                          c0.1,0.2,0.1,0.4,0.2,0.7c0,0.2,0,0.2,0.1,0.3c0.2,0.7,0.4,1.5,0.6,2.2c0.2,0.6,0.3,1.2,0.5,1.9c0,0.1,0.1,0.3,0.1,0.4
                          c0.1,0.2,0.1,0.5,0.2,0.7c0.1,0.2,0.1,0.4,0.2,0.6c0.1,0.5,0.1,0.5-0.2,0.8c-0.1,0.1-0.2,0.2-0.3,0.3c-0.1,0.1-0.2,0.1-0.3,0.2
                          c-0.9,0.7-0.9,0.7-2.8,2.7c-0.2,0.2-0.4,0.4-0.6,0.6c-0.4,0.4-0.7,0.7-1.1,1.1c-0.6,0.6-1.2,1.2-1.8,1.8c-1.3,1.3-2.5,2.5-3.8,3.9
                          c-0.1,0.1-0.2,0.3-0.4,0.4c-1.3,1.5-2.6,2.9-3.9,4.5C3.7,64,0.3,68.6,0.8,74.2C1,75.4,1.6,76.3,2.6,77c5,3.5,12.2-2.2,16-5.3
                          c0.2-0.1,0.4-0.3,0.5-0.4c0.9-0.7,1.8-1.5,2.6-2.2c0.4-0.4,0.8-0.7,1.2-1.1c0.8-0.7,1.6-1.4,2.3-2.2c0.6-0.6,1.1-1.1,1.7-1.7
                          c0.5-0.4,0.9-0.8,1.3-1.3c1-1.2,1-1.2,2.2-2.2c0.8-0.8,1.6-1.5,2.3-2.4c0.3-0.3,0.5-0.6,0.8-0.9c0.1-0.1,0.2-0.2,0.2-0.2
                          c0.2-0.2,0.5-0.5,0.7-0.7c0.6-0.1,0.6-0.1,1.3,0.1c0.1,0,0.2,0.1,0.4,0.1c0.4,0.1,0.8,0.2,1.2,0.3c0.3,0.1,0.6,0.2,0.9,0.2
                          c0.6,0.2,1.2,0.3,1.8,0.5c0.9,0.2,1.8,0.5,2.7,0.7c1.5,0.4,3,0.8,4.5,1.2c-0.1,0.1-0.3,0.3-0.4,0.4c-0.2,0.2-0.3,0.3-0.5,0.5
                          c-0.1,0.1-0.2,0.2-0.3,0.3c-0.5,0.5-0.8,0.9-0.8,1.6c0.1,0.7,0.3,1,0.9,1.5c0.1,0.1,0.2,0.2,0.3,0.3c0.2,0.2,0.4,0.4,0.5,0.6
                          c0.3,0.3,0.6,0.6,0.9,0.9c0.2,0.2,0.4,0.4,0.6,0.6c0.1,0.1,0.2,0.2,0.3,0.3c0.7,0.7,1.4,1.5,2.4,1.6c0.5-0.1,0.6-0.1,1-0.4
                          c0.1-0.1,0.2-0.2,0.3-0.3c0.1-0.1,0.2-0.2,0.3-0.3c0.2-0.2,0.5-0.5,0.7-0.7c0.4-0.4,0.7-0.8,1.1-1.1c0.4-0.4,0.7-0.8,1.1-1.1
                          c0.1-0.1,0.2-0.2,0.4-0.4c0.1-0.1,0.2-0.2,0.3-0.3c0.1-0.1,0.2-0.2,0.3-0.3c0.3-0.3,0.3-0.3,0.6-0.5c0.4-0.3,0.5-0.3,1-0.3
                          c0.2,0,0.3,0.1,0.4,0.1c0.2,0,0.3,0.1,0.5,0.1c0.4,0.1,0.7,0.2,1.1,0.3c0.4,0.1,0.8,0.2,1.2,0.3c0.5,0.1,1,0.3,1.5,0.4
                          c0.1,0,0.2,0.1,0.3,0.1c1,0.3,2,0.5,2.9,0.8c0.3,0.1,0.3,0.1,0.6,0.1c0.6,0.1,1.1,0.3,1.7,0.4c0.3,0.1,0.3,0.1,0.5,0.1
                          c0.8,0.2,1.5,0.4,2.3,0.4c1.9-0.1,2.9-1,4.2-2.3c0.3-0.3,0.5-0.5,0.8-0.8c0.2-0.2,0.3-0.3,0.5-0.5c0.1-0.1,0.2-0.2,0.2-0.2
                          c0.6-0.6,1.2-1.2,1.3-2c0-0.1,0-0.2,0-0.3C78,59.1,78,58.9,78,58.8z M65,20.4c0.1-0.1,0.1-0.2,0.2-0.3c0.1-0.1,0.1-0.2,0.2-0.3
                          c0.1-0.1,0.2-0.2,0.2-0.3c0.5-0.7,1-1.4,1.5-2c0.4,0.2,0.7,0.4,1.1,0.6c0.2,0.1,0.2,0.1,0.5,0.3c0.9,0.6,1.9,1.1,2.8,1.7
                          c0.1,0.1,0.2,0.1,0.3,0.2c0.3,0.2,0.7,0.4,1,0.6c0.2,0.1,0.3,0.2,0.5,0.3c0.2,0.1,0.3,0.2,0.5,0.3c0.5,0.3,0.9,0.5,1.2,0.9
                          c-3.5,1.7-3.5,1.7-7.5-0.2c-0.2-0.1-0.4-0.2-0.5-0.2c-0.3-0.1-0.3-0.1-0.5-0.2c-0.3-0.1-0.3-0.1-0.5-0.2c-0.4-0.2-0.4-0.2-0.8-0.4
                          C64.8,20.7,64.8,20.7,65,20.4z M54.9,5.5C55,5,55.3,4.7,55.7,4.3c0.2-0.2,0.4-0.3,0.6-0.5c0.1-0.1,0.2-0.1,0.2-0.2
                          c0.7,1.2,1.5,2.4,2.2,3.6c0.1,0.2,0.2,0.4,0.3,0.5c0.8,1.2,1.5,2.5,2.2,3.8c-0.4,0.3-0.7,0.5-1.1,0.8c-0.2,0.1-0.3,0.2-0.5,0.3
                          c-0.2,0.1-0.3,0.2-0.5,0.3c-0.1,0.1-0.3,0.2-0.4,0.3c-0.4,0.2-0.6,0.5-1,0.5C56.3,11.7,54.9,8.1,54.9,5.5z M13.1,72.1
                          c-0.6,0.4-0.9,0.4-1.6,0.4c-1.7-0.3-3.1-1.1-4.1-2.5c-0.8-1.2-1.4-2.4-1.1-3.9c0.3-0.5,0.4-0.6,1-0.8c0.6,0,0.9,0,1.3,0.4
                          c0.1,0.3,0.2,0.7,0.3,1c0.3,1.1,0.8,1.9,1.8,2.5c0.5,0.2,1,0.3,1.5,0.4c0.6,0.2,0.8,0.4,1.1,0.9C13.4,71.3,13.3,71.6,13.1,72.1z
                           M21.1,25.6c-0.3,0.3-0.5,0.5-0.8,0.8c-0.4,0.4-0.9,0.9-1.3,1.3c-0.3,0.3-0.6,0.6-0.8,0.8c-0.1,0.1-0.3,0.3-0.4,0.4
                          c-0.1,0.1-0.3,0.3-0.4,0.4c-0.1,0.1-0.2,0.2-0.4,0.4c-0.1,0.1-0.2,0.2-0.3,0.3c-0.4,0.3-0.5,0.3-0.9,0c-0.1-0.1-0.2-0.2-0.4-0.4
                          c-0.3-0.3-0.7-0.7-1-1c-0.1-0.1-0.2-0.2-0.2-0.3c-0.1-0.2-0.3-0.3-0.4-0.5c0.7-0.7,1.3-1.3,2-2c0.3-0.3,0.5-0.5,0.8-0.8
                          c0.4-0.4,0.8-0.8,1.2-1.2c0.1-0.1,0.3-0.3,0.4-0.4c0.7-0.7,1.4-1.4,2.2-1.9c0.6-0.1,1,0,1.5,0.3c0.4,0.3,0.6,0.6,0.7,1.1
                          c0,0.7,0,1.1-0.4,1.6c-0.1,0.1-0.2,0.2-0.3,0.3c-0.1,0.1-0.2,0.2-0.4,0.4C21.4,25.4,21.3,25.5,21.1,25.6z M32.3,35.3
                          c-0.1,0.1-0.2,0.2-0.3,0.2c-0.1,0.1-0.2,0.2-0.3,0.3c-0.2,0.2-0.5,0.4-0.7,0.6c-0.1,0.1-0.2,0.2-0.4,0.3c-1,0.9-1.9,1.8-2.9,2.6
                          c-1,0.9-1.9,1.8-3,2.7c-0.3-0.3-0.4-0.6-0.5-1.1c0-0.2,0-0.2-0.1-0.3c-0.1-0.3-0.2-0.7-0.3-1c-0.1-0.3-0.1-0.5-0.2-0.8
                          c-0.1-0.5-0.3-1-0.4-1.6c-0.2-0.7-0.3-1.4-0.5-2c-0.1-0.5-0.3-1.1-0.4-1.6c-0.1-0.3-0.1-0.5-0.2-0.8c-0.1-0.4-0.2-0.7-0.3-1.1
                          c0-0.1-0.1-0.2-0.1-0.3c-0.1-0.5-0.2-1-0.3-1.5c-0.1-0.6-0.1-0.8,0.2-1.3c0.2-0.2,0.5-0.4,0.7-0.6c0.1-0.1,0.2-0.1,0.2-0.2
                          c0.6-0.5,1.1-1,1.6-1.6c1-1.2,1.1-2.5,0.9-4c-0.3-1.2-1-1.9-2-2.6c-1-0.5-2.1-0.6-3.2-0.4c-0.2,0-0.2,0-0.4,0.1
                          c-0.1,0-0.3,0.1-0.4,0.1c-0.1,0-0.2,0-0.3,0.1c-0.3-1.2-0.6-2.3-0.9-3.5c-0.2-0.6-0.3-1.2-0.5-1.8c-0.2-0.6-0.3-1.2-0.5-1.7
                          c-0.1-0.2-0.1-0.4-0.2-0.7c-0.4-1.4-0.8-2.8-0.9-4.2c0.2-0.8,0.6-1.3,1.2-1.9c0.1-0.1,0.2-0.2,0.3-0.3c0.5-0.5,1-1,1.7-1.3
                          c2.3,5.2,4.6,10.4,6.9,15.7c1,2.2,2,4.5,3,6.7c0.6,1.4,1.2,2.7,1.8,4.1c0.1,0.1,0.1,0.2,0.2,0.3c0.2,0.5,0.5,1.1,0.7,1.6
                          c0,0.1,0.1,0.2,0.1,0.3c0.1,0.2,0.2,0.4,0.2,0.5c0.2,0.5,0.4,0.9,0.5,1.4C32.6,35,32.6,35,32.3,35.3z M56,59.7
                          c-0.1,0.1-0.3,0.3-0.4,0.4c-0.4,0.4-0.8,0.8-1.3,1.2c-0.3,0.3-0.6,0.6-0.9,0.9c-0.6,0.5-1.1,1.1-1.7,1.6c-0.1,0.1-0.2,0.2-0.4,0.4
                          c-0.7,0.6-0.7,0.6-1.4-0.1c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.2-0.2-0.3-0.3c-0.3-0.3-0.6-0.7-0.8-1.1
                          c0.6-0.6,1.2-1.2,1.7-1.7c0.2-0.2,0.5-0.5,0.7-0.7c0.3-0.3,0.7-0.7,1-1c0.1-0.1,0.2-0.2,0.3-0.3c0.5-0.5,1-1,1.5-1.5
                          c0.1-0.1,0.2-0.2,0.3-0.3c0.3-0.3,0.3-0.3,0.6-0.5c0.7-0.1,1.2-0.1,1.8,0.3c0.3,0.4,0.6,0.7,0.7,1.2C57.1,58.6,56.7,59.1,56,59.7z
                           M73.9,60.7c-0.1,0.1-0.2,0.2-0.3,0.3c-0.4,0.4-0.8,0.8-1.2,1.2c-1,0.7-1.8,0.6-3,0.3c-0.2,0-0.4-0.1-0.6-0.1
                          c-0.3-0.1-0.7-0.2-1-0.3c-0.2-0.1-0.5-0.1-0.7-0.2c-0.6-0.2-1.2-0.3-1.8-0.5c-0.6-0.2-1.3-0.3-1.9-0.5c-1.4-0.4-2.7-0.7-4.1-1.1
                          c0.1-0.6,0.3-1.1,0.4-1.7c0-1.4-0.2-2.5-1.3-3.6c-0.9-0.8-1.8-1.1-3.1-1.1c-1.7,0-2.8,0.8-3.9,2c-0.2,0.2-0.4,0.5-0.7,0.7
                          c-0.2,0.2-0.2,0.2-0.3,0.3c-0.3,0.3-0.3,0.3-0.6,0.6c-0.4,0.1-0.7,0.2-1.2,0.1c-0.2,0-0.3-0.1-0.5-0.1c-0.3-0.1-0.6-0.1-0.9-0.2
                          c-0.2-0.1-0.4-0.1-0.6-0.2c-0.5-0.1-1.1-0.3-1.6-0.4c-0.4-0.1-0.9-0.2-1.3-0.3c-2.3-0.6-4.7-1.2-7-1.9c0.2-0.4,0.4-0.7,0.7-1.1
                          c0.1-0.1,0.2-0.2,0.3-0.3c0.9-1,0.9-1,1.9-2c0.5-0.5,0.9-1,1.4-1.5c0.5-0.6,1-1.1,1.5-1.7c0.1-0.1,0.2-0.2,0.2-0.3
                          c0.3-0.4,0.7-0.8,1.1-1.1c0.4,0,0.8,0.1,1.2,0.3c0.1,0,0.2,0.1,0.3,0.1c0.4,0.2,0.7,0.3,1.1,0.5c0.3,0.1,0.5,0.2,0.8,0.4
                          c0.6,0.2,1.1,0.5,1.7,0.7c0.8,0.4,1.7,0.7,2.5,1.1c1.1,0.5,2.2,1,3.3,1.4c1.7,0.7,3.3,1.5,5,2.2c0.5,0.2,1.1,0.5,1.6,0.7
                          c0.1,0.1,0.3,0.1,0.4,0.2c0.8,0.4,1.7,0.7,2.5,1.1c3.4,1.5,6.8,3,10.3,4.5c0.1,0.1,0.2,0.1,0.3,0.2C74.5,60.2,74.5,60.2,73.9,60.7z
                          "
                            />
                        </g>
                        <g>
                            <path
                                ref={pathRef}
                                className="d_mob_none"
                                opacity="1"
                                d="M1672.17 231.675C1658.17 240.675 1615.9 242.675 1558.9 178.675C1487.64 98.6749 1423.69 138.175 1425.52 188.675C1427.35 239.175 1511.85 206.173 1455.67 145.673C1399.48 85.1733 1290.69 40.5169 1170.65 143.132C908 367.634 121 461.632 121 850.132C121 1315.63 1523.63 1362.13 1523.63 1850.63C1523.63 2378.13 267.629 2028.13 294.129 2598.13C325.954 3282.67 1504.61 2562.41 1532.11 3177.41C1560.61 3814.85 229.654 3482.41 275.106 3985.41C320.106 4483.41 1586.11 4393.41 1985.61 4110.91"
                                stroke="#3B3064"
                                stroke-width="1.5"
                                stroke-dasharray="6 6"
                            />
                        </g>
                    </svg>
                )}
            </div>
        </div>
    );
};

export default AirAnimPage;