import { useEffect, useState, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import comon from "@/styles/comon.module.scss";
import csr from "@/styles/csr.module.scss";
import Modal from "@mui/material/Modal";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import "swiper/css/free-mode";
import "swiper/css/navigation";
import "swiper/css/thumbs";

// import required modules
import { FreeMode, Navigation, Thumbs } from "swiper/modules";
import LocomotiveScroll from "locomotive-scroll";

const ImageBlockSlide = ({ extraBlock = false, cardData }) => {
  const [selectedItem, setSelectedItem] = useState(null);
  const images = [
    "/images/csr_1.jpg",
    "/images/csr_2.jpg",
    "/images/csr_3.jpg",
    "/images/csr_4.jpg",
    "/images/csr_5.jpg",
    "/images/csr_6.jpg",
    "/images/csr_7.jpg",
    "/images/csr_8.jpg",
    "/images/csr_9.jpg",
    "/images/csr_10.jpg",
    "/images/csr_11.jpg",
    "/images/csr_12.jpg",
  ];

  // const imageSlides = cardData.sliderImg
  const imageSlides = [
    "/images/Imageblock_slide1.png",
    "/images/csr_2.jpg",
    "/images/Imageblock_slide1.png",
    "/images/Imageblock_slide1.png",
    "/images/Imageblock_slide1.png",
    "/images/Imageblock_slide1.png",
    "/images/Imageblock_slide1.png",
    "/images/Imageblock_slide1.png",
    "/images/Imageblock_slide1.png",
  ];
  const [open, setOpen] = useState(false);
  const handleOpen = (data) => {
    setSelectedItem(data);
    window.scrollTo({
      top: 100, // Scrolls 100px down
      behavior: "smooth", // Smooth scrolling effect
    });
    setOpen(true);
  };

  const handleClose = () => {
    setSelectedItem(null);
    setOpen(false);
  };
  const [thumbsSwiper, setThumbsSwiper] = useState(null);

  // const [thumbsSwiper, setThumbsSwiper] = useState();


  return (
    <div>
      <section className={`${comon.pb_50}`}>
        <div className={`${comon.wrap}`}>
          <ul className={`${csr.block_ul}`}>
            {cardData.map((data, index) => (
              <li key={index}  >
                <div
                  style={{ aspectRatio: "1 / 1" }}
                  className={`${comon.w_100} ${comon.p_relative} ${comon.hover_visible_sec} ${comon.image_hover} ${comon.p_relative}`}
                >
                  <Image
                    src={data.image}
                    fill
                    className={comon.img}
                    style={{ objectFit: "contain" }}
                    alt=""
                    quality={100}
                  />
                  {extraBlock == true ? (
                    <div className={comon.hover_visible}>
                      <div className={`${comon.hover_visible_container} ${comon.mob_hover_container}`}>
                        <h4>{data.text}</h4>
                        <p>{data.description}</p>
                        <span className={comon.hover_popup_btn} onClick={() => handleOpen(data)} >Check Location</span>
                      </div>
                    </div>
                  ) : (
                    ""
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      </section>

      <div>
        <Modal
          className="ModalBlockSlideSection"
          open={open}
          data-lenis-prevent="true"
          onClose={() => {
            setThumbsSwiper(false);
            setOpen(false);
          }}
        >
          <Box className={`${comon.popup_container} ${comon.min_height}`}>
            <button
              className={comon.popup_close}
              onClick={() => {
                setThumbsSwiper(false);
                setOpen(false);
              }}
            >
              <Image
                src="/images/close_btn.svg"
                height={30}
                width={30}
                alt="Close"
                quality={100}
              />
            </button>




            <Swiper
              spaceBetween={10}
              onSwiper={setThumbsSwiper}
              // navigation
              navigation={{
                prevEl: '.prev_btn',
                nextEl: '.next_btn'
              }}
              thumbs={{ swiper: thumbsSwiper && !thumbsSwiper.destroyed ? thumbsSwiper : null }}
              modules={[FreeMode, Navigation, Thumbs]}
              className="mySwiper2 ImageBlockSlide"
            >
              {selectedItem && selectedItem.gallerys.map((url, index1) => (
                <SwiperSlide key={index1}>
                  <div className={comon.swiper_image}>
                    <img src={url} alt={`Slide ${index1 + 1}`} />
                  </div>
                </SwiperSlide>
              ))}

              <div className="thumb_swiper_slide_btn">
                <div className="prev_btn swiper_btn ">
                  <Image
                    src="/images/next_icon1.svg"
                    height={30}
                    width={30}
                    alt="next"
                    quality={100}
                  />
                </div>
                <div className="next_btn swiper_btn">
                  <Image
                    src="/images/next_icon1.svg"
                    height={30}
                    width={30}
                    alt="prev"
                    quality={100}
                  />
                </div>
              </div>
            </Swiper>

            {/* -----thumbs------ */}
            <Swiper
              onSwiper={setThumbsSwiper}
              spaceBetween={10}
              slidesPerView={3}
              freeMode={true}
              watchSlidesProgress={true}
              modules={[FreeMode, Navigation, Thumbs]}
              className="mySwiper ImageBlockSlideThumbs"
              breakpoints={{
                500: {
                  slidesPerView: 4,
                },
              }}
            >
              {selectedItem && selectedItem.gallerys.map((url, index2) => (
                <SwiperSlide key={index2}>
                  <div className={comon.swiper_image_thumbs}>
                    <img src={url} alt={`Thumbnail ${index2 + 1}`} />
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          </Box>
        </Modal>
      </div>
    </div>
  );
};

export default ImageBlockSlide;
