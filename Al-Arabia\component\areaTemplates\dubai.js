import React from "react";
import CountUp from "@/component/CountUp";
import Marquee from "@/component/Marquee";
import SliderSection from "@/component/SliderSection";
import TextBlock from "@/component/TextBlock";
import StartCampaign from "@/component/StartCampaign";
import ContactSection from "@/component/ContactSection";
import comon from "@/styles/comon.module.scss";
import SliderAnim from "@/component/SliderAnimMezah";
import SliderBannerText from "@/component/SliderBannerText";
import HeadMarquee from "@/component/HeadMarquee";
import SliderAnimDubai from "@/component/SliderAnimDubai";
import Head from "next/head";
import InnerBanner from "@/component/InnerBanner";
import parse from 'html-react-parser';
import { fetchByPost } from "@/lib/api/pageApi";

const dubai = ({ pageData }) => {

  const formattedContent = pageData.acf.slider_details_.map((item) => {
		if (!item.acf_fc_layout) return "";
		if (item.acf_fc_layout === "small_bold_text") {
		  return `<b>${item.text_bold}</b>`;
		} else if (item.acf_fc_layout === "regular_big_text") {
		  return item.text__reg;
		}
		return "";
	  });
  return (

    <div>
      {/* <Head>
        <title>Dubai</title>
        <meta name="description" content="Generated by create next app" />
      </Head> */}
      <HeadMarquee />
      <div className={`  `}>
        {pageData.acf.banner_details.description_field ? (

          <SliderBannerText
            title={pageData.acf.banner_details.title}
            paragraph={pageData.acf.banner_details.description_}
            details={pageData.acf.banner_details}
            className={'width_870'}
            bgColor={true}
            extra_height_class={true}
            full_height_banner={true}
          />
        ) : (
          <InnerBanner showLink={false} title={pageData.acf.banner_details.title} details={pageData.acf.banner_details}
            full_height_banner={true}
          />
        )}


        <SliderAnimDubai
          images={pageData.acf.gallery_details.gallery.map((item) => ({
            img: item.image,
            description: item.title,
            condition: item.text_or_image,
            titleimg:item.Image_slider
          }))}
          bgColor={pageData.acf.gallery_details.select_background_color}
          textColor={pageData.acf.gallery_details.text_color}
          singlecolor ={pageData.acf.gallery_details.select_single_slide_color}
          arrowColor ={pageData.acf.gallery_details.select_arrow_color}
          gradient={pageData.acf.gallery_details.gradient[0]}
          bgColor2={pageData.acf.gallery_details.select_background_color_2}
          />
      </div>


      <CountUp counterData={pageData.acf.counter} extraClass={true} 
      bgColor={pageData.acf.select_background_color_counter} textColor={pageData.acf.text_color_counter}  innerBg={pageData.acf.inner_box_bg}/>

      <Marquee
        data-aos="fade-up"
        data-aos-duration="1000"
        content={formattedContent}
        // speed={100000}
        speed={9999}
        direction="left"
        bgColor={pageData.acf.select_background_color_text} 
        textColor={pageData.acf.text_color_text}
      />
      <SliderSection
        images={pageData.acf.slider.map((item) => ({
          img: item,
          description: ''
        }))}
        title={pageData.acf.title}
        bgColor={pageData.acf.select_background_color_slider} 
        textColor={pageData.acf.text_color_text_slider}
      />
      {pageData.acf.details_.show_section_in_frontend && (
        <TextBlock content={pageData.acf.details_} />
      )}

      
      {pageData?.acf?.choose_builder === "Plain Black BG" ? (
        <StartCampaign bgColor="#ECECEC" darkColor={true} plainBlack={true} />
      ) : pageData?.acf?.choose_builder === "Black BG with Lines" ? (
        <StartCampaign bgColor="#ECECEC" darkColor={true} />
      ) : (
        <StartCampaign />
      )}



      <section className={`${comon.pt_50}  
      ${
        pageData?.acf?.choose_style === "Black" ? comon.black_bg : ""
      }
      ${
        pageData?.acf?.choose_style === "Black with Texture" ? comon.black_bg : ""
      }
      `}  
         
      style={
        pageData?.acf?.choose_style == "Black with Texture"
          ? undefined
          : { backgroundImage: "none" }
      }
      >
        <ContactSection
          showAutoMargin={false}
          className={
            pageData?.acf?.choose_style === "Black" || pageData?.acf?.choose_style === "Black with Texture"
              ? "white_text"
              : ""
          }
          classNameSecond={
            pageData?.acf?.choose_style === "Black" || pageData?.acf?.choose_style === "Black with Texture"
              ? "form_white"
              : ""
          }
          whiteBtn={
            pageData?.acf?.choose_style === "Black" || pageData?.acf?.choose_style === "Black with Texture"
          }
        />
      </section>
    </div>
  );
};

export default dubai;
