"use client";
import React, { useState, useEffect, useRef } from "react";
import Head from "next/head";
import Link from "next/link";
// ---buttion-import--start
import But<PERSON> from "@/component/button/Buttons";
import Marquee from "@/component/Marquee";
import rtl from "@/styles/rtl.module.scss";
// ---buttion-import--end

import Image from "next/image";
import InnerBanner from "@/component/InnerBanner";
import Parallax from "@/component/Paralax";
import Map from "@/component/contactmap";
// Import Swiper styles
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/scrollbar";
import { Scrollbar, Autoplay, Navigation } from "swiper/modules";
import comon from "@/styles/comon.module.scss";
import project from "@/styles/project.module.scss";
import buttion from "@/styles/buttion.module.scss";
import DynamicImage from "@/component/DynamicImage";
import AOS from "aos";
import "aos/dist/aos.css";
import HeadMarquee from "@/component/HeadMarquee";
import SliderSectionDark from "@/component/SliderSectionDark";
import ContactSection from "@/component/ContactSection";
import parse from "html-react-parser";
import "react-tooltip/dist/react-tooltip.css";
import SliderNew from "@/component/SliderNew";
import StartCampaign from "@/component/StartCampaign";
import { useRouter } from "next/router";
import { fetchByPost } from "@/lib/api/pageApi";
// -----slider--mapping------start
export default function Home({ pageData }) {
	const [isClient, setIsClient] = useState(false);

	const [activeIndex, setActiveIndex] = useState(0);
	const swiperRef = useRef(null);

	const [isMobile, setIsMobile] = useState(false);

	useEffect(() => {
		// Function to check if the screen is mobile size
		const checkScreenSize = () => {
			setIsMobile(window.innerWidth <= 767); // Mobile breakpoint
		};

		// Check screen size on load
		checkScreenSize();

		// Add event listener for resize
		window.addEventListener("resize", checkScreenSize);

		// Cleanup event listener on unmount
		return () => {
			window.removeEventListener("resize", checkScreenSize);
		};
	}, []);


	const [sliderIndex, setSliderIndex] = useState("");

	console.log(" ", sliderIndex);

	// Initialize AOS and set client-side state after the component mounts
	useEffect(() => {
		setIsClient(true);
		AOS.init();
	}, []);

	const originalLogoArray = pageData.acf.gallery_details.gallery?.map(item => item.title_logo);
	const logoArray = [...originalLogoArray, ...originalLogoArray, ...originalLogoArray];


	//   useEffect(() => {
	// 	console.log("uuuuuuuuuuuuuuuu", logoArray)
	//   }, logoArray)


	const galleryItems = pageData.acf.gallery_details.gallery;
	const images =
		galleryItems.length < 6
			? [...galleryItems, ...galleryItems].map((item) => ({
				img: item,
				description: "",
			}))
			: galleryItems.map((item) => ({
				img: item,
				description: "",
			}));

	if (!isClient) {
		return null; // Prevent SSR mismatch by rendering nothing until the component is mounted on the client
	}

	return (
		<>
			<HeadMarquee />

			<section
				className={`${comon.no_banner} ${project.project2_slider_section} ${comon.pb_50}  project_second_slider_section`}
			>
				<div className={`${comon.wrap}`}>
					<div
						className={`${project.project_title} ${project.project_title_maleem} ${comon.d_flex_wrap}`}
					>
						<div class={`${comon.w_50} ${comon.pr_10}`}>
							<div
								class={`${comon.title_30} ${comon.mb_10} ${comon.white_color}`}
							>
								<h3>{parse(pageData.acf.banner_details.title)}</h3>
								<p>{parse(pageData.acf.banner_details.description_)}</p>
							</div>
						</div>

						{pageData.acf.description.project_switch_logos.logo_1 && pageData.acf.description.project_switch_logos.logo_2 && (
						<div className={`${comon.w_50}  ${comon.d_flex_wrap}`}>
							<ul className={`${project.project_tab} ${rtl.project_tab}`}>
								{pageData.acf.description.project_switch_logos.logo_1 &&
								 pageData.acf.description.project_switch_logos.link_1 && (
								<li>
									<Link href={pageData.acf.description.project_switch_logos.link_1}>
										<Image
											src={pageData.acf.description.project_switch_logos.logo_1}
											width={74}
											height={42}
											alt="button image"
											quality={100}
										/>
									</Link>
								</li>
								)}
								{pageData.acf.description.project_switch_logos.logo_2 &&
								 pageData.acf.description.project_switch_logos.link_2 && (
								<li className={`${project.active}`}>
									<Link href={pageData.acf.description.project_switch_logos.link_2}>
										<Image
											src={pageData.acf.description.project_switch_logos.logo_2}
											width={70}
											height={42}
											alt="button image"
											quality={100}
										/>
									</Link>
								</li>
								)}
							</ul>
						</div>
						)}
					</div>

					<div
						className={`${comon.p_relative} ${comon.w_100} ${project.project_partner_slider} ${comon.pt_80}`}
						data-aos="fade-in"
						data-aos-duration="1000"
					>
						<h3 className={`${comon.mb_20}`}>
							{parse(pageData.acf.group_title)}
						</h3>

						{isMobile == false ? (
							<ul className={`${project.project_partner_logo_sec} `}>
								{pageData.acf.features && pageData.acf.features.map((item, index) => {
									return (
										<li key={index} style={{ height: "70px" }}>
											<div style={{ height: "100%" }}>
												<Image
													src={item.logo}
													width={170}
													height={70}
													alt=""
													style={{
														height: "100%",
														display: "block",
														width: "100%",
														objectFit: "scale-down",
													}}
													quality={100}
												/>
											</div>
										</li>
									);
								})}
							</ul>
						) : (
							<Swiper
								slidesPerView={3}
								spaceBetween={20}
								speed={1200}
								navigation={{
									prevEl: ".custom_prev_4",
									nextEl: ".custom_next_4",
								}}
								modules={[Navigation, Autoplay]}
								loop={true}
								autoplay={{
									delay: 2500,
									disableOnInteraction: false,
								}}
								breakpoints={{
									580: {
										slidesPerView: 2,
										spaceBetween: 20,
									},
									768: {
										slidesPerView: 4,
										spaceBetween: 40,
									},
									1024: {
										slidesPerView: 4,
										spaceBetween: 60,
									},
								}}
								className="project_partner project_partner_slider"
							>
								{pageData.acf.features && pageData.acf.features.map((item, index) => {
									return (
										<SwiperSlide key={index}>
											<Image
												src={item.logo}
												width={170}
												height={70}
												alt=""
												style={{
													height: "100%",
													display: "block",
													width: "100%",
													objectFit: "scale-down",
												}}
												quality={100}
											/>
										</SwiperSlide>
									);
								})}
							</Swiper>
						)}
					</div>

					<div
						className={`${project.project_slider} ${project.maalem_page} ${project.min_height_unset} ${comon.pt_20}`}
						data-aos="fade-in"
						data-aos-duration="1000"
					>
						<SliderNew
							sliderData={pageData.acf.details_}
							className={"maleem"}
						/>
					</div>
				</div>
			</section>

			<Marquee
				data-aos="fade-in"
				data-aos-duration="1000"
				content={pageData.acf.details.slider_text?.map((item) => item.text)}
				// speed={100000}
				speed={9999}

				direction="left"
				font_weight={true}
				className="white_marquee"
			/>

			<section
				className={`${project.dark_slider_section} ${comon.black_bg} ${comon.pt_80} ${comon.pb_80} `}
			>
				<div className={`${comon.wrap} ${comon.d_flex_wrap}`}>
					<div
						className={`${comon.h1} ${comon.w_100} ${comon.white_color} ${comon.the_gallery_sec}`}
						data-aos="fade-in"
						data-aos-duration="1000"
					>
						<h3 className={`${comon.text_center} ${comon.text_capitalize}`}>
							{parse(pageData.acf.gallery_details.gallery_title)}
						</h3>
						<h5 className={`${comon.mt_20} ${project.title_with_img}`}>
							{parse(pageData.acf.gallery_details.gallery_description)}{" "}
							{/* <span>
								<Image
									src={pageData.acf.gallery_details.title_logo}
									width={264}
									height={15}
									alt="button image"
								/>
							</span> */}
							<div className={project.logo_sec}>
								<Image
									// src="/images/zaha-hadid.png"
									src={logoArray[sliderIndex]}
									width={264}
									height={15}
									alt="button image"
									quality={100}
								/>
							</div>

						</h5>
					</div>
				</div >

				<SliderSectionDark
					images={pageData.acf.gallery_details.gallery?.map((item) => ({
						img: item.image,
						description: "",
					}))}
					activeSlider={(i) => setSliderIndex(i)}
				/>
			</section >
			<StartCampaign bgColor={"#ECECEC"} darkColor={true} />

			<section className={`${comon.pt_60} ${comon.pb_10} ${comon.black_bg} `}>
				<ContactSection
					title="Request for Quotation"
					paragraph="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore."
					button="Request a Quote"
					showAutoMargin={false}
					className="white_text"
					classNameSecond="form_white"
					whiteBtn={true}
				/>
			</section>
		</>
	);
}
