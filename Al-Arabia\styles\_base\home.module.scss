@import "variable", "base", "mixin";

.home_txt_01 {
  width: 42.5%;

  h1 {
    color: #fff;
    font-size: 2.5rem;
    font-weight: unset;
    font-family: var(--aller_bd);
    line-height: 2rem;

    span {
      font-family: var(--aller_lt);
      font-weight: unset;
      font-size: 1.813rem;
    }
  }

  @media #{$media-700} {
    width: 100%;
    text-align: center;
  }


}

.home_txt_02 {
  width: 57%;

  @media #{$media-820} {
    width: 100%;
    margin-top: 25px;
  }
}

.home_txt_counder {
  display: flex;
  flex-wrap: wrap;
  margin-left: -8%;
  margin-right: -8%;

  li {
    position: relative;
    padding-left: 8%;
    padding-right: 8%;
    width: 33%;

    h2 {
      color: #fff;
      font-size: 3.125rem;

    }

    p {
      font-size: 16px;
      color: #fff;
      font-weight: 200;
    }

    &::after {
      content: "";
      display: block;
      width: 1px;
      height: 170px;
      background: #fff;
      position: absolute;
      right: 0;
      top: -35px;
      -moz-transform: rotate(28deg);
      -webkit-transform: rotate(28deg);
      -o-transform: rotate(28deg);
      -ms-transform: rotate(28deg);
      transform: rotate(28deg);

      @media #{$media-820} {
        height: 130px;
        top:-25px;
      }

      @media #{$media-700} {
        height: 130px;
        top: -15px;
        display: none;

      }


    }

    @media #{$media-700} {
      width: 100%;
      text-align: center;
      border-top: solid 1px #7f6fcf;
      padding-top: 15px;
      margin-top: 15px;
    }
  }

  &> :last-child {
    &::after {
      display: none;
    }
  }


  @media #{$media-700} {
    margin-left: 0;
    margin-right: 0;
  }

}



.corporate_block {
  background: rgba(103, 88, 182, 0.9);
  padding: 5%;
  width: 39%;



  h3 {
    font-family: var(--aller_rg);
    @include rem(28);
    color: #fff;
    font-weight: 400;
    font-weight: unset;
  }

  @media #{$media-700} {
    width: 100%;
  }

}

.slider_block {
  width: 45% !important;

  @media #{$media-700} {
    width: 75% !important;
  }

}

.slider_block_txt {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 100;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  padding: 5%;

  h4 {
    font-family: var(--aller_lt);
    @include rem(28);
    color: #fff;
    font-weight: 400;
  }
}

.bottom_row_corporate {
  border-top: solid 1px #fff;
  padding-top: 15px;

  p {
    font-size: 12px;
    color: #d7cfff
  }

  h6 {
    font-size: 16px;
    color: #fff;
    font-weight: 300;
    margin-top: 25px;
    line-height: 26px
  }
}

.insight_left_block {
  width: 32%;

  @media #{$media-700} {
    width: 100%;
  }
}

.insight_right_block {
  width: 62%;

  @media #{$media-700} {
    width: 100%;
  }

}

.news_list_ul {
  margin: 0;
  padding: 0;

  li {
    padding-left: 3%;
    padding-right: 3%;
    list-style: none;
    width: 100%;
    border-bottom: solid 1px #9C9C9C;
    padding-top: 30px;
    padding-bottom: 62px;
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;


    .news_date {
      font-size: 15px;
      margin-bottom: 25px;
      display: inline-flex;
      color: #3B3064;
       font-style: italic;

      @media #{$media-700} {
        margin-bottom: 5px;
      }
    }

    p {
      @include rem(20);
      color: #3B3064;
    }

    &::after {
      display: block;
      width: 100%;
      margin-top: 30px;
      content: "";
      height: 0%;
      background: #6758B6;
      position: absolute;
      z-index: -1;
      left: 0;

      transition: all .4s ease-out 0s;
      -moz-transition: all .4s ease-out 0s;
      -webkit-transition: all .4s ease-out 0s;
      -o-transition: all .4s ease-out 0s;

    }


    &:hover {
      &::after {
        height: 100%;
      }

      p,
      .news_date {
        color: #fff;
      }

    }

    @media #{$media-700} {
      padding-top: 15px;
      padding-bottom: 15px;
    }
  }
}


.scroll_block {
  max-width: 378px;
  margin-left: auto;
  margin-right: auto;
}


.custom_scrollbar {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  height: 14px;

  border-radius: 4px;

  &::after {
    content: "";
    display: block;
    height: 2px;
    width: 100%;
    background: rgba(0, 0, 0, 0.3);
    position: absolute;
  }


  @media #{$media-700} {
    height: 9px;

  }

}

.custom_scrollbar .swiper-scrollbar-drag {
  background: #f5a802;
  border-radius: 4px;
}