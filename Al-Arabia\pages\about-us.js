import React, { useState, useEffect } from "react";
import Head from "next/head";
import Link from "next/link";
import Image from "next/image";
import rtl from "@/styles/rtl.module.scss";
import InnerBanner from "@/component/InnerBanner";
import HeadMarquee from "@/component/HeadMarquee";
import { fetchPageById } from '@/lib/api/pageApi';
import parse from 'html-react-parser';
import comon from "@/styles/comon.module.scss";
import about from "@/styles/about.module.scss";
import { useRouter } from "next/router";
export default function Home({ pageData }) {

	const { locale } = useRouter();
	// const [tabSelect, setTabSelect] = useState();

	// const scrollToSection = (sectionId) => {
	//   setTabSelect(sectionId);
	//   const section = document.getElementById(sectionId);
	//   if (section) {
	//     section.scrollIntoView({ behavior: "smooth" });
	//   }
	// };

	const [activeSection, setActiveSection] = useState(null);

	// Sections list for tracking
	const sections = ["section1", "section2", "section3", "section4"];

	useEffect(() => {
		const observerOptions = {
			root: null, // viewport
			threshold: 0.3, // Reduced threshold to detect smaller sections
			rootMargin: "-20% 0px -50% 0px", // Adjusts when sections are considered "visible"
		};

		const observer = new IntersectionObserver((entries) => {
			let visibleSection = activeSection; // Maintain the last active section if none found

			entries.forEach((entry) => {
				if (entry.isIntersecting) {
					visibleSection = entry.target.id;
				}
			});

			setActiveSection(visibleSection);
		}, observerOptions);

		sections.forEach((sectionId) => {
			const section = document.getElementById(sectionId);
			if (section) observer.observe(section);
		});

		return () => observer.disconnect(); // Cleanup observer on unmount
	}, [activeSection]);

	const scrollToSection = (sectionId) => {
		setActiveSection(sectionId);
		const element = document.getElementById(sectionId);

		if (element) {
			const offset = 100; // Adjust this based on your header height
			const elementPosition =
				element.getBoundingClientRect().top + window.scrollY;

			window.scrollTo({
				top: elementPosition - offset,
				behavior: "smooth",
			});
		}
	};
	const banner = pageData.acf.banner_details;
	const overview = pageData.acf.overview_details;
	const comment = pageData.acf.comment_details;
	const mission = pageData.acf.mission__vision;
	const subsidiaries = pageData.acf.subsidiaries;
	const sustainability = pageData.acf.sustainability;

	const [isExpanded, setIsExpanded] = useState(false);
	const toggleExpand = () => setIsExpanded(!isExpanded);
	const MAX_LENGTH = 380

	return (
		<>
			<HeadMarquee />
			<InnerBanner showLink={false} title={banner.title} details={banner} />

			<section className={comon.d_flex_wrap}>
				<div
					className={`${comon.d_flex_wrap} ${comon.p_relative} ${comon.w_100}`}
				>
					<div className={about.left_block}>
						<div className={`${about.left_block_block} ${about.sticky}`}>
							<ul className={about.right_block_ul}>
								{pageData.acf.tab_content.map((section, index) => (
									<li
										key={index}
										// data-aos="fade-up"
										// data-aos-duration="1000"
										data-aos-delay={200 * (index + 1)}
									>
										<span
											onClick={() => scrollToSection(`section${index + 1}`)}
											className={activeSection === `section${index + 1}` ? about.active : ""}
										>
											{section.item}
										</span>
									</li>
								))}
							</ul>
						</div>
					</div>

					<div className={about.right_block}>
						{pageData.acf.tab_content.map((section, index) => {
							return (
								<>

									{index == 0 && (
										<>
											<div
												id={`section${index + 1}`}
												className={`${comon.w_100} ${comon.d_flex_wrap} ${about.px_about}  ${about.px_about_min} ${about.padding_zero_block} `}
											>
												<ul className={about.two_cl_ul}>
													<li
														// data-aos="fade-up"
														// data-aos-duration="1000"
														className={`${comon.w_50} ${about.mission_vission_block}`}
													>
														<>
															{parse(overview.overview_text_left)}
														</>
													</li>
													<li
														// data-aos="fade-up"
														// data-aos-duration="1000"
														className={`${comon.w_50} ${about.mission_vission_block}`}
													>
														<>
															{parse(overview.overview_text_right)}
														</>
													</li>
												</ul>
											</div>

											<div
												className={`${comon.w_100}  ${comon.d_flex_wrap} ${about.message_block_sec}`}
											>

												{comment.map((item, index) => {
													return (

														<div
															style={{ backgroundColor: index === 0 ? "#6758b6" : "#514887" }}
															className={`${comon.w_50} ${about.message_block}  ${about.message_block_anim}`}
														>
															<div
																className={`${comon.w_100} ${comon.d_flex_wrap}  ${about.no_wrap}`}
															>
																<div
																	// data-aos="fade-up"
																	// data-aos-duration="1000"
																	className={`${about.message_img} ${comon.p_relative}`}
																>
																	<Image
																		src={item.image}
																		fill
																		quality={100}
																		style={{ objectFit: "cover" }}
																		alt=""
																	/>
																</div>
																<div
																	// data-aos="fade-in"
																	// data-aos-duration="1000"
																	className={`${about.message_txt} ${comon.d_flex_wrap} ${comon.w_100}`}
																>


																	<Image
																		// src="/images/qt.png"
																		src="/images/qt1.svg"
																		quality={100}
																		width={29}
																		height={29}
																		className={rtl.quote_icn}
																		alt=""
																	/>

																	<p>
																		{parse(item.comment)}
																	</p>
																	<Image
																		src="/images/qt21.svg"
																		className={`${comon.ml_auto} ${rtl.quote_icn} ${rtl.ml_auto}`}
																		quality={100}
																		width={29}
																		height={29}
																		alt=""
																	/>
																	<div
																		// data-aos="fade-in"
																		// data-aos-duration="1000"
																		className={about.message_txt_02}
																	>
																		<p>{item.designation}</p>
																		<h5>{item.name}</h5>
																	</div>
																</div>
															</div>
														</div>

													)
												})}
											</div>
										</>
									)}
									{index == 1 && (
										<>
											<div
												className={`${comon.w_100} ${comon.d_flex_wrap} ${about.mission_vission_padding} ${about.px_about} ${about.padding_zero_block}`}
												id={`section${index + 1}`}
											>
												<ul className={about.two_cl_ul}>
													<li
														data-aos="fade-in"
														data-aos-duration="1000"
														className={`${comon.w_50} ${about.mission_vission_block}`}
													>
														<div className={`${comon.title_30} ${comon.mb_20}`}>
															<h3>{parse(mission.mission_title)}</h3>
														</div>
														<>
															{parse(mission.mission_details)}
														</>
													</li>
													<li
														data-aos="fade-in"
														data-aos-duration="1000"
														className={`${comon.w_50} ${about.mission_vission_block}`}
													>
														<div className={`${comon.title_30} ${comon.mb_20}`}>
															<h3>{parse(mission.vision_title)}</h3>
														</div>
														<>
															{parse(mission.vision_details)}
														</>
													</li>
												</ul>
											</div>

											<div
												data-aos="fade-in"
												data-aos-duration="1000"
												className={`${comon.w_100} ${comon.d_flex_wrap} ${comon.p_relative}`}
												style={{ aspectRatio: "1305 / 676" }}
											>
												<Image
													src={mission.image}
													fill
													style={{ objectFit: "cover" }}
													alt=""
													quality={100}
												/>
											</div>
										</>
									)}
									{index == 2 && (
										<>
											<div
												id={`section${index + 1}`}
												className={`${comon.w_100} ${comon.mt_60}  ${about.px_about} ${about.px_about_min}  ${about.padding_bot_mob} ${comon.pt_0} ${comon.pb_20}`}
											>
												<div
													data-aos="fade-in"
													data-aos-duration="1000"
													className={`${comon.title_30} ${about.subsidiaries_head}  ${comon.justify_space_bet}  ${comon.d_flex_wrap} ${comon.flex_center}`}
												>
													<h3>{parse(subsidiaries.title)}</h3>
													{/* {subsidiaries.button && (
														<Link className={`${comon.link}`} href={subsidiaries.button.url}>
															{subsidiaries.button.title}
														</Link>
													)} */}
												</div>
											</div>

											<ul className={`${about.subsidiaries_block}`}>
												{(() => {
													let hoverBoxCount = 0;
													const bgColors = [
														{ backgroundColor: "#6758b6" },
														{ backgroundColor: "rgb(236 236 236)" },
														{ backgroundColor: "#6758b6" },
														{ backgroundColor: "#6758b6" },
														{ backgroundColor: "rgb(236 236 236)" },
														{ backgroundColor: null },
														{ backgroundColor: 'rgb(225 226 225)' }
													];
													return subsidiaries?.item_type?.map((item, index) => {
														if (item.acf_fc_layout === "title_&_text") {
															return (
																<li
																	key={index}
																	className={`${about.subsidiaries_li}`}
																	data-aos="fade-in"
																	data-aos-duration="1000"
																>
																	<div className={`${comon.w_100}`}>
																		<h3>{item.title}</h3>
																		<p>{item.text_area}</p>
																	</div>
																</li>
															);
														}

														if (item.acf_fc_layout === "hover_box") {
															hoverBoxCount++;
															const bgColor = bgColors[hoverBoxCount - 1]?.backgroundColor || "#6758b6"; // Ensure default value is used when undefined
															return (
																<li
																	key={index}
																	data-aos="fade-in"
																	data-aos-duration="1000"
																	className={`${comon.logo_block} ${about.subsidiaries_li} ${comon.flex_center} ${comon.justify_center} ${comon.d_flex_wrap}`}
																	style={{
																		backgroundColor: bgColor,  // Dynamic background color
																		aspectRatio: "326/260",
																	}}
																>
																	<div
																		className={` ${about.hover_block}`}
																		style={{ backgroundColor: bgColor }}
																	//   data-lenis-prevent={true}
																	>
																		{/* <div className={`${about.hover_block_logo}`}>
													<Image
													  src={item.image || "/images/subsidiaries_ic_01.svg"}  // Dynamic image URL
													  width={111}
													  height={62}
													  alt=""
													  quality={100}
													/>
												  </div> */}

																		<div className={`${about.hover_block_txt} ${hoverBoxCount === 7 ||hoverBoxCount === 5 || hoverBoxCount === 2 ? about.hover_block_white_sec : ''}`}>
																			<p>{parse(item.description)}</p>
																			{/* {item.button.url != "#" && (
																				<Link
																					className={`${comon.link} ${comon.link_white}`}
																					href={item.button.url}
																				>
																					{item.button.title}
																				</Link>
																			)} */}

																			{item.button && item.button.url != "#" && (
																				<Link
																					className={`${comon.link} ${comon.link_white}`}
																					href={item.button.url}
																				>
																					{item.button.title}
																				</Link>
																			)}
																		</div>
																	</div>

																	<div
																		className={` ${comon.p_relative} ${comon.w_100}`}
																	// style={{ aspectRatio: "200/60", maxWidth: "200px" }}
																	>
																		<div className={`${about.stock_block_logo}`}>
																			<Image
																				src={item.image || "/images/subsidiaries_ic_01.svg"}  // Dynamic image URL
																				fill
																				style={{ objectFit: "contain" }}
																				alt=""
																				quality={100}
																			/>
																		</div>
																	</div>
																</li>
															);
														}
														if (item.acf_fc_layout === "image_only") {

															<li
																data-aos="fade-in"
																data-aos-duration="1000"
																className={`${comon.logo_block} ${about.subsidiaries_li} ${comon.flex_center} ${comon.justify_center} ${comon.d_flex_wrap} `}
																style={{ aspectRatio: "326/ 260" }}
															>
																<div
																	className={` ${comon.p_relative} ${comon.w_100}`}
																	style={{ aspectRatio: "135 / 60", maxWidth: "100px" }}
																>
																	<div className={`${about.stock_block_logo}`}>

																		<Image
																			src={block.image}
																			fill
																			style={{ objectFit: "contain" }}
																			alt=""
																			quality={100}

																		/>
																	</div>
																</div>
															</li>
														}

														return null;
													});
												})()}
											</ul>

										</>
									)}
									{index == 3 && (
										<>
											<div
												id={`section${index + 1}`}
												className={`${comon.pt_65}  ${comon.pb_65} custom_next_4`}
												style={{
													backgroundImage: `url(${sustainability.image})`,
													backgroundSize: "cover", // Optional: Adjust background size
													backgroundPosition: "center", // Optional: Adjust background position
													backgroundRepeat: "no-repeat", // Optional: Prevent image repetition
												}}
											>
												<div className={`${comon.wrap}`}>
													<div className={`${comon.w_100} ${comon.d_flex_wrap}`}>
														<div
															data-aos="fade-in"
															data-aos-duration="1000"
															className={`${about.sustainability_block} ${comon.ml_auto} ${rtl.ml_auto}`}
														>
															<h3>{parse(sustainability.titile)}</h3>
															<>
																{isExpanded
																	? parse(sustainability.description)
																	: parse(sustainability.description.substring(0, MAX_LENGTH) + (sustainability.description.length > MAX_LENGTH ? '...' : ''))
																}
															</>
															{!isExpanded && sustainability.description.length > MAX_LENGTH && (
																<button
																	onClick={toggleExpand}
																	className={`${comon.buttion} ${comon.but_white} base_font1 ${comon.mt_20}`}
																>
																	{locale === 'ar' ? "اقرأ المزيد" : "Read More"}
																</button>
															)}
															{isExpanded && (
																<button
																	onClick={toggleExpand}
																	className={`${comon.buttion} ${comon.but_white} base_font1 ${comon.mt_20}`}
																>
																	{locale === 'ar' ? "اقرأ أقل" : "Read Less"}
																</button>
															)}

														</div>
													</div>
												</div>
											</div>
										</>
									)}
								</>
							)
						})}
					</div>
				</div>
			</section>
		</>
	);
}

export async function getStaticProps({ locale }) {
	const langCode = locale === "ar" ? "ar" : "en";
	//const langCode = "en"
	try {
		const pageData = await fetchPageById("about-us", langCode);
		return {
			props: {
				pageData,
			},
			revalidate: 10,
		};
	} catch (error) {
		console.error('Error fetching data:', error);
		return {
			props: {
				pageData: null,
			},
		};
	}
}
