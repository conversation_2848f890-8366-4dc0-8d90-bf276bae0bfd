@import "variable", "base", "mixin";

.banner_main {
    background: #ccc;
    height: 100vh;

    &.height_banner {
        height: 100dvh;
    }

    .banner_slide_main {
        align-items: center;
        display: flex;
        flex-wrap: wrap;

        .banner_main_swipper {
            height: 100%;
            width: 100%;
        }

        .banner_main_img {
            width: 25%;
            height: 100%;

            img {
                width: auto;
                height: 100%;
                object-fit: contain;
            }
        }

        .banner_txt_block {
            width: 100%;
            height: 100%;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
        }
    }

    @media #{$media-700} {
        height: calc(100dvh - 50px);
    }
}

.banner_main {
    background: linear-gradient(107.56deg,
            rgba(103, 90, 167, 1) 0%,
            rgba(58, 48, 100, 1) 100%);

    position: relative;

    .banner_txt_block {
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;

        h2 {
            color: #fff;
            font-size: 10rem;
            font-weight: 700;
            text-align: center;
            line-height: 100%;
            font-family: var(aller_bd);

            span {
                width: 100%;
                display: block;
                font-weight: 400;
                font-family: var(--aller_lt);
                color: #fff;
            }

            @media #{$media-700} {
                font-size: 4rem;
            }
        }
    }

    .banner_video_main {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;

        // .video_block{  transform: translate3d(0px, -18vh, 0px) scale3d(0.5, 0.5, 1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
        //             transform-style: preserve-3d;}
    }

    .video_block {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}

.banner_bottom_img_block {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    margin-left: -6%;
    margin-right: -6%;
    margin-bottom: 35px;

    li {
        list-style: none;
        padding: 0 6%;

        h5 {
            color: #fff;
            @include rem(17);
            font-weight: 500;

            span {
                font-weight: 400;
                display: block;
            }
        }

        img {
            width: 100%;
        }
    }
}

.banner_bot_box {
    width: auto;
    position: absolute;
    bottom: 0;

    @media #{$media-700} {
        width: 40%;
    }

    @media #{$media-700} {
        width: auto;
    }
}

:global(body.rtl) {

    .image_text_safari {
        height: 100%;
        width: 100%;

        img {
            height: auto;
            width: 100%;
            display: block;

            object-fit: contain;
        }
    }
}

.explore_btn {
    position: absolute;
    bottom: 65px;
    z-index: 2;

    .banner_logo_box {
        // width: 37%;
        width: 158px;
        height: 158px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        >img {
            height: auto;
            width: 40%;
            object-fit: contain;
            display: block;

            @media #{$media-1280} {
                width: 35%;
            }
        }

        .text_round {
            position: absolute;
            height: 100%;
            width: 100%;
            left: 50%;
            top: 50%;
            transform: translateX(-50%) translateY(-50%);
            display: flex;
            align-items: center;
            justify-content: center;
            animation: logoRotate 15s linear infinite;

            >img {
                height: auto;
                width: 100%;
                object-fit: contain;
                display: block;
            }
        }

        @media #{$media-1280} {
            width: 120px;
            height: 120px;
        }

        @media #{$media-700} {
            width: 100px;
            height: 100px;
        }

        &.banner_logo_text_box {
            @keyframes rotateText {
                0% {
                    transform: rotate(360deg);
                }

                100% {
                    transform: rotate(0deg);
                }
            }

            .text_sec {
                position: absolute;
                width: 100%;
                height: 100%;
                animation: rotateText 10s linear infinite;

                p {
                    span {
                        position: absolute;
                        left: 50%;
                        font-size: 16px;
                        transform-origin: 0 79px;
                        color: white;

                        @media #{$media-1280} {
                            transform-origin: 0 60px;
                            font-size: 14px;
                        }

                        @media #{$media-700} {
                            transform-origin: 0 50px;
                            font-size: 12px;
                        }
                    }
                }

                svg {
                    fill: currentColor;
                    height: auto;
                    max-width: 66vmin;
                    transform-origin: center;
                    width: 100%;
                    // font-size: 10.5px;
                    // font-size: 15.4px;
                    // font-size: 14.5px;
                    font-size: 11.7px;
                    font-family: var(--arbfontsBase) !important;
                    line-height: normal;

                    // @media #{$media-700} {
                    //      font-size: 12px;
                    // }
                }
            }
        }
    }

    @media #{$media-820} {
        left: 60px;
    }

    @media #{$media-500} {
        left: 50px;
    }
}

@keyframes logoRotate {
    from {
        transform: translateX(-50%) translateY(-50%) rotate(0deg);
    }

    to {
        transform: translateX(-50%) translateY(-50%) rotate(360deg);
    }
}

.banner_sub_txt_box {
    width: 63%;
}

@keyframes animName {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.paralax_block {
    width: 25%;
    margin-bottom: 100px;
    margin-top: 100px;
}

.inner_banner_video {
    height: 100%;
    width: 100%;
    position: absolute;
    pointer-events: none;
    left: 0;
    top: 0;

    video {
        height: 100%;
        width: 100%;
        object-fit: cover;
        display: block;
    }

    &::after {
        height: 100%;
        width: 100%;
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        pointer-events: none;
        background-color: hsla(252, 35%, 29%, 0.55);
    }

    &.black_gradient {
        filter: grayscale(1);

        &::after {
            background-color: hsla(0, 0%, 0%, 0.55);
        }
    }
}

.inner_banner_main {
    min-height: 300px;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    justify-content: center;
    // padding-bottom: 75px;
    padding-bottom: 85px;
    position: relative;
    overflow: hidden;

    // background: linear-gradient(107.56deg,
    //         rgba(103, 90, 167, 1) 0%,
    //         rgba(58, 48, 100, 1) 100%);

    &.mob_banner {
        @media #{$media-700} {
            padding-top: 80px;
        }
    }

    h1 {
        color: #fff;
        @include rem(30);
        font-family: var(--aller_bd);
        font-weight: unset;
        width: 100%;
        text-align: center;
        position: relative;
        z-index: 1;

        @media #{$media-820} {
            @include rem(50);
            line-height: 4rem;
        }

        @media #{$media-700} {
            @include rem(40);
            line-height: 2.5rem;
        }

        @media #{$media-500} {
            @include rem(30);
        }
    }

    .banner_link {
        position: relative;
        z-index: 1;
    }

    @media #{$media-1024} {
        padding-bottom: 100px;
    }

    @media #{$media-700} {
        // min-height: 150px;
        min-height: 250px;
        padding-bottom: 80px;
    }

    @media #{$media-500} {
        min-height: 200px;
        padding-bottom: 55px;
    }
}

.padding_banner {
    padding-bottom: 45px;

    @media #{$media-700} {
        padding-bottom: 30px;
    }
}

.btn_padding_device {
    @media #{$media-700} {
        height: 35px;
        padding-left: 15px;
        padding-right: 15px;
    }
}

// .padding_bottom_banner {
//     padding-bottom: 45px;
// }

.padding_top_banner {
    padding-top: 50px;
}

// -------Slider Banner Text--------
.slider_banner_main {
    overflow: hidden;
    position: relative;

    &.full_view_section {
        height: calc(100dvh - 50px) !important;
        height: 100dvh !important;
        padding-top: 150px;
        display: flex;
        align-items: center;

        @media #{$media-1024} {
            padding-top: 70px;
        }
    }

    &.extra_height {
        min-height: 450px;

        @media #{$media-1440} {
            min-height: 400px;
        }

        @media #{$media-1366} {
            min-height: unset;
        }
    }

    .slider_head_sec {
        text-align: center;
        margin: auto;
        width: 100%;
        max-width: 570px;
        display: flex;
        flex-direction: column;
        align-items: center;
        // padding-bottom: 75px;
        padding-bottom: 50px;
        position: relative;
        z-index: 2;

        h3 {
            color: #ffffff;
            // font-size: 30px;
            @include rem(30);
            font-weight: 700;
            margin-bottom: 25px;

            @media #{$media-1200} {
                margin-bottom: 15px;
            }
        }

        p {
            color: #ffffff;
            font-size: 15px;
            font-weight: 400;

            @media #{$media-700} {
                font-size: 14px;
            }
        }

        @media #{$media-1200} {
            padding-bottom: 20px;
        }
    }

    @media #{$media-700} {
        padding-top: 80px;
    }

    &.mob_pt_150 {
        @media #{$media-700} {
            padding-top: 120px;
        }
    }
}