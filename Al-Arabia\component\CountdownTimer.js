import { useState, useEffect } from "react";
import comon from "@/styles/comon.module.scss";
import boulevard from "@/styles/boulevard.module.scss";

const CountdownTimer = ({ targetDate }) => {
	const calculateTimeLeft = () => {
		const difference = new Date(targetDate) - new Date();
		let timeLeft = {};

		if (difference > 0) {
			timeLeft = {
				days: Math.floor(difference / (1000 * 60 * 60 * 24)),
				hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
				minutes: Math.floor((difference / 1000 / 60) % 60),
				seconds: Math.floor((difference / 1000) % 60),
			};
		}

		return timeLeft;
	};

	const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());

	useEffect(() => {
		const timer = setInterval(() => {
			setTimeLeft(calculateTimeLeft());
		}, 1000);

		return () => clearInterval(timer);
	}, []);

	return (
		<div className={`${boulevard.conter_wrap_outer} ${comon.d_flex_wrap}`}>
			<h3>Riyadh Season End:</h3>
			<div className={`${boulevard.conter_wrap} ${comon.d_flex_wrap}`}>
				<div className={`${boulevard.conter_main}`}>
					<div className={`${boulevard.conter_title}`}>
						{String(timeLeft.days).padStart(2, "0")}
					</div>
					<span>Days</span>
				</div>
				<div className={`${boulevard.conter_main}`}>
					<div className={`${boulevard.conter_title}`}>
						{String(timeLeft.hours).padStart(2, "0")}
					</div>
					<span>Hours</span>
				</div>
				<div className={`${boulevard.conter_main}`}>
					<div className={`${boulevard.conter_title}`}>
						{String(timeLeft.minutes).padStart(2, "0")}
					</div>
					<span>Minutes</span>
				</div>
				<div className={`${boulevard.conter_main}`}>
					<div className={`${boulevard.conter_title}`}>
						{String(timeLeft.seconds).padStart(2, "0")}
					</div>
					<span>Seconds</span>
				</div>
			</div>
		</div>
	);
};

export default CountdownTimer;
