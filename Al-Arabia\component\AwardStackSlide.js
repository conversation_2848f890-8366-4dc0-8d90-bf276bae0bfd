import React from "react";
import comon from "@/styles/comon.module.scss";
import style from "@/styles/AwardSlide.module.scss";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/effect-fade"; // Import fade effect styles
import parse from "html-react-parser";
import { Autoplay, EffectFade, Navigation, Pagination } from "swiper/modules";
import { useRouter } from "next/router";

const AwardStackSlide = ({ data }) => {
	const slides = [
		{
			backgroundImage: "/images/bg_csr11.jpg",
			title: "AWARD Winning",
			subtitle: "AlArabia Gift Campaign",
			imageSrc: "/images/card_img.jpg",
			description:
				"On the 90th Saudi National Day, AlArabia launched a campaign across the country to support 90 startup businesses to overcome the limitations caused by Covid-19. AlArabia allocated 18 million Saudi Riyal to be distributed among the 90 winners.",
		},
		{
			backgroundImage: "/images/bg_csr1.jpg",
			title: "AWARD Winning",
			subtitle: "Biggest Art Gallery in The World",
			imageSrc: "/images/bg_csr1.jpg",
			description:
				"AlArabia launched the biggest art gallery in the world on its billboards across the country to support young Saudi artists. The campaign helped raise awareness of hundreds of Saudi artists and thousands of artworks. It won six international awards and created international buzz.",
		},
		{
			backgroundImage: "/images/bg_csr2.jpg",
			title: "AWARD Winning",
			subtitle: "The Campaign Won 5 International Awards",
			imageSrc: "/images/bg_csr2.jpg",
			description:
				"BE A MAN Prostate cancer has been steadily increasing in KSA as men avoid getting themselves checked. Therefore, AlArabia redefined manhood through a CSR campaign to encourage men to get themselves checked. The 360-campaign revealed that manhood means being responsible towards one’s family and health. It created buzz and raised awareness which led to a significant increase in early checkup numbers. The campaign caught the attention of the Kingdom’s Ministry of Health in which they offered their support.",
		},
	];

	const router = useRouter();

	return (
		<div className={style.card_container}>
			<Swiper
				loop={true}
				effect="fade"
				modules={[Autoplay, EffectFade, Navigation, Pagination]}
				autoplay={{
					delay: 5000,
					disableOnInteraction: false,
				}}
				navigation={{
					nextEl: ".next_btn",
					prevEl: ".prev_btn",
				}}
				className="award_stack_swiper"
				speed={1000}
				fadeEffect={{ crossFade: true }} // Ensures a smooth transition
				dir={router.locale == "ar" || router.locale == "rtl" ? "rtl" : "ltr"}
			>
				{data.map((slide, index) => (
					<SwiperSlide key={index} className="fade-up-img">
						<div className={style.slide_wrapper}>
							<div
								className={`${comon.pt_90} ${comon.pb_90} ${style.card_section}`}
								style={{
									backgroundImage: `url(${slide.background_image})`,
									backgroundSize: "cover",
									backgroundPosition: "center top",
									backgroundRepeat: "no-repeat",
								}}
							>
								<div
									className={style.gift_block}
									data-aos="fade-up"
									data-aos-duration="1000"
									data-lenis-prevent={true}

								>
									<div className={style.gift_content}>
										<div className={`${comon.w_100} ${comon.pb_20}`}>
											<h6>{slide.title}</h6>
											<h5>{slide.sub_title}</h5>
										</div>
										<div
											className={`${comon.pb_20} ${comon.d_flex_wrap} ${comon.justify_center}`}
										>
											<Image
												src={slide.image}
												className={comon.image_res}
												alt=""
												quality={100}
												width={412}
												height={209}
											/>
										</div>
										<div className={style.gift_txt_block}>
											{parse(slide.description)}
										</div>
									</div>
								</div>

								<div className={style.swiper_btn_section}>
									<div className={`${style.swiper_btn} prev_btn`}></div>
									<div className={`${style.swiper_btn} next_btn`}></div>
								</div>
							</div>
						</div>
					</SwiperSlide>
				))}
			</Swiper>
		</div>
	);
};

export default AwardStackSlide;
