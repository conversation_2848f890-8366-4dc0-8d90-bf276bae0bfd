@import "variable", "base", "mixin";


.marqueeWrapper {
  overflow: hidden;
  white-space: nowrap;
  position: relative;
  width: 100%;
  background: #3B3064;
  padding-top: 13px;
  padding-bottom: 13px;

  p {
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
    @include rem(18);
    font-weight: 400;
    font-family: var(--aller_lt);

    @media #{$media-700} {
      @include rem(15);
    }
  }
}

.marqueeContent {
  display: inline-flex;
  white-space: nowrap;
}


.head_marqueeWrapper {
  overflow: hidden;
  white-space: nowrap;
  position: relative;
  width: 100%;
  background: #6758B6;
  padding-top: 13px;
  padding-bottom: 8px;

  &.head_marqueeWrapper_overlay {
    z-index: 500;
  }

  p {
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
    @include rem(18);
    font-weight: 400;
    font-family: var(--aller_lt);

    @media #{$media-700} {
      @include rem(15);
    }

    &.special<PERSON>har {
      position: relative;
      color: #03D784;


      &::after {
        content: '';
        border-top: 5px solid #03D784;
        border-left: 5px solid #03D784;
        border-right: 5px solid rgba(255, 0, 0, 0);
        border-bottom: 5px solid rgba(255, 0, 0, 0);
        top: 55%;
        transform: translateY(-20%) rotate(45deg);
        left: 0;
        position: absolute;
      }
    }
  }
}