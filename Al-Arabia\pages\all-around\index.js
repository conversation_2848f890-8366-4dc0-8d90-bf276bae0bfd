import React, { useState, useEffect } from "react";
import Head from "next/head";
import Image from "next/image";
import Link from "next/link";
import StartCampaign from "@/component/StartCampaign";
import InnerBanner from "@/component/InnerBanner";
import ContactSection from "@/component/ContactSection";
import HeadMarquee from "@/component/HeadMarquee";
import comon from "@/styles/comon.module.scss";
import Masonry from "react-masonry-css";
import style from "@/styles/terminals.module.scss";
import { fetchPageById, fetchPostList } from '@/lib/api/pageApi';
export default function Index({pageData, AllList}) {

	// const images = [
	// 	"/images/around-gallery1.jpg",
	// 	"/images/around-gallery2.jpg",
	// 	"/images/around-gallery3.jpg",
	// 	"/images/around-gallery4.jpg",
	// 	"/images/around-gallery5.jpg",
	// 	"/images/around-gallery6.jpg",
	// 	"/images/around-gallery7.jpg",
	// 	"/images/around-gallery8.jpg",
	// 	"/images/around-gallery9.jpg",
	// 	"/images/around-gallery10.jpg",
	// ];

	const breakpointColumns = {
		default: 3,
		1366: 4,
		1100: 3,
		700: 2,
	};
	const banner = pageData.acf.banner_details;
	const chunkArray = (arr, size) =>
		Array.from({ length: Math.ceil(arr.length / size) }, (_, i) =>
			arr.slice(i * size, i * size + size)
		);
	const imageChunks = chunkArray(AllList, 10);
	return (
		<>
			<HeadMarquee />
			{/* <InnerBanner
				linkHref="/campaign"
				linkText="Join the Movement"
				title="All Around"
				extraClass="padding_bottom_banner"
				showLink={false}
			/> */}
			<InnerBanner  showLink={false} title={banner.title} details={banner} extraClass="padding_bottom_banner" />

			<section className={`${comon.airport_gallery_main}`}
				data-aos="fade-in"
				data-aos-duration="1000">
				{/* <Masonry
					breakpointCols={breakpointColumns}
					className="my-masonry-grid"
					columnClassName={`my-masonry-grid_column  `}
				>
					{images.map((src, index) => (
						<Link
							href={"/all-around-detail"}
							className={`${style.img_body} ${comon.image_hover} ${comon.d_block}      `}
							key={index}
							data-aos="fade-in"
							data-aos-duration="1000"
							data-aos-delay={index * 100}
						>
							<Image
								src={src}
								height={300}
								width={400}
								className={`   `}
								alt={`Image ${index + 1}`}
							/>
						</Link>
					))}
				</Masonry> */}

				{/* <div className={`${comon.image_grid_layout}`}>
					{images.map((data, index) => (
						<Link
							href={"/all-around-detail"}
							className={`${` ${data.class}`} `}
							key={index}
						>
							<Image
								src={data.img}
								height={300}
								width={400}
								alt={`Image ${index + 1}`}
							/>
						</Link>
					))}
				</div> */}

				{imageChunks?.map((chunk, groupIndex) => {
				let x = 1; 
				return (
					<div className={`${comon.image_grid_layout}`} key={groupIndex}>
						{chunk.map((data, index) => {
					
					const content =  (
								<Link
									href={`/all-around/${data.slug}`}
									className={`grid_sec${x}`}
									key={index}
								
								>
									<Image
										src={data.acf.listing_page_image}
										height={2400}
										width={2500}
										alt={`Image ${groupIndex * 10 + index + 1}`}
										quality={100}
									/>
								</Link>
							);
							x++;
					if (x > 9) x = 1;
					return content;
						})}
					</div>
				)})}
			</section>

			<StartCampaign />
			<section className={`${comon.pt_50}  `}>
				<ContactSection
					title="Request for Quotation"
					paragraph="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore."
					button="Request a Quote"
					showAutoMargin={false}
				/>
			</section>
		</>
	);
}


export async function getStaticProps({locale}) {
	const langCode = locale === "ar" ? "ar" : "en";
	//const langCode = "en"
	const slug = null;
	try {
	  const pageData = await fetchPageById("areas-all-around", langCode);  
	  const AllList = await fetchPostList('all-around', 100, langCode, slug);
	  return {
		props: {
			pageData, 
			AllList
		},
		revalidate: 10, 
	  };
	} catch (error) {
	  console.error('Error fetching data:', error);
	  return {
		props: {
			pageData: null, 
		},
	  };
	}
  }
