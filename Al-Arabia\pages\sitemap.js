import React, { useState, useEffect, useRef } from "react";
import Head from "next/head";
import Link from "next/link";
// ---buttion-import--start
import But<PERSON> from "@/component/button/Buttons";

import Marquee from "@/component/Marquee";
// ---buttion-import--end

import Image from "next/image";
import InnerBanner from "@/component/InnerBanner";
import Parallax from "@/component/Paralax";
import Map from "@/component/contactmap";
// Import Swiper styles
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/scrollbar";
import { Scrollbar, Autoplay, Navigation } from "swiper/modules";
import comon from "@/styles/comon.module.scss";
import csr from "@/styles/csr.module.scss";
import buttion from "@/styles/buttion.module.scss";
import DynamicImage from "@/component/DynamicImage";

import HeadMarquee from "@/component/HeadMarquee";
import SliderSection from "@/component/SliderSection";

export default function Home() {
	const [isDark, setIsDark] = useState(false);

	useEffect(() => {
		const darkModeQuery = window.matchMedia("(prefers-color-scheme: dark)");

		// Function to update the state when theme changes
		const updateTheme = (e) => {
			setIsDark(e.matches);
		};

		// Check initial theme
		setIsDark(darkModeQuery.matches);

		// Listen for theme changes
		darkModeQuery.addEventListener("change", updateTheme);

		// Cleanup the event listener when the component unmounts
		return () => darkModeQuery.removeEventListener("change", updateTheme);
	}, []);

	return (
		<>
			{/* <div
      className={`min-h-screen flex items-center justify-center ${
        isDark ? "bg-gray-900 text-white" : "bg-white text-black"
      }`}
    >
      <h1>{isDark ? "🌙 Dark Mode is Active" : "☀️ Light Mode is Active"}</h1>
    </div> */}

		 
			<HeadMarquee />
			{/* <InnerBanner
				linkHref="/campaign"
				linkText="Join the Movement"
				title="Sitemap"
				extraClass="padding_bottom_banner"
				showLink={true}
			/> */}

			<section className={`${comon.pt_60} ${comon.pb_30}`}>
				<div className={`${comon.wrap}`}>
					<ul className={`${comon.sitemap}`}>
						<li>
							<Link target="_blank" href={"/"}>
								Home
							</Link>
						</li>
						<li>
							<Link target="_blank" href={"airports"}>
								Airports
							</Link>
						</li>

						<li>
							<Link target="_blank" href={"news"}>
								news
							</Link>
						</li>

						<li>
							<Link target="_blank" href={"news-detail"}>
								news-detail
							</Link>
						</li>

						<li>
							<Link target="_blank" href={"project-kkia"}>
								project-kkia
							</Link>
						</li>

						<li>
							<Link target="_blank" href={"terminals"}>
								terminals
							</Link>
						</li>

						<li>
							<Link target="_blank" href={"csr"}>
								CSR
							</Link>
						</li>

						<li>
							<Link target="_blank" href={"about-us"}>
								About Us
							</Link>
						</li>

						<li>
							<Link target="_blank" href={"project-mezah"}>
								Riyadh project mezah
							</Link>
						</li>

						<li>
							<Link target="_blank" href={"project-maalem"}>
								Riyadh project Maalem
							</Link>
						</li>

						<li>
							<Link target="_blank" href={"boulevard"}>
								Riyadh project Boulevard
							</Link>
						</li>

						<li>
							<Link target="_blank" href={"all-around"}>
								All Around (main)
							</Link>
						</li>

						<li>
							<Link target="_blank" href={"start-your-campaign"}>
								Start Your Campaign{" "}
							</Link>
						</li>

						<li>
							<Link target="_blank" href={"careers"}>
								Career
							</Link>
						</li>

						<li>
							<Link target="_blank" href={"contact-us"}>
								Contact us
							</Link>
						</li>

						<li>
							<Link target="_blank" href={"all-around-detail"}>
								All Around Detail
							</Link>
						</li>
						<li>
							<Link target="_blank" href={"altanfeethi"}>
								Altanfeethi
							</Link>
						</li>

						<li>
							<Link target="_blank" href={"news"}>
								news
							</Link>
						</li>
						<li>
							<Link target="_blank" href={"dubai"}>
								dubai
							</Link>
						</li>

						<li>
							<Link target="_blank" href={"king-khaled-international-airport"}>
								King Khaled International Airport
							</Link>
						</li>

						<li>
							<Link target="_blank" href={"news"}>
								news
							</Link>
						</li>
					 

						<li>
							<Link target="_blank" href={"access-road"}>
								Access Road
							</Link>
						</li>

						<li>
							<Link target="_blank" href={"tuwaiq"}>
								Tuwaiq
							</Link>
						</li>
					</ul>
				</div>
			</section>
		</>
	);
}
