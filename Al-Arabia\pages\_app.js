import "@/styles/globals.css";
import "@/styles/calendar.css";
import "@/styles/sliderAnim.css";
import "@/styles/projectslidernew.css";
import "@/styles/ProjectSlider.css";
import "@/styles/fontface.css";
import Layout from "@/component/Layout";
import Head from "next/head";
import parse from 'html-react-parser';

export default function App({ Component, pageProps, props }) {
  const yoast_head = pageProps?.pageData?.yoast_head;
  return (
    <Layout props={props}>
      <Head>
        {yoast_head && parse(yoast_head)} 
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      <Component {...pageProps} />
    </Layout>
  );
}
