import React from "react";
import campaign from "@/styles/campaign.module.scss";
import comon from "@/styles/comon.module.scss";
import Link from "next/link";
const CampaignNetwork = ({ button, showAutoMargin }) => {
	return (
	  <>
	   
	   <div className={`${comon.title_30}`}>
          <h3>Select Network</h3>
        </div>

        <div className={`${comon.w_100} ${comon.d_flex_wrap} ${comon.justify_space_bet}   ${comon.pt_30}  `}>

          <div className={`${campaign.select_network_block}`}>
         <ul className={`${campaign.list_ul}`}>
          <li>
            <label for="vehicle1"> <input type="checkbox" id="vehicle1" name="vehicle1" value="Bike" /> Full Network</label>
          </li>
           
         </ul>
         </div>
        </div>


        <div className={`${comon.w_100} ${comon.d_flex_wrap} ${comon.justify_space_bet}   ${comon.pt_30}`}>
            <div className={`${campaign.select_network_block} ${campaign.block_style_01}`}>


            <span className={`${campaign.title_lable}`}>
              Commercial Elevators
            </span>
            



            <ul className={`${campaign.list_ul}`}>
              <li>
                <label for="vehicle2"> <input type="checkbox" id="vehicle2" name="vehicle2" value="Bike" /> Select All</label>
              </li>
              <li>
                <label for="vehicle3"> <input type="checkbox" id="vehicle3" name="vehicle3" value="Bike" /> Abu Dhabi</label>
              </li>
              <li>
                <label for="vehicle4"> <input type="checkbox" id="vehicle4" name="vehicle4" value="Bike" /> Business Bay</label>
              </li>
              <li>
                <label for="vehicle5"> <input type="checkbox" id="vehicle5" name="vehicle5" value="Bike" /> DIFC Freezone</label>
              </li>
              <li>
                <label for="vehicle6"> <input type="checkbox" id="vehicle6" name="vehicle6" value="Bike" /> DIFC Lift</label>
              </li>
              <li>
                <label for="vehicle7"> <input type="checkbox" id="vehicle7" name="vehicle7" value="Bike" /> JLT</label>
              </li>
              <li>
                <label for="vehicle8"> <input type="checkbox" id="vehicle8" name="vehicle8" value="Bike" /> Media City</label>
              </li>
              <li>
                <label for="vehicle9"> <input type="checkbox" id="vehicle9" name="vehicle9" value="Bike" /> One Central</label>
              </li>
              
            </ul>
            </div>

            <div className={`${campaign.select_network_block} ${campaign.block_style_01}`}>


            <span className={`${campaign.title_lable}`}>
            Residential Elevators
            </span>
            



            <ul className={`${campaign.list_ul}`}>
              <li>
                <label for="vehicle10"> <input type="checkbox" id="vehicle10" name="vehicle10" value="Bike" /> Select All</label>
              </li>
              <li>
                <label for="vehicle11"> <input type="checkbox" id="vehicle11" name="vehicle11" value="Bike" /> Abu Dhabi</label>
              </li>
              <li>
                <label for="vehicle12"> <input type="checkbox" id="vehicle12" name="vehicle12" value="Bike" /> Business Bay</label>
              </li>
              <li>
                <label for="vehicle13"> <input type="checkbox" id="vehicle13" name="vehicle13" value="Bike" /> Downtown</label>
              </li>
              <li>
                <label for="vehicle14"> <input type="checkbox" id="vehicle14" name="vehicle14" value="Bike" /> Dubai Creek</label>
              </li>
              <li>
                <label for="vehicle15"> <input type="checkbox" id="vehicle15" name="vehicle15" value="Bike" /> Dubai Marina</label>
              </li>
              <li>
                <label for="vehicle16"> <input type="checkbox" id="vehicle16" name="vehicle16" value="Bike" /> Dubai Hills</label>
              </li>
              <li>
                <label for="vehicle17"> <input type="checkbox" id="vehicle17" name="vehicle17" value="Bike" /> Silicon Oasis</label>
              </li>
              
            </ul>
            </div>



            <div className={`${campaign.select_network_block} ${campaign.block_style_01}`}>


<span className={`${campaign.title_lable}`}>
DIFC Large Format
</span>




<ul className={`${campaign.list_ul}`}>
  <li>
    <label for="vehicle17"> <input type="checkbox" id="vehicle17" name="vehicle17" value="Bike" /> Select All</label>
  </li>
  <li>
    <label for="vehicle18"> <input type="checkbox" id="vehicle18" name="vehicle18" value="Bike" /> Ticker</label>
  </li>
  <li>
    <label for="vehicle19"> <input type="checkbox" id="vehicle19" name="vehicle19" value="Bike" /> Pavilion</label>
  </li>
  <li>
    <label for="vehicle20"> <input type="checkbox" id="vehicle20" name="vehicle20" value="Bike" /> Gate District</label>
  </li>
  <li>
    <label for="vehicle21"> <input type="checkbox" id="vehicle21" name="vehicle21" value="Bike" /> Gate Village</label>
  </li>
  <li>
    <label for="vehicle22"> <input type="checkbox" id="vehicle22" name="vehicle22" value="Bike" /> Gate Avenue</label>
  </li>
  <li>
    <label for="vehicle23"> <input type="checkbox" id="vehicle23" name="vehicle23" value="Bike" /> Innovation Hub</label>
  </li>
 
  
</ul>
</div>






<div className={`${campaign.select_network_block} ${campaign.block_style_01}`}>
<span className={`${campaign.title_lable}`}>
One Central Large Format
</span>




<ul className={`${campaign.list_ul}`}>
  <li>
    <label for="vehicle24"> <input type="checkbox" id="vehicle24" name="vehicle24" value="Bike" /> Select All</label>
  </li>
  <li>
    <label for="vehicle25"> <input type="checkbox" id="vehicle25" name="vehicle25" value="Bike" /> One Central Columns</label>
  </li>
  
   
  
</ul>
</div>








        </div>






        <div className={`${comon.w_100} ${comon.d_flex_wrap}`}>
        <Link className={`${comon.buttion}  ${comon.but_h_02} ${comon.ml_auto} ${comon.but_fill}  ${comon.but_min_w} `} href={"#"}  >
                Next
         </Link>
        </div>
</>
	);
};

export default CampaignNetwork;
