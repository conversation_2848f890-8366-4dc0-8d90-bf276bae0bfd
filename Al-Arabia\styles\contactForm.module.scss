@import "variable", "base", "mixin";

@font-face {
  font-family: "aller_lt";
  src: url("../public/fonts/aller_lt-webfont.woff");
}

@font-face {
  font-family: "aller_rg";
  src: url("../public/fonts/aller_rg-webfont.woff");
}

@font-face {
  font-family: "aller_bd";
  src: url("../public/fonts/aller_bd-webfont.woff");
}

@font-face {
  font-family: "arbfontsBase";
  src: url("../public/fonts/Cairo-Regular.woff");
}

@font-face {
  font-family: "arbfonts";
  src: url("../public/fonts/arbfonts-ge_thameen_book.woff");
}

.dark_mode {
  .contact_form_main_ul {
    .form_li {
      .input_fld {
        color: #ffffff !important;
      }

      &::after {
        background: #ffffff;
      }
    }

    .input_fld {
      &::placeholder {
        color: #ffffff !important;
      }
    }
  }

  p {
    color: #ffffff !important;
  }
}

.ul_wrap {

  overflow: auto;
  max-height: 35dvh;
  // scrollbar-width: thin;
  padding: 0 10px;


  &::-webkit-scrollbar {
    // background-color: #3700ff00;
    width: 9px;

    // @media #{$media-700} {
    //   width: 8px;
    // }
  }

  &::-webkit-scrollbar-track {
    border: 1px solid #00000000;
    margin-top: 30px;
    margin-bottom: 30px;
    background: linear-gradient(90deg,
        rgba(206, 38, 38, 0) 47%,
        #838383 48%,
        #838383 52%,
        rgba(255, 255, 255, 0) 53%);
    border-radius: 12px;
    width: 100%;
    position: relative;
  }

  &::-webkit-scrollbar-thumb {
    // background-color: #c9c5d8;
    // border: 1px solid #c9c5d8;
    border: 1px solid #6758b6;
    background-color: #6758b6;
    border-radius: 16px;
  }

  &::-webkit-scrollbar-thumb:hover {
    border: 1px solid #514491;
    background-color: #514491;
  }

  &::-webkit-scrollbar-button {
    display: none;
  }


}



.contact_form_main_ul {
  >li {
    display: flex;
    flex-wrap: wrap;
  }

  margin: 0;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  .form_li {
    width: 48%;
    position: relative;
    padding-bottom: 20px;

    .input_fld {
      width: 100%;
      opacity: 1;
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -khtml-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      transition: all 0.4s ease-out 0s;
      -moz-transition: all 0.4s ease-out 0s;
      -webkit-transition: all 0.4s ease-out 0s;
      -o-transition: all 0.4s ease-out 0s;
      color: #3b3064;
      position: relative;
      // z-index: 2;

      &.font_14 {
        font-size: 14px !important;

        @media #{$media-700} {
          font-size: 13px !important;
        }
      }

      @media #{$media-700} {
        height: 40px;
        font-size: 13px;
      }
    }

    &::after {
      display: block;
      width: 100%;
      height: 1px;
      background: #3b3064;
      content: "";
      bottom: 20px;
      position: absolute;
      transition: all 0.4s ease-out 0s;
      -moz-transition: all 0.4s ease-out 0s;
      -webkit-transition: all 0.4s ease-out 0s;
      -o-transition: all 0.4s ease-out 0s;
      z-index: -1;

      @media #{$media-700} {
        bottom: 10px;
      }
    }

    &:hover {
      &::after {
        bottom: 10px;
      }

      .input_fld {
        opacity: 0.5;
      }
    }

    &.form_li_mb_40 {
      margin-bottom: 40px !important;
    }

    @media #{$media-700} {
      width: 100%;
      padding-bottom: 10px;
    }
  }

  @media #{$media-700} {
    margin-left: 0;
    margin-right: 0;
  }
}

.input_fld {
  background: transparent;
  border: none;
  height: 55px;
  position: relative;
  // font-family: var(--aller_lt);
  font-family: "Aller", sans-serif;


  &:focus-visible {
    outline: none;
  }

  &::placeholder {
    // font-weight: bold;
    font-weight: 500;

    color: #3b3064;
  }
}

.textarea {
  &.input_fld {
    height: 100px;
    padding-top: 15px;

    @media #{$media-700} {
      height: 60px !important;
    }
  }

  font-family: inherit;
  // font-family: var(--aller_lt);
  font-family: "Aller",
  sans-serif;

  resize: none;
}

.contact_but {
  min-width: 150px;
  justify-content: center;

  &.contact_but_width {
    min-width: 180px !important;
  }
}

.popup_container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 95%;
  max-width: 880px;
  background-color: white;
  padding: 60px 4%;

  @media #{$media-600} {
    padding: 50px 4%;
  }

  @media #{$media-500} {
    padding: 25px 4%;
  }

  &:focus {
    outline: none;
  }

  .popup_close {
    position: absolute;
    height: 30px;
    width: 30px;
    border-radius: 50%;
    background-color: rgb(255, 255, 255);
    display: flex;
    justify-content: center;
    align-items: center;
    right: 25px;
    top: 25px;
    border: 0;
    cursor: pointer;

    img {
      width: 80%;
      height: auto;
      display: block;
      object-fit: contain;
    }

    @media #{$media-700} {
      right: 15px;
      top: 15px;
    }


  }

  .popup_section {
    margin: 0 !important;
    padding-top: 15px;
    color: #3b3064;

    .head_sec {
      margin-left: auto;
      margin-right: auto;
      max-width: 500px;
      text-align: center;
      margin-bottom: 20px;

      h3 {
        color: #3b3064;
        text-align: center;
        // font-family: "aller_bd";
        font-family: "Aller", sans-serif;

        font-size: 30px;
        line-height: 35px;
        font-weight: 700;
        margin-bottom: 10px;

        @media #{$media-1366} {
          font-size: 27px;
        }

        @media #{$media-1280} {
          font-size: 23px;
        }

        @media #{$media-700} {
          font-size: 18px;
        }
      }

      p {
        color: #3b3064;
        text-align: center;
        // font-family: "aller_rg";
        font-family: "Aller", sans-serif;

        font-size: 15px;
        line-height: 24px;
        font-weight: 400;

        @media #{$media-768} {
          font-size: 14px;
        }
      }
    }

    ul {
      margin: 0;
    }

    .pop_up_input_fld {
      color: #3b3064;

      // font-family: "aller_lt";
      font-family: "Aller", sans-serif;

      label {
        font-weight: 400;

        // font-family: "aller_rg";
        font-family: "Aller", sans-serif;

        font-size: 14px;
      }
    }

    .popup_btn_sec {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      font-family: "aller_rg";
      gap: 25px;
      padding-left: 10px;
      padding-right: 10px;

      .upload_sec {
        display: flex;
        align-items: center;
        gap: 40px;
        font-size: 14px;

        label {
          padding: 0px 30px;
          height: 52px;
          display: flex;
          align-items: center;
          border: 1px solid #3b3064;
          background-color: white;
          color: #3b3064;
          font-family: "aller_rg";
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 14px;

          &:hover {
            border: 1px solid #ffffff;
            background-color: #3b3064;
            color: #ffffff;
          }

          @media #{$media-700} {
            height: 40px !important;
          }
        }

        @media #{$media-768} {
          gap: 20px;
        }
      }

      @media #{$media-700} {
        gap: 15px;
      }
    }
  }
}

.contact_form_main_ul.form_white .form_li .input_fld {
  color: #fff;
}

.contact_form_main_ul.form_white .form_li .input_fld::placeholder {
  color: #fff;
  font-weight: 400;
}

.contact_form_main_ul.form_white .form_li::after {
  background: #fff;
}

.error_msg {
  position: absolute;
  // top: 70%;
  bottom: 0%;
  // top: 40%;
  // transform: translateY(-60%);
  padding: 0;
  right: 0;
  font-family: "aller_rg";
  font-size: 12px !important;
  transition: all 0.3s ease;

  &.resume_error_msg {
    position: unset !important;
  }

  @media #{$media-700} {
    bottom: 10px;
  }
}

.form_li:hover .error_msg {
  transform: translateY(-10px);

  @media #{$media-700} {
    transform: unset;
  }
}

.form_response_msg {
  font-size: 13px !important;
  margin-top: 20px;
  font-family: "aller_rg";

}

:global(body.rtl) {

  .input_fld {
   
    // font-family: "arbfonts", sans-serif;
    font-family: "arbfontsBase", sans-serif;
  }
}