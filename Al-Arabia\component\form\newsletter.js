import React, { useEffect, useState } from "react";
import { useRouter } from 'next/router';
import footer from "@/styles/footer.module.scss";
const Newsletter = () => {
  const { locale } = useRouter();
  const langCode = locale === 'ar' ? 'ar' : 'en';

  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [emessage, setEmessage] = useState('');

  const messages = {
    en: {
      success:
        "Thank You! You are successfully subscribed to get our latest updates.",
      error: "An error occurred. Please try again.",
      placeholder: "Email address",
      submit: "Subscribe",
    },
    ar: {
      success: "شكراً لك! لقد تم الاشتراك بنجاح لتلقي أحدث تحديثاتنا.",
      error: "حدث خطأ ما. يرجى المحاولة مرة أخرى.",
      placeholder: "عنوان البريد الإلكتروني",
      submit: "اشترك",
    },
  };

  const handleSubmit = async (event) => {
    event.preventDefault(); // Prevent the default form submission

    // Sending the email to your REST API
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/subscribers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    const data = await response.json();
    //console.log(data)
    // Handle the response
    if (response.ok) {
      setEmessage('');
      setMessage(messages[langCode].success);
      setEmail('');
      setTimeout(() => {
        setMessage('');
      }, 3000);

    } else {
      setMessage('');
      setEmessage(data.message || messages[langCode].error);
      setEmail('');
      setTimeout(() => {
        setEmessage('');
      }, 3000);
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit}>
        <ul className={`${footer.subscribe_ul}`}>
          <li className={`${footer.sub_block_01}`}>
            <input
              className={`${footer.email_fld} base_font`}
              type="Email"
              placeholder={messages[langCode].placeholder}
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </li>
          <li className={`${footer.sub_block_02}`}>
            <input
              className={`${footer.sub_buttion}`}
              type="Submit"
              value={messages[langCode].submit}
            />
          </li>
        </ul>
      </form>
      {message && <p className={`${footer.validation_msg}`} style={{ color: '#53d553', paddingTop: '5px' , width: '100%' }}>{message}</p>}
      {emessage && <p className={`${footer.validation_msg}`} style={{ color: 'red', paddingTop: '5px' , width: '100%' }}>{emessage}</p>}
    </>
  );
};

export default Newsletter;
