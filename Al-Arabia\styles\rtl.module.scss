@import "variable", "base", "mixin";

@font-face {
    font-family: "ArbFonts";
    src: url("/fonts/arbfonts-ge_thameen_book.woff") format("woff");
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: "ArbFontsBold";
    src: url("/fonts/arbfonts-ge_thameen_demibold.woff") format("woff");
    font-weight: bold;
    font-style: normal;
}

.rtl {

    //  .banner_txt_block{ background: #000;}
    .quote_icn {
        transform: scaleX(-1);
    }

    .home_txt_counder {
        li {
            &::after {
                right: unset;
                left: 0;
            }
        }
    }

    .map_right_block {
        left: 0% !important;
        right: unset !important;

        @media screen and (max-width: 1440px) {
            left: 9% !important;
        }

        @media screen and (max-width: 820px) {
            left: 50% !important;

            transform: translateX(-50%) !important;
        }
    }

    .map_txt_block {
        left: unset !important;
        right: 0% !important;

        @media screen and (max-width: 1440px) {
            right: 9% !important;
        }

        // @media screen and (max-width:700px) {
        @media screen and (max-width: 820px) {
            right: 0% !important;
        }
    }

    .airport_img_text_section {
        li {
            .text_section {
                padding: 65px 5% 65px 0% !important;

                @media #{$media-1440} {
                    padding: 40px 5% 40px 0% !important;
                }

                @media #{$media-1366} {
                    padding: 30px 5% 30px 0% !important;
                }

                @media #{$media-768} {
                    padding: 0px 5% 0px 0% !important;
                }

                @media #{$media-600} {
                    width: 100% !important;
                    padding: 20px 0 0 0 !important;
                }
            }

            &:nth-child(2n) {
                .text_section {
                    padding-left: 5% !important;
                    padding-right: 0% !important;
                }
            }
        }
    }

    .swiper_section {
        padding-right: unset !important;
        padding-left: 5%;
    }

    .detail_breadcrumb,
    .bread_crumb_mian_ul {
        padding-left: unset;
        padding-right: 50px;

        &> :first-child {
            left: unset;
            right: 0;
        }
    }

    .like_section {
        &:first-child {
            padding-right: 0;
            padding-left: unset;
        }

        &:last-child {
            padding-right: 0;
            padding-left: unset;

            & :after {
                display: block;
            }
        }

        &.news_like_section {
            li {
                &::after {
                    left: unset !important;
                    right: -3px;
                    transform: translateY(50%);
                }

                &:first-child {
                    padding-left: 25px !important;
                    padding-right: 0 !important;

                    &::after {
                        display: none;
                    }

                    @media #{$media-1024} {
                        padding-left: 15px !important;
                    }

                    @media #{$media-600} {
                        padding-left: 10px !important;
                    }
                }
            }
        }
    }

    .ml_auto {
        margin-left: unset;
        margin-right: auto;
    }

    .txt_right_block {
        padding-left: unset;
        padding-right: 6%;
    }

    .conter_wrap_outer {
        margin-left: unset;
        margin-right: auto;
    }

    .prev_boul {
        right: 50%;
    }

    .swiper_btn_boul {
        right: 50%;
        transform: rotate(180deg);
    }

    .career_list_left {
        li {
            border-left: solid 1px rgba(59, 48, 100, 0.2);
        }
    }

    .altanfeethi_about {
        padding-right: unset !important;
        padding-left: 8%;
    }

    .tuwqiq_banner_content {
        padding-left: unset !important;
        padding-right: 5%;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        font-family: var(--arbfontsbld) !important;
    }

    a,
    p,
    span,
    label {
        font-family: var(--arbfonts) !important;
    }

    .locationList {
        margin: 15px -12% 0px -3% !important;

        @media #{$media-820} {
            margin: 0 !important;
            margin-top: 20px !important;
        }
    }

    .news_date {
        height: 24px;
    }

    .news_list_ul {
        li {
            &::after {
                right: -6%;
                left: -7%;

                @media #{$media-1280} {
                    right: -4%;
                    left: -4%;
                }

                @media #{$media-700} {
                    margin-top: 0;
                    right: -3%;
                    left: -3%;
                }

                @media #{$media-500} {
                    right: 0;
                    left: 0;
                }
            }

            &::before {
                left: unset;
                right: 0;
            }

            &:hover {

                p,
                a,
                .news_date {
                    color: #fff;
                    opacity: 1;
                    transform: translateX(-10px) !important;
                }
            }

            &:first-child {
                // padding-top: 0px;
                // min-height: 167px;

                &::before {
                    // bottom: -2px;

                    @media #{$media-1440} {
                        // bottom: 1px;
                    }
                }

                &:after {
                    // height: 118%;

                    @media #{$media-1440} {
                        // height: 110%;
                    }

                    @media #{$media-700} {
                        //     height: 100%;
                    }
                }

                &::after {
                    // transform: translateY(10px);

                    @media #{$media-1280} {
                        transform: unset;
                    }
                }
            }

            &:last-child {
                &::before {
                    @media #{$media-1440} {
                        // bottom: 25px;
                    }

                    @media #{$media-1280} {
                        // bottom: 2px;
                    }
                }
            }

            &:nth-child(2):hover {
                &::after {
                    // height: 99%;

                    @media #{$media-1440} {
                        // height: 104%;
                        // bottom: -3px;
                    }
                }
            }

            &:last-child:hover {
                &::after {
                    // height: 91%;
                    transform: translateY(-10px);

                    @media #{$media-1440} {
                        // height: 91%;
                        transform: translateY(-18px);
                    }

                    @media #{$media-1280} {
                        // height: 100%;
                        transform: translateY(-4px);
                    }
                }
            }
        }
    }

    .ul_section li .ul_content li span {
        // font-family: var(--arbfonts) !important;
    }

    // .ul_section li:hover .ul_content li  span {
    .ul_section li:hover .ul_content li>span {
        transform: translateX(-10px);

        @media #{$media-700} {
            transform: translateX(0px);
        }
    }

    .ul_section li:hover .ul_content li p {
        transform: translateX(10px);

        @media #{$media-700} {
            transform: translateX(-0px);
        }
    }

    .breadcrumb {
        display: flex;
        list-style: none;
        margin-top: 15px;

        li {
            span {
                border-left: 2px solid rgba(59, 48, 100, 0.377);
                border-right: unset !important;

                @media #{$media-820} {
                    padding: 10px 0px;
                }
            }

            &:first-child span {
                padding-left: 15px !important;
                padding-right: 0 !important;
            }

            &:last-child span {
                border-right: unset !important;
                border-left: 0px solid rgba(59, 48, 100, 0.377);
            }
        }

        &.px_unset {
            li {
                &:first-child span {
                    padding-left: 0px !important;
                    padding-right: 0 !important;
                }
            }
        }
    }

    .news_date_block {
        right: 15px;
        left: unset;
    }

    .project_tab {
        margin-right: auto;
        margin-left: unset;
    }

    .season_image {
        img {
            object-position: right !important;
        }
    }

    .mobile_menu_sub_list {
        .submenu_list {
            @media #{$media-1024} {
                left: 0;
                right: 50%;
            }
        }
    }

    .language_switch {
        left: 20px !important;
        right: unset !important;

        &.language_switch_globe {
            @media #{$media-890} {
                left: 5px;
                right: unset;
            }
        }

        @media #{$media-1024} {
            margin-right: auto;
            margin-left: unset;
        }
    }

    .explore_btn {
        @media #{$media-820} {
            right: 60px;
            left: unset;
        }

        @media #{$media-500} {
            right: 50px;
            left: unset;
        }
    }

    .footer_cl_01 {
        padding-right: 0%;
        padding-left: 3%;
    }

    .footer_bot_link_outer_bot {
        width: 41% !important;

        .footer_bot_link_ul {
            li {
                text-align: left !important;

                &:nth-child(2) {
                    padding-right: unset !important;
                }

                &:last-child {
                    padding-right: unset !important;
                }
            }
        }

        @media #{$media-700} {
            width: 100% !important;
        }
    }

    @import "variable", "base", "mixin";

    .airplane_wrap {
        top: -500px;
        // top: 0px;
        position: absolute;
        width: 80%;
        height: auto;
        z-index: 1;
        // right: 0;
        right: -25px;
        pointer-events: none;

        >svg {
            width: 100%;

            @media #{$media-600} {
                // height: 100%;
            }
        }

        @media #{$media-1599} {
            top: -50%;
        }

        @media #{$media-1440} {
            top: -60%;
        }

        @media #{$media-1366} {
            top: -68%;
        }

        @media #{$media-1024} {
            top: -80%;
        }

        @media #{$media-768} {
            top: -90%;
        }

        @media #{$media-600} {
            top: -75%;
            width: 100%;
        }

        @media #{$media-500} {
            // top:-92%;
            top: -86%;
        }

        @media #{$media-430} {
            top: -90%;
        }

        @media #{$media-400} {
            top: -95%;
        }
    }

    .airplane_wrap_new {
        right: unset !important;
        left: -20px !important;

        transform: scaleX(-1);
    }

    .text_container {
        padding-left: unset !important;
        padding-right: 5% !important;

        @media #{$media-768} {
            padding-right: 0% !important;
        }
    }

    .home_txt_01 {
        h1 {
            font-family: var(--arbfonts) !important;

            span {
                font-family: var(--arbfonts) !important;
            }
        }
    }

    // ----for pop up secions------

    .popup_container {
        .popup_section {
            .head_sec {

                h3,
                p {
                    font-family: "ArbFonts" !important;

                    // font-family: var(--arbfonts) !important;
                }
            }
        }

        .popup_close {
            right: unset !important;
            left: 25px !important;

            @media #{$media-700} {
                left: 15px !important;
            }
        }
    }

    // .custom_select {
    //     :global {
    //         .selectes {
    //             &__single-value {
    //                 font-family: var(--arbfontsbld) !important;
    //             }
    //         }
    //     }
    // }

    .campaign_list_ul {
        &.special_label {
            li {
                >label {
                    padding-right: 7px !important;
                    padding-left: 15px !important;
                }
            }
        }
    }

    .error_msg {
        right: unset;
        left: 0;
        font-family: "ArbFonts" !important;
    }

    .form_response_msg {
        font-family: "ArbFonts" !important;
    }

    .contact_form_main_ul {
        .input_fld {
            // font-family: var(--arbfonts) !important;
            font-family: "ArbFonts" !important;
        }
    }

    .arb_font {
        font-family: "ArbFonts" !important;
    }

    .bot_left_block {
        h3 {
            padding-right: unset !important;
            padding-left: 3%;
        }
    }
}