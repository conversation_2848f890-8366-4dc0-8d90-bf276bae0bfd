@import "variable", "base", "mixin";

.section_project_category {
  width: 100%;

  background-color: #6758b6;
  align-items: center;

  .project_logo {
    // width: 30%;
    width: 27%;
    padding-right: 5%;
    min-height: 140px;
    border-right: 1px solid #8576d3;
    display: flex;
    align-items: center;

    img {
      max-width: 100%;
    }

    @media #{$media-700} {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 0;
      border-bottom: 1px solid #fff;
      padding-right: 0%;
      margin-bottom: 20px;
    }
  }

  .project_icons_list {
    width: 73%;
    padding-left: 6%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;

    ul {
      width: 100%;
      justify-content: space-between;
      gap: 20px;

      li {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        position: relative;
        cursor: pointer;

        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;

        .project_icon {
          max-width: 60px;
          height: 60px;
          // height: auto;
          max-height: 60px;
          margin-bottom: 20px;
          display: flex;
          align-items: center;
          position: relative;

          >img {
            object-fit: contain;
          }

          @media #{$media-1280} {
            margin-bottom: 10px;
          }
        }

        h4 {
          width: 100%;
          text-align: center;
          color: #fff;
          font-size: 18px; // Replace @include rem(18) if no mixin is available
          margin-bottom: 0;
          font-weight: 400;

          @media #{$media-1280} {
            font-size: 16px;
          }

          @media #{$media-700} {
            font-size: 14px;
          }

        }

        .tooltip {
          visibility: hidden;
          opacity: 0;
          transition: opacity 0.1s ease-in-out, visibility 0.1s ease-in-out;

          @media #{$media-700} {
            visibility: visible;
            opacity: 1;
            position: unset;
            text-align: center;
            margin-top: 10px;
            width: 100%;
            height: auto;
            padding: 15px !important;
            margin-bottom: 25px;
          }

          p {
            @media #{$media-700} {
              font-size: 13px !important;
              line-height: 18px;
            }
          }
        }

        &:hover .tooltip {
          visibility: visible;
          opacity: 1;
        }

        &:active {
          background: none;
        }

        @media #{$media-700} {
          width: 47%;
        }
      }

      @media #{$media-700} {
        justify-content: center;
      }
    }

    @media #{$media-700} {
      width: 100%;
      padding-left: 0;
    }

    @media #{$media-700} {
      padding-bottom: 35px;
    }
  }
}

.project_logo_icon {
  width: 100%;
  padding-right: 30px;
  // margin-bottom: 20px;
  margin-bottom: 35px;
  color: #fff;

  h2 {
    font-size: 50px;
    font-weight: 500;

    @media #{$media-1024} {
      font-size: 40px;
    }

    @media #{$media-768} {
      font-size: 30px;
    }
  }

  img {
    width: 100%;
    max-width: 304px;
    object-fit: contain;
    display: block;
    height: auto;
  }

  @media #{$media-1024} {
    max-width: 200px;
    width: 50%;
  }

  @media #{$media-700} {
    margin: auto;
    margin-bottom: 10px;
  }
}

:global(body.rtl) {
  .project_logo_icon {
    padding-right: 0;
    padding-left: 30px;
  }
}

.project2_slider_section {
  background: rgb(74, 74, 74, 1);
  background: linear-gradient(107.56deg, #4b4b4b 0%, #000000 48.85%);

  .project_tab {
    background: rgba(246, 246, 246, 0.1);

    li {
      width: 50%;
      min-height: 75px;
      opacity: 0.5;

      @media #{$media-768} {
        min-height: 55px;
        padding: 10px;
      }

      &.active {
        background: #fff;
        opacity: 1;

        // img{
        //   filter: invert(1);
        // }
      }
    }
  }
}

.project_slider_section {
  // background: rgb(103,90,167);
  // background: linear-gradient(171deg, rgba(103,90,167,1) 0%, rgba(58,48,100,1) 88%);
}

.project_title {
  width: 100%;
  align-items: flex-start;

  &.min_width {
    p {
      max-width: 420px;
      width: 100%;

      @media #{$media-700} {
        max-width: unset;
      }
    }
  }

  h3 {
    color: #fff;
    margin-bottom: 10px;
  }

  p {
    color: #fff;

    @media #{$media-700} {
      text-align: center;
    }
  }

  &.project_title_maleem {
    h3 {
      margin-bottom: 30px !important;

      @media #{$media-1366} {
        margin-bottom: 29px !important;
      }

      @media #{$media-768} {
        margin-bottom: 10px !important;
      }
    }
  }
}

.project_tab {
  width: 100%;
  max-width: 300px;
  display: flex;
  flex-wrap: wrap;
  background: rgba(246, 246, 246, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.15);
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  border-radius: 20px;
  margin-left: auto;

  @media #{$media-768} {
    margin-top: 0px;
    margin-left: inherit;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
  }

  li {
    width: 50%;
    min-height: 75px;

    @media #{$media-768} {
      min-height: 55px;
      padding: 10px;
    }

    &.active {
      background-color: #6758b6;
      -webkit-border-radius: 20px;
      -moz-border-radius: 20px;
      border-radius: 20px;
      margin: -1px;

      @media #{$media-768} {
        -webkit-border-radius: 10px;
        -moz-border-radius: 10px;
        border-radius: 10px;
      }
    }

    a {
      width: 100%;
      height: 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
    }
  }

  @media #{$media-700} {
    max-width: unset;
  }
}

.project_slider {
  width: 100%;
  min-height: 650px;

  &.min_height_unset {
    min-height: unset !important;

    @media #{$media-1024} {
      min-height: unset !important;
    }
  }

  &.maalem_page {
    width: auto;
    margin: 0 -2%;
  }

  .project_slider_main {
    width: 100%;
    max-width: 580px;
    margin: 0 auto;
  }

  .project_slide_img {
    width: 100%;

    img {
      width: 100%;
      height: auto;
    }
  }

  .project_slider_btn {
    margin: 0 auto;
    width: 90px;
    display: flex;
    justify-content: space-between;
    position: relative;
    z-index: 9;
    bottom: 40px;

    &.project_slider_btn_second {
      bottom: 170px;
    }
  }
}

.project_second_section {
  width: 100%;

  .project_logo_center {

    @media #{$media-700} {
      display: flex;
      justify-content: center;
    }

  }

  .maalem_btn {
    padding: 0 6.55%;
  }

  .w_45 {
    width: 49%;

    @media #{$media-768} {
      width: 100%;
    }
  }

  .w_55 {
    width: 55%;

    &.project_description {
      max-width: 611px;
      width: 100%;

      @media #{$media-700} {
        max-width: unset;
      }
    }

    &.project_description_maalem {
      max-width: 590px;

      @media #{$media-700} {
        max-width: unset;
      }
    }

    @media #{$media-768} {
      width: 100%;
      margin-top: 25px;
    }

    @media #{$media-500} {
      margin-top: 5px;
    }
  }

  p {
    color: #fff;

    @media #{$media-700} {
      text-align: center;
    }
  }

  a {
    padding: 0 6.26%;
  }
}

.project_description h5 {
  @include rem(18);
  color: #fff;
  font-weight: 400;
}

.project_partner_slider {
  width: 100%;

  h3 {
    color: #fff;
    @include rem(22);
    font-family: var(--aller_lt);
    font-weight: 400;

    @media #{$media-820} {
      @include rem(28);
    }

    @media #{$media-700} {
      display: none;
    }
  }

  .project_partner_logo_sec {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 10px;

    li {
      max-width: 25%;
      max-height: 70px;

      div {
        width: auto;
        height: auto;

        img {
          height: auto;
          max-width: 100%;
          display: block;
          object-fit: contain;
        }
      }
    }
  }
}

.black_title h3,
.black_title p {
  color: #000 !important;
}

.black_title p {
  font-size: 15px;

  @media #{$media-768} {
    font-size: 14px;
  }
}

.title_with_img {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  font-weight: 400;
  color: #fff;
  line-height: 30px;

  .logo_sec {
    max-width: 300px;
    height: 50px;

    >img {
      height: 100%;
      width: 100%;
      object-fit: contain;
      display: block;
    }

    @media #{$media-1366} {
      max-width: 250px;

    }

    @media #{$media-700} {
      max-width: 200px;

    }
  }

  img {
    margin: 0 10px;
  }

  @media #{$media-1366} {
    font-size: 16px;


  }

  @media #{$media-700} {
    font-size: 14px;


  }
}

.project_partner_logo_sec {
  img {
    max-width: 100%;
  }
}

.col_rev_mob {
  @media #{$media-700} {
    flex-direction: column-reverse;
  }
}

.mob_margin {
  @media #{$media-700} {
    margin-bottom: 20px;
  }
}

:global(body.rtl) .section_project_category .project_logo {
  padding-right: 0%;
  padding-left: 5%;
  border-right: none;
  border-left: 1px solid #8576d3;

  @media #{$media-700} {
    border-left: none;
    padding-left: 0;
  }
}

:global(body.rtl) .section_project_category .project_icons_list {
  padding-left: 0;
  padding-right: 6%;

  @media #{$media-700} {
    padding-right: 0;
  }
}