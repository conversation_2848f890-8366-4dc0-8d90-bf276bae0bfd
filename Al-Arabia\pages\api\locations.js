import J<PERSON>Z<PERSON> from 'jszip';
import { parseStringPromise } from 'xml2js';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { kmlUrl } = req.query;

  if (!kmlUrl) {
    return res.status(400).json({ error: 'Missing kmlUrl query parameter' });
  }

  try {
    const response = await fetch(kmlUrl);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Failed to fetch KML/KMZ from ${kmlUrl}: ${response.status} ${response.statusText} - ${errorText}`);
      return res.status(response.status).json({ error: `Failed to fetch KML/KMZ: ${response.statusText}`, detail: errorText });
    }

    const buffer = await response.arrayBuffer();
    let kmlText;
    const contentType = response.headers.get('content-type');

    if (contentType?.includes('application/vnd.google-earth.kmz') || kmlUrl.endsWith('.kmz')) {
      const zip = await JSZip.loadAsync(buffer);
      const kmlFile = Object.keys(zip.files).find((name) => name.endsWith('.kml'));

      if (!kmlFile) {
        return res.status(400).json({ error: 'No .kml file found inside .kmz' });
      }
      kmlText = await zip.file(kmlFile).async('text');
    } else {
      kmlText = new TextDecoder().decode(buffer);
    }

    const parsed = await parseStringPromise(kmlText);
    const placemarks = parsed?.kml?.Document?.[0]?.Folder?.[0]?.Placemark || parsed?.kml?.Document?.[0]?.Placemark || [];

    if (!placemarks || placemarks.length === 0) {
      console.warn(`No placemarks found in map: ${kmlUrl}`);
      return res.status(200).json({ coordinates: [] });
    }

   // console.log("test the city co-ords", placemarks);
    const coordinates = placemarks
      .filter(p => p.Point && p.Point[0].coordinates && p.Point[0].coordinates[0].trim())
      .map(p => {
        const coordText = p.Point[0].coordinates[0].trim();
        const [longitude, latitude] = coordText.split(',').map(Number);
        return { longitude, latitude };
      });

    res.status(200).json({ coordinates });
  } catch (err) {
    console.error('Error parsing KML:', err);
    res.status(500).json({ error: 'Failed to parse KML', detail: err.message });
  }
}
