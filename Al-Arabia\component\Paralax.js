import { useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

const Parallax = ({ mediaSrc, altText, isFull = false, isVideo = false, additionalMedia = [] }) => {
  useEffect(() => {
    const hasParallax = gsap.utils.toArray('.has-parallax');

    hasParallax.forEach((hParallax) => {
      const bgMedia = gsap.utils.toArray(
        hParallax.querySelectorAll('.img, video')
      );

      const parallax = bgMedia.map((media) =>
        gsap.fromTo(
          media,
          { y: '-20%', scale: 1.15 },
          { y: '20%', scale: 1, duration: 1, ease: 'none' }
        )
      );

      parallax.forEach((anim) => {
        ScrollTrigger.create({
          trigger: hParallax,
          start: 'top 100%',
          end: () => `+=${hParallax.offsetHeight + window.innerHeight}`,
          animation: anim,
          scrub: true,
        });
      });
    });

    return () => {
      ScrollTrigger.getAll().forEach((st) => st.kill());
    };
  }, []);

  return (
    <>
      <style jsx>{`
        .has-parallax {
          overflow: hidden;
        }

        .full.has-parallax {
          overflow: hidden;
          height: 100vh;
          position: relative;
        }

        .has-parallax .img, .has-parallax video {
          max-width: 100%;
        }

        .has-parallax-content > .img,
        .has-parallax-content > video,
        .has-parallax > img,
        .has-parallax > video {
          width: 100%;
          height: 100%;
          position: absolute;
          display: block;
          object-position: center;
          object-fit: cover;
        }
      `}</style>
      <figure className={`has-parallax ${isFull ? 'full' : ''}`}>
        {isVideo ? (
          <video src={mediaSrc} autoPlay muted loop playsInline className="img" />
        ) : (
          <img src={mediaSrc} className="img" alt={altText} />
        )}
        {additionalMedia.map((media, index) => (
          media.isVideo ? (
            <video
              key={index}
              src={media.src}
              autoPlay
              muted
              loop
              playsInline
              className="img"
            />
          ) : (
            <img key={index} src={media.src} alt={media.alt} className="img" />
          )
        ))}


         
      </figure>
    </>
  );
};

export default Parallax;

// Example Usage:
// <Parallax 
//   mediaSrc="/images/hero.jpg" 
//   altText="Main Image" 
//   isFull 
//   additionalMedia={[{ src: '/images/secondary.jpg', alt: 'Secondary Image' }]} 
// />
// <Parallax 
//   mediaSrc="/videos/hero.mp4" 
//   altText="Main Video" 
//   isFull 
//   isVideo 
//   additionalMedia={[{ src: '/videos/secondary.mp4', isVideo: true }]} 
// />
