@import "variable", "base", "mixin";

.test_height {
  height: 1000px;
  font-family: var(--helveticaneuelt_arabic_55);
}

.h1 {
  font-size: 4.688rem;
  font-weight: normal;
  font-family: var(--helveticaneueltarabicBold);
  line-height: 100%;

  span {
    background: linear-gradient(-90deg, rgba(227, 181, 255, 1) 0%, rgba(94, 69, 255, 1) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: left;
    display: inline-block;
  }
}

.text_capitalize {
  text-transform: capitalize;
}

.h1 {
  h3 {
    @include rem(40);
    color: #3B3064;
    line-height: 3rem
  }
}


.title_02 {
  h3 {

    @include rem(26);
    font-family: var(--aller_lt);
    @include rem(28);
    color: #fff;
    font-weight: 400;
    color: #3B3064;

    @media #{$media-768} {
      @include rem(30);
    }

  }
}



.custom_but {
  position: absolute;
  top: 50%;
  margin-top: -7px;
  z-index: 900;
  cursor: pointer;
  height: auto;
  width: 15px;

  .img {
    height: auto;
    width: 100%;
    object-fit: contain;
  }

  &.custom_but_next {
    // right: -5%;
    left: calc(100% + 3%);

    @media #{$media-1440} {
      left: calc(100% + 1%);



    }

    @media #{$media-1024} {
      // right: 5px;
      left: calc(100% + 5px);

    }

  }

  &.custom_but_prev {
    // left: -5%;
    right: calc(100% + 3%);

    @media #{$media-1440} {
      right: calc(100% + 1%);



    }

    @media #{$media-1024} {
      // left: 5px;
      right: calc(100% + 5px);

    }
  }

  &:hover {
    opacity: 0.8;
  }
}

.project_wrap {


  @media #{$media-1024} {
    padding: 0 40px;

  }
}

.gap_10 {
  gap: 10px;
}

.overflow_hide {
  overflow: hidden;
}

.more_than_sec {

  @media #{$media-820} {
    flex-direction: column;
    gap: 20px;
  }

}

.more_than_counter {

  @media #{$media-820} {
    margin: 0 !important;
  }
}

.buttion {
  display: inline-flex;
  padding: 0 20px;
  border: solid 1px #ccc;
  color: #fff;
  @include rem(16);
  height: 42px;
  align-items: center;

  &:hover {
    background: rgba(88, 72, 170, 0.9);
  }

  &.but_blue {
    border: solid 1px #6758B6;
    color: #6758B6;


    &:hover {
      color: #fff;
    }
  }


  &.but_white {
    background: #fff;

    &:hover {
      color: #6758B6;
      background: #e8e1ff;
    }
  }


  @media #{$media-700} {
    height: 35px;
    @include rem(18);
  }
}

.paralax_test {
  width: 50%;
}


.test_paralax{ display: flex; flex-wrap: wrap;
  li{ width: 25%;  list-style: none;}
}

.link{ color: #6758B6; text-decoration: underline;}


 