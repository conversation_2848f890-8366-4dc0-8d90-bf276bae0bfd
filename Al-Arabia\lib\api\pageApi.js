// API for fetching page data

export const fetchPageById = async (slug, langCode) => {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/pages?slug=${slug}&acf_format=standard&lang=${langCode}`);
    
    // if (!response.ok) {
    //   throw new Error('Network response was not ok');
    // }
  
    const data = await response.json();
    return data[0];
};


  // API for fetching posts type data

  export const fetchPostList = async (postype,mediaCount,langCode,excludeSlug) => {
    let url = `${process.env.NEXT_PUBLIC_API_BASE_URL}/${postype}?acf_format=standard&per_page=${mediaCount}&lang=${langCode}&_embed`;
    
    if (excludeSlug) {
      const encodedSlug = encodeURIComponent(excludeSlug); 
      url += `&slug_exclude=${encodedSlug}`;  
  }
  
    
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    const data1 = await response.json();
    return data1
  };

  // Single
export const fetchByPost = async (postype, slug, langCode) => {
  if (!slug) {
      throw new Error('Slug is required');
  }
  
  const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/${postype}?slug=${slug}&acf_format=standard&lang=${langCode}&_embed`;
  //console.log('Fetching URL:', apiUrl); 
  
  const response = await fetch(apiUrl);
  
  if (!response.ok) {
      throw new Error(`Network response was not ok: ${response.statusText}`);
  }
  
  const data = await response.json();
  return data[0];
  };


  // fetch by option page

  export const fetchCommonSec = async (fieldName,langCode) => {
    const response2 = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/option-field/${fieldName}?lang=${langCode}`);
  
    if (!response2.ok) {
      const errorMessage = `Error ${response2.status}: ${response2.statusText}`;
      throw new Error(errorMessage);
    }
  
    const data = await response2.json();
    return data;
  };