import React, { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import comon from "@/styles/comon.module.scss";
import csr from "@/styles/csr.module.scss";
import { useRouter } from "next/router";
const ImageBlock = ({ list, extraBlock = false }) => {
const { locale } = useRouter()
  const images = list.flatMap(item => item.image);
  const [visibleCount, setVisibleCount] = useState(0);

  useEffect(() => {
    const handleResize = () => {
      const isMobile = window.innerWidth <= 768;
      setVisibleCount(isMobile ? 8 : 8);
    };

    handleResize(); // Set on mount
    window.addEventListener("resize", handleResize); // Optional: respond to resize

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const handleLoadMore = () => {
    setVisibleCount((prev) => Math.min(prev + 8, images.length));
  };

  // useEffect(() => {
  //   console.log("llllllllllllllllllllllll", images)
  // },[images])
  return (
    <div>
      <section
        className={`${comon.pb_65}`}
        data-aos="fade-in"
        data-aos-duration="1000"
      >
        <div className={`${comon.wrap}`}>
          <ul className={`${csr.block_ul}`}>
          {images.slice(0, visibleCount).map((url, srcIndex) => (
              <li
                key={`item-${srcIndex}`}
                // data-aos="fade-up"
                // data-aos-duration="1000"
              >
                <div
                  style={{ aspectRatio: "1 / 1" }}
                  className={`${comon.w_100} ${comon.p_relative} ${comon.hover_visible_sec} ${comon.image_hover}`}
                >
                  <Image
                    src={url}
                    fill
                    className={comon.img}
                    style={{ objectFit: "cover" }}
                    alt=""
                    quality={100}
                  />
                  {extraBlock && (
                    <div className={comon.hover_visible}>
                      <div className={comon.hover_visible_container}>
                        <h4>Triplets</h4>
                        <p>
                          Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                          Curabitur consectetur.
                        </p>
                        <Link href="/#">Check Locations</Link>
                      </div>
                    </div>
                  )}
                </div>
              </li>
            ))}

          </ul>

          {visibleCount < images.length && (
            <div className={comon.text_center} style={{ marginTop: "2rem" }}>
              <button className={`${comon.buttion} ${comon.but_fill} ${comon.but_h_02}`} onClick={handleLoadMore}>
              {locale === 'ar' ? 'عرض المزيد' : 'Show More'}
              </button>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default ImageBlock;
