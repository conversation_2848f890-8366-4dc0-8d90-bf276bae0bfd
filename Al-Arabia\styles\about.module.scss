@import "variable", "base", "mixin";

.left_block {
    // width: 18.5%;
    width: 15.56%;
    // max-width: 296px;
    background: #e1e2e1;
    position: relative;

    @media #{$media-1600} {
        width: 18.5%;
    }

    @media #{$media-820} {
        width: 100%;
        position: sticky;
        z-index: 10;
        top: 0px;
    }
}

.right_block {
    // width: calc(100% - 296px);
    // width: 81.5%;

    width: calc(100% - 15.56%);

    @media #{$media-1600} {
        width: 81.5%;
    }

    @media #{$media-820} {
        width: 100%;
    }
}

.left_block_block {
    background: #e1e2e1;
    width: 100%;
    border-right: solid 1px #d5d6d6;
    position: sticky;
    // top: 45%;
    top: 39%;
    padding: 15%;

    // @media #{$media-768} {
    //     // padding: 6%;
    //     padding: 4%;
    //     padding-top: 30px;
    //     padding-bottom: 25px;
    // }
    @media #{$media-820} {
        // padding: 6%;
        padding: 4%;
        padding-top: 20px;
        padding-bottom: 20px;
        overflow: auto !important;
        position: unset;
    }
}

.left_block_block {
    h6 {
        @include rem(17);
        font-family: var(--aller_bd);
        color: #6758b6;
        margin-bottom: 20px;

        @media #{$media-820} {
            @include rem(25);
            margin-bottom: 10px;
        }

        @media #{$media-700} {
            @include rem(25);
            margin-bottom: 10px;
        }
    }

    ul {
        li {
            margin-bottom: 15px;

            span,
            a {
                @include rem(17);
                color: #857ab8;

                @media #{$media-1024} {
                    font-size: 14px;
                }

                @media #{$media-700} {
                    font-size: 13px;
                }
            }
        }
    }

    @media #{$media-700} {
        padding-top: 10px;
        padding-bottom: 10px;
    }
}

.message_block_sec {
    flex-wrap: nowrap !important;

    @media #{$media-1024} {
        flex-wrap: wrap !important;
    }

    .message_block_anim {
        transition: all 0.5s ease;

        @media #{$media-1024} {
            width: 100% !important;
        }

        .no_wrap {
            flex-wrap: nowrap !important;
        }

        .message_img {
            flex-shrink: 0;
            transition: all 0.5s ease;
        }

        .message_txt {
            // max-width: 500px;
            flex-direction: column;

            p {
                margin-top: 10px;
                max-width: 430px;

                @media #{$media-1600} {
                    max-width: 400px;
                }

                @media #{$media-1440} {
                    max-width: 275px;
                }

                @media #{$media-1366} {
                    max-width: 255px;
                }

                @media #{$media-1280} {
                    max-width: 230px;
                }

                @media #{$media-1024} {
                    max-width: 100%;
                    font-size: 14px;
                    line-height: 130%;
                }
            }
        }

        &:hover {
            width: 55% !important;

            @media #{$media-1024} {
                width: 100% !important;
            }
        }
    }

    .message_block_anim:hover .message_img {
        transform: scale(1.1);

        @media #{$media-1440} {
            transform: scale(1.02);
        }

        @media #{$media-1024} {
            transform: scale(1);
        }
    }

    .message_block_anim:hover~.message_block_anim .message_img,
    .message_block_anim:has(~ .message_block_anim:hover) .message_img {
        transform: scale(0.8);

        @media #{$media-1440} {
            transform: scale(0.9);
        }

        @media #{$media-1024} {
            transform: scale(1);
        }
    }
}

.message_block {
    padding: 4%;

    @media #{$media-820} {
        padding-top: 40px;
        padding-bottom: 40px;
    }
}

.message_img {
    width: 138px;
    height: 138px;
    aspect-ratio: 1 / 1;
    border-radius: 100%;
    overflow: hidden;

    @media #{$media-820} {
        width: 80px;
        height: 80px;
    }
}

.message_txt {
    width: calc(100% - 138px);
    padding-left: 5%;
    padding-right: 5%;

    p {
        color: #fff;
        @include rem(16);
        font-weight: 200;
        line-height: 1.625rem;
        text-transform: none;

        @media #{$media-700} {
            @include rem(18);
        }
    }

    @media #{$media-820} {
        width: 100%;
        // margin-top: 25px;
    }
}

.message_txt_02 {
    width: 100%;

    p {
        color: #fff;
        @include rem(16);

        @media #{$media-700} {
            @include rem(18);
        }
    }

    h5 {
        color: #fff;
        @include rem(18);
        font-weight: normal;
        font-family: var(--aller_bd);
    }
}

.mission_vission_block {
    p {
        color: #3b3064;
        // @include rem(15);
        line-height: 1.5rem;
        max-width: 550px;

        @media #{$media-1024} {
            font-size: 14px;
            line-height: 160%;
        }

        @media #{$media-700} {
            font-size: 13px;
            line-height: 18px;
        }
    }

    &.bringing_block {
        h3 {
            color: #3b3064;
            // font-size: 30px;
            font-family: "Aller", sans-serif;

            @include rem(30);
            font-weight: 700;
            margin-bottom: 35px;

            @media #{$media-1280} {
                margin-bottom: 15px;
            }

            @media #{$media-768} {
                margin-bottom: 10px;
            }
        }

        p {
            max-width: 580px;
        }
    }

    &.airplane_block {
        padding: 0 !important;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .airplane_img {
            width: 100%;
            height: auto;

            >img,
            svg {
                height: auto;
                width: 100%;
                display: block;
                object-fit: contain;
            }

            @media #{$media-600} {
                height: 30px;
            }
        }
    }
}

.px_about {
    padding: 50px 4%;

    &.px_about_min {
        padding: 50px 2.5%;

        @media #{$media-700} {
            padding: 30px 4%;
        }
    }

    &.padding_zero_block {
        padding: 50px 0% !important;

        @media #{$media-820} {
            padding: 30px 0% !important;
        }
    }

    @media #{$media-820} {
        padding: 30px 4%;
    }
}

.subsidiaries_block {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    li {
        list-style: none;
        width: 25%;
        // padding: 5%;
        padding: 3.3% 2.5%;
        display: flex;
        flex-wrap: wrap;
        overflow: auto;
        text-transform: none;

        h3 {
            @include rem(20);
            color: #3b3064;
            // line-height: 1.25rem;
            line-height: 125%;
            margin-bottom: 5px;

            @media #{$media-1024} {
                margin-bottom: 10px;
            }

            @media #{$media-1000} {
                @include rem(23);
            }
        }

        p {
            color: #3b3064;
            font-size: 15px;
            line-height: 24px;

            @media #{$media-1440} {
                line-height: 1.5rem;
            }

            @media #{$media-1000} {
                font-size: 14px;
                line-height: 1.8rem;
            }
        }

        @media #{$media-1366} {
            padding: 3%;
        }

        @media #{$media-820} {
            padding: 2%;
        }

        @media #{$media-700} {
            width: 100%;
            margin-top: 10px;
            margin-bottom: 10px;
            border: solid 1px #dfdfdf;
            padding: 6%;
            aspect-ratio: unset !important;
        }

        &:hover {
            visibility: visible;
            opacity: 1;
        }

        @media #{$media-700} {
            min-height: 150px;
        }
    }

    @media #{$media-700} {
        padding-left: 4%;
        padding-right: 4%;
    }

    & :nth-child(5) {
        @media #{$media-700} {
            background: #3b3064;
            order: 6;
        }
    }
}

.sustainability_container {
    min-height: 582px;

    // display: flex;
    // align-items: center;
    @media #{$media-1440} {
        min-height: unset;
    }
}

.sustainability_block {
    background: rgba(103, 88, 182, 0.9);
    padding: 60px 4%;
    width: 49.5%;
    max-width: 590px;

    h3 {
        @include rem(30);
        color: #fff;
        font-weight: 400;
        margin-bottom: 15px;

        @media #{$media-820} {
            @include rem(36);
        }
    }

    p {
        color: #fff;
        margin-bottom: 25px;
        // font-size: 18px;
        font-size: 15px;

        // line-height: 1.5rem;
        line-height: 150%;
        font-weight: 400;
        font-family: var(--aller_lt);

        // &+p {
        //     line-height: 160%;
        //     font-size: 15px;

        //     @media #{$media-700} {
        //         font-size: 14px;
        //     }
        // }

        // @media #{$media-1366} {
        //     font-size: 16px;

        // }

        @media #{$media-700} {
            font-size: 14px;
            margin-bottom: 10px;
        }
    }

    @media #{$media-1366} {
        padding: 4%;
    }

    @media #{$media-820} {
        width: 100%;
    }
}

.airplane_section {
    @media #{$media-700} {
        flex-direction: column-reverse;
    }
}

.two_cl_ul {
    // margin-left: -2%;
    // margin-right: -2%;
    display: flex;

    flex-wrap: wrap;
    width: 100%;

    &.padding_space {
        li {
            padding-left: 3%;
            padding-right: 3%;

            @media #{$media-1600} {
                padding-left: 5%;
                padding-right: 5%;
            }

            @media #{$media-1400} {
                padding-left: 3%;
                padding-right: 3%;
            }
        }
    }

    &.kkia_ul_sec {
        gap: 20px;

        li {
            width: calc(50% - 10px);
            padding-left: 0%;
            padding-right: 0%;

            p {
                @media #{$media-700} {
                    max-width: 100%;
                }
            }

            @media #{$media-700} {
                width: 100%;
            }
        }

        @media #{$media-700} {
            gap: 0px;
        }
    }

    li {
        width: 50%;
        padding-left: 4%;
        padding-right: 4%;

        @media #{$media-1280} {
            padding-top: 15px;
            padding-bottom: 15px;
        }

        @media #{$media-820} {
            padding-top: 5px;
            padding-bottom: 5px;
        }

        @media #{$media-700} {
            width: 100%;

            padding-bottom: 18px;
        }
    }
}

.right_block_ul {
    margin-left: -5px;
    margin-right: -5px;

    li {
        padding: 0px 5px;
        margin-bottom: 15px !important;

        &.max_width {
            max-width: 200px;

            @media #{$media-820} {
                max-width: unset;
            }
        }

        span,
        a {
            color: #857ab8;
            cursor: pointer;
            line-height: 140%;

            &.active {
                color: #453299 !important;
                font-weight: 700;
                font-family: var(--aller_lt);
                font-family: "Aller", sans-serif;

                @media #{$media-820} {
                    position: relative;

                    &::after {
                        position: absolute;
                        content: "";
                        // bottom: 0;
                        bottom: -35px;
                        left: 50%;
                        transform: translateX(-50%);
                        background-color: #453299;
                        // width: 40%;
                        width: 100%;
                        // height: 1px;
                        height: 2px;

                        @media #{$media-768} {
                            bottom: -25px;
                        }

                        @media #{$media-700} {
                            bottom: -15px;
                        }
                    }

                    &::before {
                        position: absolute;
                        content: "";
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background-color: #3700ff0e;

                        width: 100%;
                        pointer-events: none;
                        height: 300%;
                    }
                }
            }

            @media #{$media-820} {
                display: inline-flex;
                padding: 10px;
                flex-wrap: nowrap;
                text-wrap: nowrap;
                // border: solid 1px #ccc;
                border-radius: 0px;
            }

            &:hover {
                color: #6b5cac;
            }

            @media #{$media-700} {
                font-size: 14px;
                padding: 5px 10px;
            }
        }

        @media #{$media-768} {
            display: inline-flex;
            flex-wrap: nowrap;
            height: auto;
            margin-bottom: 5px !important;
        }
    }

    @media #{$media-820} {
        display: inline-flex;
    }
}

.padding_bot_mob {
    @media #{$media-700} {
        padding-bottom: 10px !important;
    }
}

.mission_vission_padding {
    @media #{$media-700} {
        padding-bottom: 15px;
    }
}

.hover_block {
    transition: all 0.4s ease-out 0s;
    -moz-transition: all 0.4s ease-out 0s;
    -webkit-transition: all 0.4s ease-out 0s;
    -o-transition: all 0.4s ease-out 0s;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: #000000;
    z-index: 800;
    padding: 10%;
    visibility: hidden;
    opacity: 0;
    overflow: auto;
    display: flex;
    align-items: center;
    justify-content: center;

    @media #{$media-1280} {
        align-items: flex-start;
        justify-content: flex-start;
    }

    @media #{$media-700} {
        padding: 10px 5%;
    }
}

.stock_block_logo {
    width: 100%;
    // max-width: 150px;
    max-width: 200px;
    margin: auto;

    >img {
        height: auto;
        width: 100%;
        display: block;
        object-fit: contain;
        margin: auto;
        position: unset !important;
    }

    @media #{$media-1366} {
        max-width: unset;
        width: 90%;
    }

    @media #{$media-700} {
        max-width: 160px;
        width: 90%;
    }
}

.hover_block_logo {
    width: 100%;
    max-width: 110px;

    >img {
        height: auto;
        width: 100%;
        display: block;
        object-fit: contain;
    }

    @media #{$media-1366} {
        max-width: unset;
        width: 60%;
        margin-bottom: 10px;
    }

    @media #{$media-700} {
        max-width: 120px;
    }
}

.hover_block_txt {
    width: 100%;
    // margin-top: 10px;

    @media #{$media-1366} {
        margin-top: 5px;
    }

    &.hover_block_white_sec {

        p,
        a {
            color: #6758b6 !important;
        }
    }

    p {
        color: #fff !important;
        font-family: 15px;
        line-height: 24px;
        margin-bottom: 10px;
    }

    a {
        font-size: 17px !important;

        @media #{$media-1366} {
            font-size: 15px !important;
        }
    }
}

.subsidiaries_li {
    @media #{$media-1366} {
        aspect-ratio: 1/1;
    }

    @media #{$media-700} {
        aspect-ratio: unset;
    }

    p {
        // line-height: 120% !important;

        @media #{$media-1366} {
            line-height: 130% !important;
        }
    }

    &:hover {
        .hover_block {
            visibility: visible;
            opacity: 1;
            // @media #{$media-600} {
            //     display: none !important;
            // }
        }
    }
}

.subsidiaries_head {
    a {
        font-size: 17px;

        @media #{$media-700} {
            font-size: 15px;
        }
    }
}

.airplane_moving_section {
    position: relative;
    overflow: hidden;
}

.quote_icon {
    height: auto;
    width: 30px;

    >img {
        height: 100%;
        width: 100%;
        display: block;

        object-fit: cover;
    }
}