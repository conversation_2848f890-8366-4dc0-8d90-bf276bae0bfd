import React, { useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/effect-coverflow";
import "swiper/css/pagination";
import {
  Autoplay,
  EffectCoverflow,
  Navigation,
  Pagination,
} from "swiper/modules";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";
import project from "@/styles/project.module.scss";
import comon from "@/styles/comon.module.scss";
import Link from "next/link";
import parse from 'html-react-parser';
const SliderNew = ({ sliderData, className }) => {
  useEffect(() => {
    AOS.init();
  }, []);
  const [activeIndex, setActiveIndex] = useState(0);

  console.log("className", className);

  const SliderHead = [
    // "Product1",
    // "Product2",
    // "Product3",
    // "Product4",
    // "Product5",
    // "Product6",
    // "Product7",
  ];

  const [HeadName, setHeadname] = useState(sliderData[0]['slider_descriptions'].title);
  //console.log(HeadName);
  const sliderCount = (swiper) => {
    const activeIndex = swiper.activeIndex; // Get activeIndex from Swiper instance
    setHeadname(sliderData[activeIndex]['slider_descriptions'].title);  // Update HeadName using state
  };

  const galleryItems = sliderData;
  let extendedGallery = [...galleryItems];
  while (extendedGallery.length < 7) {
      extendedGallery = [...extendedGallery, ...galleryItems];
  }
  extendedGallery = extendedGallery.slice(0, 7);
  return (
    <>
      <div
        className="slidernew maalem  "
        data-aos="fade-in"
        data-aos-duration="1000"
      >
        {/* <Swiper
				centeredSlides={true}
				slidesPerView={1}
				onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)} // `realIndex` is more accurate for looped swipers

				// onSlideChange={sliderCount}
				loop={true}
				// autoplay={{ delay: 3000 }}
				spaceBetween={20}
				coverflowEffect={{
					rotate: 30,
					stretch: 0,
					depth: 200,
					modifier: 1,
					slideShadows: true,
				}}
				navigation={{
					nextEl: ".swiper-button.next",
					prevEl: ".swiper-button.prev",
				}}
				pagination={{ clickable: true }}
				className={`projectslider_new maalem_swiper ${className == "maleem" ? "maleem_border" : ""
					} `}
				modules={[Navigation, Autoplay, EffectCoverflow]}
				breakpoints={{
					769: {
						slidesPerView: 5,

					},
					1440: {
						slidesPerView: 5,

						spaceBetween: 30

					},
				}}
			>
				{ImageSrc.map((data, index) => (
					<SwiperSlide key={index}>
						<div className={`project_slide_new_img  `}>
							<Image src={data} alt={`slider-${index + 1}`} width={500} height={500} />
						</div>
					</SwiperSlide>
				))}

		 






			</Swiper> */}

        <Swiper
          centeredSlides={true}
          slidesPerView={1}
          onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)} // `realIndex` is more accurate for looped swipers
          loop={true}
          spaceBetween={20}
          coverflowEffect={{
            rotate: 30,
            stretch: 0,
            depth: 200,
            modifier: 1,
            slideShadows: true,
          }}
          navigation={{
            nextEl: ".swiper-button.next",
            prevEl: ".swiper-button.prev",
          }}
          pagination={{ clickable: true }}
          //   ${className == "maleem" ? "maleem_border" : ""}
          className={`projectslider_new maalem_swiper    `}
          modules={[Navigation, Autoplay, EffectCoverflow]}
          breakpoints={{
            600: {
              slidesPerView: 3,
              spaceBetween: 0,
            },
            821: {
              slidesPerView: 5,
              spaceBetween: 50,
            },
            1440: {
              spaceBetween: 50,
              slidesPerView: 5,
            },
          }}
        >
          {extendedGallery.map((data, index) => (
            <SwiperSlide key={index}>
              <div className={`slider_anim_new_img `}>
                <Image
                  src={data.slider_image}
                  alt={`slider-${index + 1}`}
                  width={500}
                  height={500}
                  
                />
              </div>
            </SwiperSlide>
          ))}
        </Swiper>

        <div
          className={`project_slider_new_btn maleem_btn  project_slider_btn  ${
            className == "maleem" ? "maleem_btn" : ""
          }`}
        >
          <button className="swiper-button prev">❮</button>
          <button className="swiper-button next">❯</button>
        </div>
        {extendedGallery[activeIndex]['slider_descriptions'].title !== 0 ? (
          <div className="slider_heading">
            <h4>{HeadName}</h4>
          </div>
        ) : (
          ""
        )}
      </div>
      <div
        className={`${project.project_second_section} ${comon.d_flex_wrap} ${comon.pt_70}`}
      >
        <div
          class={`${project.w_45} ${comon.mb_20} `}
          data-aos="fade-in"
          data-aos-duration="1000"
        >
          <div class={`${project.project_logo} ${project.project_logo_center} ${comon.mb_30}`}>
            {/* <Image
							src="/images/vol.png"
							alt="slider"
							width={94}
							height={42}
						/> */}
           {extendedGallery[activeIndex].slider_descriptions.logo_title_d &&
           extendedGallery[activeIndex]?.slider_descriptions?.logo ? (
                            <Image
                              src={`${extendedGallery[activeIndex].slider_descriptions.logo}`}
                              // src="/images/mezah.png"
                              alt="slider"
                              width={304}
                              className="max_w_img"
                              height={44}
                              style={{objectFit:'contain'}}
                            />
                          ):(
              <h2 className={`${comon.text_center} ${comon.text_capitalize}`}
              style={{color:'white'}}
              >
                {extendedGallery[activeIndex].slider_descriptions.title && extendedGallery[activeIndex].slider_descriptions.title}</h2>
            )}
          </div>

          {extendedGallery[activeIndex].slider_descriptions.button && (
          <Link
            href="#"
            className={`${comon.buttion} ${comon.display_hidden} ${comon.but_blue} ${comon.but_h_02}  ${comon.but_white}`}
          >
            Check Locations
          </Link>
          )}
        </div>

        <div
          class={`${project.w_55} ${project.project_description} ${project.project_description_maalem}`}
          data-aos="fade-in"
          data-aos-duration="1000"
        >
          <>
            {extendedGallery[activeIndex].slider_descriptions.description && parse(extendedGallery[activeIndex].slider_descriptions.description)}
          </>
        </div>
      </div>
    </>
  );
};

export default SliderNew;