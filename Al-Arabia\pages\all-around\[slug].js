import React, { useState, useEffect } from "react";
import Head from "next/head";
import Link from "next/link";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";
import rtl from "@/styles/rtl.module.scss";
import InnerBanner from "@/component/InnerBanner";
import Map from "@/component/contactmap";
import HeadMarquee from "@/component/HeadMarquee";
import SliderSection from "@/component/SliderSection";
import ContactSection from "@/component/ContactSection";
import { fetchByPost } from "@/lib/api/pageApi";
import comon from "@/styles/comon.module.scss";
import StartCampaign from "@/component/StartCampaign";
import parse from "html-react-parser";
import { useRouter } from "next/router";


export default function Home({ pageData }) {
  const { locale } = useRouter();
  const [isMobile, setIsMobile] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [rearrangedData, setRearrangedData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 1024);
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    setIsClient(true);
    AOS.init();
  }, []);

  useEffect(() => {
    const fetchRearrangedData = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/${
            locale === "ar" ? "map-locations-ar" : "map-locations"
          }`
        );
        const json = await response.json(); 

        if (json.success && Array.isArray(json.locations)) {
          const pageTitle = pageData?.title?.rendered?.trim().toLowerCase();
          const filtered = json.locations
            .map((location) => {
              const filteredCities = location.cities
                .map((city) => {
                  const filteredCoords = city.co_ords.filter(
                    (coord) => coord.name?.trim().toLowerCase() === pageTitle
                  );
                  return {
                    ...city,
                    co_ords: filteredCoords,
                  };
                })
                .filter((city) => city.co_ords.length > 0);
              return {
                ...location,
                cities: filteredCities,
              };
            })
            .filter((location) => location.cities.length > 0);
          setRearrangedData(filtered);
        } else {
          setError("Map data is missing or invalid.");
        }
      } catch (err) {
        setError("Failed to load map data.");
      } finally {
        setLoading(false);
      }
    };
    fetchRearrangedData();
  }, [locale]);

  if (!isClient) return null;

  const banner = pageData.acf.banner_details;

  return (
    <>
      <HeadMarquee />
      <InnerBanner
        showLink={false}
        title={banner.title}
        details={banner}
        extraClass="padding_bottom_banner"
      />
      <section className={comon.pt_50}>
        <div className={comon.wrap}>
          <div className={`${comon.bread_crumb_mian} ${comon.pb_0}`}>
            <ul className={`${comon.mb_30} ${comon.detail_breadcrumb}   `}>
              <li>
                <Link href={"/"}>
                  <div className={` ${comon.bread_icon}  `}>
                    <Image
                      src={"/images/breadcumb_home.svg"}
                      height={30}
                      width={30}
                      alt=""
                    />
                  </div>
                </Link>
              </li>
              <li>
                <Link href={"/all-around"}>
                  {locale == "ar" ? "حول المملكة" : "All Around"}
                </Link>
              </li>
              <li>
                <a className={` ${comon.active}`}>{pageData.title.rendered}</a>
              </li>
            </ul>
          </div>
          <div
            className={`${comon.max_w_detail} ${comon.around_detail_page_sec} ${comon.mt_30} ${comon.text_center}`}
          >
            <>{parse(pageData.acf.details.description)}</>
            <ul
              className={`${comon.detail_ul} ${comon.all_around_detail_icon_sec} ${comon.justify_center} ${comon.mt_30}`}
            >
              {pageData?.acf?.details?.info &&
              Array.isArray(pageData.acf.details.info)
                ? pageData.acf.details.info.map((item, index) => (
                    <li key={index}>
                      <span className={comon.detail_ul_icn}>
                        <Image
                          src={item.icon}
                          width={29}
                          height={29}
                          quality={100}
                          alt={item.title || "Icon"}
                        />
                      </span>
                      <h3>
                        {item.title}
                        <span className="base_font">
                          {item.text && parse(item.text)}
                        </span>
                      </h3>
                    </li>
                  ))
                : null}
            </ul>
          </div>
        </div>
      </section>
      <section className={comon.slider_section_outer}>
        {pageData.acf.gallery_images && (
          <SliderSection
            images={pageData.acf.gallery_images.map((item) => ({
              img: item,
              description: "",
            }))}
            title=""
            padding_top0={true}
          />
        )}
      </section>
      {rearrangedData === undefined ? (
        <p>Loading map data...</p>
      ) : rearrangedData && pageData ? (
        <Map
          mapDetails={pageData.acf.location_details}
          textBlockShow={false}
          locationList={rearrangedData}
        />
      ) : null}
      <StartCampaign />
      <section className={comon.pt_60}>
        <ContactSection
          title="Request for Quotation"
          paragraph="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore."
          button="Request a Quote"
          showAutoMargin={false}
        />
      </section>
    </>
  );
}

export async function getServerSideProps(context) {
  const { params, locale } = context;
  const slug = params.slug;
  const langCode = locale === "ar" ? "ar" : "en";

  try {
    const pageData = await fetchByPost("all-around", slug, langCode);
    return {
      props: { pageData },
    };
  } catch (error) {
    console.error("Error fetching data:", error);
    return {
      props: { pageData: null },
    };
  }
}
