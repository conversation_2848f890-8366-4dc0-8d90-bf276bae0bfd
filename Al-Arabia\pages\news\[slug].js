"use client"; // Ensures the component is client-side rendered

import React, { useEffect, useState } from "react";
import comon from "@/styles/comon.module.scss";
import style from "@/styles/news.module.scss";
import Link from "next/link";
import Image from "next/image";
import rtl from "@/styles/rtl.module.scss";
import MoreNews from "@/component/MoreNews";
import HeadMarquee from "@/component/HeadMarquee";
import { fetchByPost } from "@/lib/api/pageApi";
import parse from 'html-react-parser';
import { useRouter } from "next/router";
const newsdetail = ({ pageData, relatedData }) => {


	const router = useRouter();
	const { locale } = useRouter();
	const [ip, setIp] = React.useState('');
	//console.log(router)
	const { asPath } = router;
	const pathSegments = asPath.split('/');
	const itemBeforeSlug = pathSegments[pathSegments.length - 2];

	// const fullUrl = `${process.env.NEXT_PUBLIC_BASE_URL}${router.asPath}`;
	const [fullUrl, setFullUrl] = useState('');
	useEffect(() => {
		const currentUrl = `${window.location.origin}/${pageData.slug}`;
		setFullUrl(currentUrl);
	}, [pageData.slug]);

	// Get the user's IP address
	React.useEffect(() => {
		fetch('/api/get-ip')
			.then(res => res.json())
			.then(data => {
				setIp(data.ip);
				console.log('User IP:', data.ip);
			})
			.catch(error => {
				console.error('Error fetching IP:', error);
			});
	}, []);

	React.useEffect(() => {
		const updateViewCount = async () => {
			if (pageData?.id && ip) {
				await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/increment-view-count/${pageData.id}`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({ ip }),
				});
			}
		};

		updateViewCount();
	}, [pageData, ip]);

	React.useEffect(() => {
		window.scrollTo(0, 0);
	}, []);

	const monthNames = {
		en: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
		ar: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
	};

	const fixedDate = new Date(pageData.date);
	const day = fixedDate.getDate();
	const year = fixedDate.getFullYear();
	const month = monthNames[locale === 'ar' ? 'ar' : 'en'][fixedDate.getMonth()];

	// const formattedFixedDate = `${day} ${month} ${year}`;
	const formattedFixedDate = (
		<div className="news_date_section">
			<span className="base_font ">{day}</span> {month} <span className="base_font">{year}</span>
		</div>
	);
	return (
		<>
			<HeadMarquee />
			<section className={`${comon.pt_50} ${style.detail_section} `}>
				<div
					className={`${comon.wrap} ${comon.mb_65}  ${style.detail_container}  `}
				>
					<ul className={`${comon.mb_30} ${comon.detail_breadcrumb} ${rtl.detail_breadcrumb}`}>
						<li>
							<Link href={"/"}>
								<div className={` ${comon.bread_icon}  `}>
									<Image
										src={"/images/breadcumb_home.svg"}
										height={30}
										width={30}
										alt=""
									/>
								</div>
							</Link>
						</li>
						<li>
							<Link href={"/news"}>{locale == 'ar' ? 'أخبار' : 'News'}</Link>
						</li>
						<li>
						<Link href="" className={` ${comon.active}`}>
							{(() => {
								const rawTitle = pageData.title.rendered;
								//const plainText = stripTags(rawTitle);
								const words = rawTitle.split(" ");
								const trimmedText = words.length > 5 ? words.slice(0, 5).join(" ") + "..." : rawTitle;
								return parse(trimmedText);
							})()}
						</Link>
						</li>


					</ul>

					<div className={`${style.share_section}  `}>
						<div className={`${style.btn_section}  `}>
							{pageData?.news_tags_info?.map((item, index) => {
								return (
									<Link href={"#"} key={index}>{item.name}</Link>
								)
							})}
						</div>
						<ul className={`${style.share_icon_sec}  `}>
							<li>
								<span>{locale == "ar" ? 'مشاركة :' : 'Share :'}</span>
							</li>
							<li>
								<a href={`https://wa.me/?text=share%20${fullUrl}`} className={`${style.icon}  `} target="_blank">
									<Image
										src={"/images/whatsapp_icon.svg"}
										height={30}
										width={30}
										alt=""
									/>
								</a>
							</li>
							<li>
								<a href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(fullUrl)}`} className={`${style.icon}  `}
									target="_blank" >
									<Image
										src={"/images/x_icon.svg"}
										height={30}
										width={30}
										alt=""
									/>
								</a>
							</li>

							{/* <li>
								<a href={"#"} className={`${style.icon}  `}>
									<Image
										src={"/images/instagram_icon.svg"}
										height={30}
										width={30}
										alt=""
									/>
								</a>
							</li> */}
							{/* <li>
								<a href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(fullUrl)}`}
									className={`${style.icon}  `} target="_blank">
									<Image
										src={"/images/facebook_icon.svg"}
										height={30}
										width={30}
										alt=""
									/>
								</a>
							</li> */}
							<li>
								<a
									href={`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(fullUrl)}`}
									className={`${style.icon}`}
									target="_blank"
									rel="noopener noreferrer"
								>
									<Image
									src={"/images/linked-in1.svg"}
									height={50}
									width={50}
									alt="LinkedIn"
									/>
								</a>
							</li>

						</ul>
					</div>
					<div className={`${style.content_section}  `}>
						<h2>
							{parse(pageData.title.rendered)}
						</h2>
						<>
							{parse(pageData.content.rendered)}

						</>
						<ul className={`${style.like_section}  ${rtl.like_section} ${rtl.news_like_section}  `}>
							<li>
								<span>
									{formattedFixedDate}
								</span>

							</li>
							<li>
								<div className={`${style.icon_img}  `}>
									<Image
										src={"/images/eye-icon.svg"}
										height={15}
										width={15}
										alt=""
									/>
								</div>
								<span> <span className="base_font">{pageData.view_count}</span> {locale === "ar" ? "المشاهدات" : "Views"}
								</span>
							</li>
							{/* <li>
								{" "}
								<div className={`${style.icon_img} ${style.max_width}  `}>
									<Image
										src={"/images/like_icon.svg"}
										height={15}
										width={15}
										alt=""
									/>
								</div>
								<span>5</span>
							</li> */}
						</ul>
					</div>
				</div>
			</section>
			<MoreNews related={relatedData} />
		</>
	);
};

export default newsdetail;
export async function getServerSideProps(context) {

	const { params, locale } = context;
	const slug = params.slug;
	const langCode = locale === "ar" ? "ar" : "en";

	try {
		const pageData = await fetchByPost('news', slug, langCode);

		const categoryId = pageData?.['news-category']?.[0];
		const relatedResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/news?acf_format=standard&per_page=10&news-category=${categoryId}&exclude=${pageData.id}&lang=${langCode}&_embed`);
		const relatedData = await relatedResponse.json();
		if (!pageData || Object.keys(pageData).length === 0) {
			return {
				notFound: true,
			};
		}
		return {
			props: {
				pageData,
				relatedData
			},
		};
	} catch (error) {
		console.error('Error fetching data:', error);
		return {
			notFound: true,
		};
	}
}