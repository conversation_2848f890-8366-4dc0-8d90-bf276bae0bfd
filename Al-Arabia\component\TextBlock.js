import React from "react";
import comon from "@/styles/comon.module.scss";
import Image from "next/image";
import parse from 'html-react-parser';
const TextBlock = ({ content, Page = "" }) => {
    console.log("content", content);
    console.log("Page", Page);

    return (
        <div className={`${comon.text_block_section} `}>
            <ul
                className={` ${comon.text_block_field} ${Page == "kkia_page" ? comon.kkia_page : ""
                    }  `}
            >
            
                    <li>
                        <div className={`${comon.backgroun_img}`}>
                            <Image
                                // src="/images/textblock-img2.png"
                                src={content.box_left.image}
                                width={1000} 
                                height={1000}
                                alt="button image"
                                quality={100}
                            />
                        </div>
                        {Page == "kkia_page" ? (
                            <div
                                className={`${comon.img_icon}`}
                                
                            >
                                <Image
                                    src={content.box_left.icon}
                                    width={150}
                                    height={150}
                                    alt="button image"
                                    quality={100}
                                />
                            </div>
                        ) : (
                            ""
                        )}
                        <h4
                            // data-aos="fade-in"
                            // data-aos-duration="1000"
                            // data-aos-delay="200"
                        >
                            {parse(content.box_left.title)}
                        </h4>

                        {Page != "kkia_page" ? (
                            <div
                               
                                style={{zIndex:'1'}}
                            >
                                {parse(content.box_left.description)}
                            </div>
                        ) : (
                            ""
                        )}
                    </li>

                    <li>
                        <div className={`${comon.backgroun_img}`}>
                            <Image
                                // src="/images/textblock-img2.png"
                                src={content.box_right.image}
                                width={1000}
                                height={1000}
                                alt="button image"
                                quality={100}
                            />
                        </div>
                        {Page == "kkia_page" ? (
                            <div
                                className={`${comon.img_icon}`}
                       
                                style={{zIndex:'1'}}
                            >
                                <Image
                                    src={content.box_right.icon}
                                    width={150}
                                    height={150}
                                    alt="button image"
                                    quality={100}
                                />
                            </div>
                        ) : (
                            ""
                        )}
                        <h4
                    
                        >
                            {parse(content.box_right.title)}
                        </h4>

                        {Page != "kkia_page" ? (
                            <div
                             
                                style={{zIndex:'1'}}
                            >
                                {parse(content.box_right.description)}
                            </div>
                        ) : (
                            ""
                        )}
                    </li>
              
                {/* <li>
                    <div className={`${comon.backgroun_img}`}>
                        <Image
                            src="/images/textblock-img2.png"
                            width={1000}
                            height={1000}
                            alt="button image"
                        />
                    </div>
                    <h4>Strategic Placement for Maximum Visibility</h4>
                    <p>
                        Our DOOH screens are strategically located in high-traffic areas and
                        thriving communities, ensuring brands achieve maximum reach and
                        visibility. Whether along major roads or in bustling neighborhoods,
                        we connect advertisers with consumers as they go about their daily
                        lives.
                    </p>
                </li>
                <li>
                    <div className={`${comon.backgroun_img}`}>
                        <Image
                            src="/images/textblock-img1.png"
                            width={1000}
                            height={1000}
                            alt="button image"
                        />
                    </div>
                    <h4>Data-Driven Insights for Optimal Impact</h4>
                    <p>
                        Al Arabia’s location selection is powered by in-depth data
                        analytics, focusing on impressions, reach, and frequency metrics.
                        This ensures that campaigns are not only visible but also optimized
                        for maximum ROI.
                    </p>
                </li> */}
            </ul>
        </div>
    );
};

export default TextBlock;
