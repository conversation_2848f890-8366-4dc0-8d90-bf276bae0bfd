"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_locomotive-scroll_dist_locomotive-scroll_modern_mjs"],{

/***/ "./node_modules/locomotive-scroll/dist/locomotive-scroll.modern.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/locomotive-scroll/dist/locomotive-scroll.modern.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ c; }\n/* harmony export */ });\n/* harmony import */ var lenis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lenis */ \"./node_modules/locomotive-scroll/node_modules/lenis/dist/lenis.mjs\");\nfunction s(){return s=Object.assign?Object.assign.bind():function(t){for(var s=1;s<arguments.length;s++){var e=arguments[s];for(var i in e)({}).hasOwnProperty.call(e,i)&&(t[i]=e[i])}return t},s.apply(null,arguments)}class e{constructor({scrollElements:t,rootMargin:s=\"-1px -1px -1px -1px\",IORaf:e}){this.scrollElements=void 0,this.rootMargin=void 0,this.IORaf=void 0,this.observer=void 0,this.scrollElements=t,this.rootMargin=s,this.IORaf=e,this._init()}_init(){this.observer=new IntersectionObserver(t=>{t.forEach(t=>{const s=this.scrollElements.find(s=>s.$el===t.target);t.isIntersecting?(s&&(s.isAlreadyIntersected=!0),this._setInview(t)):s&&s.isAlreadyIntersected&&this._setOutOfView(t)})},{rootMargin:this.rootMargin});for(const t of this.scrollElements)this.observe(t.$el)}destroy(){this.observer.disconnect()}observe(t){t&&this.observer.observe(t)}unobserve(t){t&&this.observer.unobserve(t)}_setInview(t){const s=this.scrollElements.find(s=>s.$el===t.target);this.IORaf&&(null==s||s.setInteractivityOn()),!this.IORaf&&(null==s||s.setInview())}_setOutOfView(t){const s=this.scrollElements.find(s=>s.$el===t.target);this.IORaf&&(null==s||s.setInteractivityOff()),!this.IORaf&&(null==s||s.setOutOfView()),null!=s&&s.attributes.scrollRepeat||this.IORaf||this.unobserve(t.target)}}function i(t,s,e,i,r){return e+((r-t)/(s-t)*(i-e)||0)}function r(t,s){return t.reduce((t,e)=>Math.abs(e-s)<Math.abs(t-s)?e:t)}class l{constructor({$el:t,id:s,modularInstance:e,subscribeElementUpdateFn:i,unsubscribeElementUpdateFn:r,needRaf:l,scrollOrientation:n}){var o,a,c,h,d;this.$el=void 0,this.id=void 0,this.needRaf=void 0,this.attributes=void 0,this.scrollOrientation=void 0,this.isAlreadyIntersected=void 0,this.intersection=void 0,this.metrics=void 0,this.currentScroll=void 0,this.translateValue=void 0,this.progress=void 0,this.lastProgress=void 0,this.modularInstance=void 0,this.progressModularModules=void 0,this.isInview=void 0,this.isInteractive=void 0,this.isInFold=void 0,this.isFirstResize=void 0,this.subscribeElementUpdateFn=void 0,this.unsubscribeElementUpdateFn=void 0,this.$el=t,this.id=s,this.needRaf=l,this.scrollOrientation=n,this.modularInstance=e,this.subscribeElementUpdateFn=i,this.unsubscribeElementUpdateFn=r,this.attributes={scrollClass:null!=(o=this.$el.dataset.scrollClass)?o:\"is-inview\",scrollOffset:null!=(a=this.$el.dataset.scrollOffset)?a:\"0,0\",scrollPosition:null!=(c=this.$el.dataset.scrollPosition)?c:\"start,end\",scrollModuleProgress:null!=this.$el.dataset.scrollModuleProgress,scrollCssProgress:null!=this.$el.dataset.scrollCssProgress,scrollEventProgress:null!=(h=this.$el.dataset.scrollEventProgress)?h:null,scrollSpeed:null!=this.$el.dataset.scrollSpeed?parseFloat(this.$el.dataset.scrollSpeed):null,scrollRepeat:null!=this.$el.dataset.scrollRepeat,scrollCall:null!=(d=this.$el.dataset.scrollCall)?d:null,scrollCallSelf:null!=this.$el.dataset.scrollCallSelf,scrollIgnoreFold:null!=this.$el.dataset.scrollIgnoreFold,scrollEnableTouchSpeed:null!=this.$el.dataset.scrollEnableTouchSpeed},this.intersection={start:0,end:0},this.metrics={offsetStart:0,offsetEnd:0,bcr:{}},this.currentScroll=\"vertical\"===this.scrollOrientation?window.scrollY:window.scrollX,this.translateValue=0,this.progress=0,this.lastProgress=null,this.progressModularModules=[],this.isInview=!1,this.isInteractive=!1,this.isAlreadyIntersected=!1,this.isInFold=!1,this.isFirstResize=!0,this._init()}_init(){this.needRaf&&(this.modularInstance&&this.attributes.scrollModuleProgress&&this._getProgressModularModules(),this._resize())}onResize({currentScroll:t}){this.currentScroll=t,this._resize()}onRender({currentScroll:t,smooth:s}){const e=\"vertical\"===this.scrollOrientation?window.innerHeight:window.innerWidth;if(this.currentScroll=t,this._computeProgress(),this.attributes.scrollSpeed&&!isNaN(this.attributes.scrollSpeed))if(this.attributes.scrollEnableTouchSpeed||s){if(this.isInFold){const t=Math.max(0,this.progress);this.translateValue=t*e*this.attributes.scrollSpeed*-1}else{const t=i(0,1,-1,1,this.progress);this.translateValue=t*e*this.attributes.scrollSpeed*-1}this.$el.style.transform=\"vertical\"===this.scrollOrientation?`translate3d(0, ${this.translateValue}px, 0)`:`translate3d(${this.translateValue}px, 0, 0)`}else this.translateValue&&(this.$el.style.transform=\"translate3d(0, 0, 0)\"),this.translateValue=0}setInview(){if(this.isInview)return;this.isInview=!0,this.$el.classList.add(this.attributes.scrollClass);const t=this._getScrollCallFrom();this.attributes.scrollCall&&this._dispatchCall(\"enter\",t)}setOutOfView(){if(!this.isInview||!this.attributes.scrollRepeat)return;this.isInview=!1,this.$el.classList.remove(this.attributes.scrollClass);const t=this._getScrollCallFrom();this.attributes.scrollCall&&this._dispatchCall(\"leave\",t)}setInteractivityOn(){this.isInteractive||(this.isInteractive=!0,this.subscribeElementUpdateFn(this))}setInteractivityOff(){this.isInteractive&&(this.isInteractive=!1,this.unsubscribeElementUpdateFn(this),null!=this.lastProgress&&this._computeProgress(r([0,1],this.lastProgress)))}_resize(){this.metrics.bcr=this.$el.getBoundingClientRect(),this._computeMetrics(),this._computeIntersection(),this.isFirstResize&&(this.isFirstResize=!1,this.isInFold&&this.setInview())}_computeMetrics(){const{top:t,left:s,height:e,width:i}=this.metrics.bcr,r=\"vertical\"===this.scrollOrientation?window.innerHeight:window.innerWidth,l=\"vertical\"===this.scrollOrientation?e:i;this.metrics.offsetStart=this.currentScroll+(\"vertical\"===this.scrollOrientation?t:s)-this.translateValue,this.metrics.offsetEnd=this.metrics.offsetStart+l,this.isInFold=this.metrics.offsetStart<r&&!this.attributes.scrollIgnoreFold}_computeIntersection(){const t=\"vertical\"===this.scrollOrientation?window.innerHeight:window.innerWidth,s=\"vertical\"===this.scrollOrientation?this.metrics.bcr.height:this.metrics.bcr.width,e=this.attributes.scrollOffset.split(\",\"),i=null!=e[0]?e[0].trim():\"0\",r=null!=e[1]?e[1].trim():\"0\",l=this.attributes.scrollPosition.split(\",\");let n=null!=l[0]?l[0].trim():\"start\";const o=null!=l[1]?l[1].trim():\"end\",a=i.includes(\"%\")?t*parseInt(i.replace(\"%\",\"\").trim())*.01:parseInt(i),c=r.includes(\"%\")?t*parseInt(r.replace(\"%\",\"\").trim())*.01:parseInt(r);switch(this.isInFold&&(n=\"fold\"),n){case\"start\":default:this.intersection.start=this.metrics.offsetStart-t+a;break;case\"middle\":this.intersection.start=this.metrics.offsetStart-t+a+.5*s;break;case\"end\":this.intersection.start=this.metrics.offsetStart-t+a+s;break;case\"fold\":this.intersection.start=0}switch(o){case\"start\":this.intersection.end=this.metrics.offsetStart-c;break;case\"middle\":this.intersection.end=this.metrics.offsetStart-c+.5*s;break;default:this.intersection.end=this.metrics.offsetStart-c+s}if(this.intersection.end<=this.intersection.start)switch(o){case\"start\":default:this.intersection.end=this.intersection.start+1;break;case\"middle\":this.intersection.end=this.intersection.start+.5*s;break;case\"end\":this.intersection.end=this.intersection.start+s}}_computeProgress(t){const s=null!=t?t:(e=i(this.intersection.start,this.intersection.end,0,1,this.currentScroll))<0?0:e>1?1:e;var e;if(this.progress=s,s!=this.lastProgress){if(this.lastProgress=s,this.attributes.scrollCssProgress&&this._setCssProgress(s),this.attributes.scrollEventProgress&&this._setCustomEventProgress(s),this.attributes.scrollModuleProgress)for(const t of this.progressModularModules)this.modularInstance&&this.modularInstance.call(\"onScrollProgress\",s,t.moduleName,t.moduleId);s>0&&s<1&&this.setInview(),0===s&&this.setOutOfView(),1===s&&this.setOutOfView()}}_setCssProgress(t=0){this.$el.style.setProperty(\"--progress\",t.toString())}_setCustomEventProgress(t=0){const s=this.attributes.scrollEventProgress;if(!s)return;const e=new CustomEvent(s,{detail:{target:this.$el,progress:t}});window.dispatchEvent(e)}_getProgressModularModules(){if(!this.modularInstance)return;const t=Object.keys(this.$el.dataset).filter(t=>t.includes(\"module\")),s=Object.entries(this.modularInstance.modules);if(t.length)for(const e of t){const t=this.$el.dataset[e];if(!t)return;for(const e of s){const[s,i]=e;t in i&&this.progressModularModules.push({moduleName:s,moduleId:t})}}}_getScrollCallFrom(){const t=r([this.intersection.start,this.intersection.end],this.currentScroll);return this.intersection.start===t?\"start\":\"end\"}_dispatchCall(t,s){var e,i;const r=null==(e=this.attributes.scrollCall)?void 0:e.split(\",\"),l=null==(i=this.attributes)?void 0:i.scrollCallSelf;if(r&&r.length>1){var n;const[e,i,o]=r;let a;a=l?this.$el.dataset[`module${i.trim()}`]:o,this.modularInstance&&this.modularInstance.call(e.trim(),{target:this.$el,way:t,from:s},i.trim(),null==(n=a)?void 0:n.trim())}else if(r){const[e]=r,i=new CustomEvent(e,{detail:{target:this.$el,way:t,from:s}});window.dispatchEvent(i)}}}const n=[\"scrollOffset\",\"scrollPosition\",\"scrollModuleProgress\",\"scrollCssProgress\",\"scrollEventProgress\",\"scrollSpeed\"];class o{constructor({$el:t,modularInstance:s,triggerRootMargin:e,rafRootMargin:i,scrollOrientation:r}){this.$scrollContainer=void 0,this.modularInstance=void 0,this.triggerRootMargin=void 0,this.rafRootMargin=void 0,this.scrollElements=void 0,this.triggeredScrollElements=void 0,this.RAFScrollElements=void 0,this.scrollElementsToUpdate=void 0,this.IOTriggerInstance=void 0,this.IORafInstance=void 0,this.scrollOrientation=void 0,t?(this.$scrollContainer=t,this.modularInstance=s,this.scrollOrientation=r,this.triggerRootMargin=null!=e?e:\"-1px -1px -1px -1px\",this.rafRootMargin=null!=i?i:\"100% 100% 100% 100%\",this.scrollElements=[],this.triggeredScrollElements=[],this.RAFScrollElements=[],this.scrollElementsToUpdate=[],this._init()):console.error(\"Please provide a DOM Element as scrollContainer\")}_init(){const t=this.$scrollContainer.querySelectorAll(\"[data-scroll]\"),s=Array.from(t);this._subscribeScrollElements(s),this.IOTriggerInstance=new e({scrollElements:[...this.triggeredScrollElements],rootMargin:this.triggerRootMargin,IORaf:!1}),this.IORafInstance=new e({scrollElements:[...this.RAFScrollElements],rootMargin:this.rafRootMargin,IORaf:!0})}destroy(){this.IOTriggerInstance.destroy(),this.IORafInstance.destroy(),this._unsubscribeAllScrollElements()}onResize({currentScroll:t}){for(const s of this.RAFScrollElements)s.onResize({currentScroll:t})}onRender({currentScroll:t,smooth:s}){for(const e of this.scrollElementsToUpdate)e.onRender({currentScroll:t,smooth:s})}removeScrollElements(t){const s=t.querySelectorAll(\"[data-scroll]\");if(s.length){for(let t=0;t<this.triggeredScrollElements.length;t++){const e=this.triggeredScrollElements[t];Array.from(s).indexOf(e.$el)>-1&&(this.IOTriggerInstance.unobserve(e.$el),this.triggeredScrollElements.splice(t,1))}for(let t=0;t<this.RAFScrollElements.length;t++){const e=this.RAFScrollElements[t];Array.from(s).indexOf(e.$el)>-1&&(this.IORafInstance.unobserve(e.$el),this.RAFScrollElements.splice(t,1))}s.forEach(t=>{const s=this.scrollElementsToUpdate.find(s=>s.$el===t),e=this.scrollElements.find(s=>s.$el===t);s&&this._unsubscribeElementUpdate(s),e&&(this.scrollElements=this.scrollElements.filter(t=>t.id!=e.id))})}}addScrollElements(t){const s=t.querySelectorAll(\"[data-scroll]\"),e=[];this.scrollElements.forEach(t=>{e.push(t.id)});const i=Math.max(...e,0)+1,r=Array.from(s);this._subscribeScrollElements(r,i,!0)}_subscribeScrollElements(t,s=0,e=!1){for(let i=0;i<t.length;i++){const r=t[i],n=this._checkRafNeeded(r),o=new l({$el:r,id:s+i,scrollOrientation:this.scrollOrientation,modularInstance:this.modularInstance,subscribeElementUpdateFn:this._subscribeElementUpdate.bind(this),unsubscribeElementUpdateFn:this._unsubscribeElementUpdate.bind(this),needRaf:n});this.scrollElements.push(o),n?(this.RAFScrollElements.push(o),e&&(this.IORafInstance.scrollElements.push(o),this.IORafInstance.observe(o.$el))):(this.triggeredScrollElements.push(o),e&&(this.IOTriggerInstance.scrollElements.push(o),this.IOTriggerInstance.observe(o.$el)))}}_unsubscribeAllScrollElements(){this.scrollElements=[],this.RAFScrollElements=[],this.triggeredScrollElements=[],this.scrollElementsToUpdate=[]}_subscribeElementUpdate(t){this.scrollElementsToUpdate.push(t)}_unsubscribeElementUpdate(t){this.scrollElementsToUpdate=this.scrollElementsToUpdate.filter(s=>s.id!=t.id)}_checkRafNeeded(t){let s=[...n];const e=t=>{s=s.filter(s=>s!=t)};if(t.dataset.scrollOffset){if(\"0,0\"!=t.dataset.scrollOffset.split(\",\").map(t=>t.replace(\"%\",\"\").trim()).join(\",\"))return!0;e(\"scrollOffset\")}else e(\"scrollOffset\");if(t.dataset.scrollPosition){if(\"top,bottom\"!=t.dataset.scrollPosition.trim())return!0;e(\"scrollPosition\")}else e(\"scrollPosition\");if(t.dataset.scrollSpeed&&!isNaN(parseFloat(t.dataset.scrollSpeed)))return!0;e(\"scrollSpeed\");for(const e of s)if(e in t.dataset)return!0;return!1}}class a{constructor({resizeElements:t,resizeCallback:s=()=>{}}){this.$resizeElements=void 0,this.isFirstObserve=void 0,this.observer=void 0,this.resizeCallback=void 0,this.$resizeElements=t,this.resizeCallback=s,this.isFirstObserve=!0,this._init()}_init(){this.observer=new ResizeObserver(t=>{var s;!this.isFirstObserve&&(null==(s=this.resizeCallback)||s.call(this)),this.isFirstObserve=!1});for(const t of this.$resizeElements)this.observer.observe(t)}destroy(){this.observer.disconnect()}}class c{constructor({lenisOptions:t={},modularInstance:s,triggerRootMargin:e,rafRootMargin:i,autoResize:r=!0,autoStart:l=!0,scrollCallback:n=()=>{},initCustomTicker:o,destroyCustomTicker:a}={}){this.rafPlaying=void 0,this.lenisInstance=void 0,this.coreInstance=void 0,this.lenisOptions=void 0,this.modularInstance=void 0,this.triggerRootMargin=void 0,this.rafRootMargin=void 0,this.rafInstance=void 0,this.autoResize=void 0,this.autoStart=void 0,this.ROInstance=void 0,this.initCustomTicker=void 0,this.destroyCustomTicker=void 0,this._onRenderBind=void 0,this._onResizeBind=void 0,this._onScrollToBind=void 0;for(const[s]of Object.entries(t))[\"wrapper\",\"content\",\"infinite\"].includes(s)&&console.warn(`Warning: Key \"${s}\" is not possible to edit in Locomotive Scroll.`);Object.assign(this,{lenisOptions:t,modularInstance:s,triggerRootMargin:e,rafRootMargin:i,autoResize:r,autoStart:l,scrollCallback:n,initCustomTicker:o,destroyCustomTicker:a}),this._onRenderBind=this._onRender.bind(this),this._onScrollToBind=this._onScrollTo.bind(this),this._onResizeBind=this._onResize.bind(this),this.rafPlaying=!1,this._init()}_init(){var e;this.lenisInstance=new lenis__WEBPACK_IMPORTED_MODULE_0__[\"default\"](s({},this.lenisOptions,{wrapper:window,content:document.documentElement,infinite:!1})),null==(e=this.lenisInstance)||e.on(\"scroll\",this.scrollCallback),document.documentElement.setAttribute(\"data-scroll-orientation\",this.lenisInstance.options.orientation),requestAnimationFrame(()=>{this.coreInstance=new o({$el:this.lenisInstance.rootElement,modularInstance:this.modularInstance,triggerRootMargin:this.triggerRootMargin,rafRootMargin:this.rafRootMargin,scrollOrientation:this.lenisInstance.options.orientation}),this._bindEvents(),this.initCustomTicker&&!this.destroyCustomTicker?console.warn(\"initCustomTicker callback is declared, but destroyCustomTicker is not. Please pay attention. It could cause trouble.\"):!this.initCustomTicker&&this.destroyCustomTicker&&console.warn(\"destroyCustomTicker callback is declared, but initCustomTicker is not. Please pay attention. It could cause trouble.\"),this.autoStart&&this.start()})}destroy(){var t;this.stop(),this._unbindEvents(),this.lenisInstance.destroy(),null==(t=this.coreInstance)||t.destroy(),requestAnimationFrame(()=>{var t;null==(t=this.coreInstance)||t.destroy()})}_bindEvents(){this._bindScrollToEvents(),this.autoResize&&(\"ResizeObserver\"in window?this.ROInstance=new a({resizeElements:[document.body],resizeCallback:this._onResizeBind}):window.addEventListener(\"resize\",this._onResizeBind))}_unbindEvents(){this._unbindScrollToEvents(),this.autoResize&&(\"ResizeObserver\"in window?this.ROInstance&&this.ROInstance.destroy():window.removeEventListener(\"resize\",this._onResizeBind))}_bindScrollToEvents(t){const s=t||this.lenisInstance.rootElement,e=null==s?void 0:s.querySelectorAll(\"[data-scroll-to]\");(null==e?void 0:e.length)&&e.forEach(t=>{t.addEventListener(\"click\",this._onScrollToBind,!1)})}_unbindScrollToEvents(t){const s=t||this.lenisInstance.rootElement,e=null==s?void 0:s.querySelectorAll(\"[data-scroll-to]\");(null==e?void 0:e.length)&&e.forEach(t=>{t.removeEventListener(\"click\",this._onScrollToBind,!1)})}_onResize(){requestAnimationFrame(()=>{var t;null==(t=this.coreInstance)||t.onResize({currentScroll:this.lenisInstance.scroll})})}_onRender(){var t,s;null==(t=this.lenisInstance)||t.raf(Date.now()),null==(s=this.coreInstance)||s.onRender({currentScroll:this.lenisInstance.scroll,smooth:this.lenisInstance.options.smoothWheel})}_onScrollTo(t){var s;t.preventDefault();const e=null!=(s=t.currentTarget)?s:null;if(!e)return;const i=e.getAttribute(\"data-scroll-to-href\")||e.getAttribute(\"href\"),r=e.getAttribute(\"data-scroll-to-offset\")||0,l=e.getAttribute(\"data-scroll-to-duration\")||this.lenisInstance.options.duration;i&&this.scrollTo(i,{offset:\"string\"==typeof r?parseInt(r):r,duration:\"string\"==typeof l?parseInt(l):l})}start(){var t;this.rafPlaying||(null==(t=this.lenisInstance)||t.start(),this.rafPlaying=!0,this.initCustomTicker?this.initCustomTicker(this._onRenderBind):this._raf())}stop(){var t;this.rafPlaying&&(null==(t=this.lenisInstance)||t.stop(),this.rafPlaying=!1,this.destroyCustomTicker?this.destroyCustomTicker(this._onRenderBind):this.rafInstance&&cancelAnimationFrame(this.rafInstance))}removeScrollElements(t){var s;t?(this._unbindScrollToEvents(t),null==(s=this.coreInstance)||s.removeScrollElements(t)):console.error(\"Please provide a DOM Element as $oldContainer\")}addScrollElements(t){var s;t?(null==(s=this.coreInstance)||s.addScrollElements(t),requestAnimationFrame(()=>{this._bindScrollToEvents(t)})):console.error(\"Please provide a DOM Element as $newContainer\")}resize(){this._onResizeBind()}scrollTo(t,s){var e;null==(e=this.lenisInstance)||e.scrollTo(t,{offset:null==s?void 0:s.offset,lerp:null==s?void 0:s.lerp,duration:null==s?void 0:s.duration,immediate:null==s?void 0:s.immediate,lock:null==s?void 0:s.lock,force:null==s?void 0:s.force,easing:null==s?void 0:s.easing,onComplete:null==s?void 0:s.onComplete})}_raf(){this._onRenderBind(),this.rafInstance=requestAnimationFrame(()=>this._raf())}}\n//# sourceMappingURL=locomotive-scroll.modern.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/locomotive-scroll/dist/locomotive-scroll.modern.mjs\n"));

/***/ }),

/***/ "./node_modules/locomotive-scroll/node_modules/lenis/dist/lenis.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/locomotive-scroll/node_modules/lenis/dist/lenis.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Lenis; }\n/* harmony export */ });\nfunction clamp(t,i,e){return Math.max(t,Math.min(i,e))}class Animate{constructor(){this.isRunning=!1,this.value=0,this.from=0,this.to=0,this.duration=0,this.currentTime=0}advance(t){var i;if(!this.isRunning)return;let e=!1;if(this.duration&&this.easing){this.currentTime+=t;const i=clamp(0,this.currentTime/this.duration,1);e=i>=1;const s=e?1:this.easing(i);this.value=this.from+(this.to-this.from)*s}else this.lerp?(this.value=function damp(t,i,e,s){return function lerp(t,i,e){return(1-e)*t+e*i}(t,i,1-Math.exp(-e*s))}(this.value,this.to,60*this.lerp,t),Math.round(this.value)===this.to&&(this.value=this.to,e=!0)):(this.value=this.to,e=!0);e&&this.stop(),null===(i=this.onUpdate)||void 0===i||i.call(this,this.value,e)}stop(){this.isRunning=!1}fromTo(t,i,{lerp:e,duration:s,easing:o,onStart:n,onUpdate:l}){this.from=this.value=t,this.to=i,this.lerp=e,this.duration=s,this.easing=o,this.currentTime=0,this.isRunning=!0,null==n||n(),this.onUpdate=l}}class Dimensions{constructor({wrapper:t,content:i,autoResize:e=!0,debounce:s=250}={}){this.width=0,this.height=0,this.scrollWidth=0,this.scrollHeight=0,this.resize=()=>{this.onWrapperResize(),this.onContentResize()},this.onWrapperResize=()=>{this.wrapper===window?(this.width=window.innerWidth,this.height=window.innerHeight):this.wrapper instanceof HTMLElement&&(this.width=this.wrapper.clientWidth,this.height=this.wrapper.clientHeight)},this.onContentResize=()=>{this.wrapper===window?(this.scrollHeight=this.content.scrollHeight,this.scrollWidth=this.content.scrollWidth):this.wrapper instanceof HTMLElement&&(this.scrollHeight=this.wrapper.scrollHeight,this.scrollWidth=this.wrapper.scrollWidth)},this.wrapper=t,this.content=i,e&&(this.debouncedResize=function debounce(t,i){let e;return function(){let s=arguments,o=this;clearTimeout(e),e=setTimeout((function(){t.apply(o,s)}),i)}}(this.resize,s),this.wrapper===window?window.addEventListener(\"resize\",this.debouncedResize,!1):(this.wrapperResizeObserver=new ResizeObserver(this.debouncedResize),this.wrapperResizeObserver.observe(this.wrapper)),this.contentResizeObserver=new ResizeObserver(this.debouncedResize),this.contentResizeObserver.observe(this.content)),this.resize()}destroy(){var t,i;null===(t=this.wrapperResizeObserver)||void 0===t||t.disconnect(),null===(i=this.contentResizeObserver)||void 0===i||i.disconnect(),window.removeEventListener(\"resize\",this.debouncedResize,!1)}get limit(){return{x:this.scrollWidth-this.width,y:this.scrollHeight-this.height}}}class Emitter{constructor(){this.events={}}emit(t,...i){let e=this.events[t]||[];for(let t=0,s=e.length;t<s;t++)e[t](...i)}on(t,i){var e;return(null===(e=this.events[t])||void 0===e?void 0:e.push(i))||(this.events[t]=[i]),()=>{var e;this.events[t]=null===(e=this.events[t])||void 0===e?void 0:e.filter((t=>i!==t))}}off(t,i){var e;this.events[t]=null===(e=this.events[t])||void 0===e?void 0:e.filter((t=>i!==t))}destroy(){this.events={}}}const t=100/6;class VirtualScroll{constructor(i,{wheelMultiplier:e=1,touchMultiplier:s=1}){this.lastDelta={x:0,y:0},this.windowWidth=0,this.windowHeight=0,this.onTouchStart=t=>{const{clientX:i,clientY:e}=t.targetTouches?t.targetTouches[0]:t;this.touchStart.x=i,this.touchStart.y=e,this.lastDelta={x:0,y:0},this.emitter.emit(\"scroll\",{deltaX:0,deltaY:0,event:t})},this.onTouchMove=t=>{var i,e,s,o;const{clientX:n,clientY:l}=t.targetTouches?t.targetTouches[0]:t,r=-(n-(null!==(e=null===(i=this.touchStart)||void 0===i?void 0:i.x)&&void 0!==e?e:0))*this.touchMultiplier,h=-(l-(null!==(o=null===(s=this.touchStart)||void 0===s?void 0:s.y)&&void 0!==o?o:0))*this.touchMultiplier;this.touchStart.x=n,this.touchStart.y=l,this.lastDelta={x:r,y:h},this.emitter.emit(\"scroll\",{deltaX:r,deltaY:h,event:t})},this.onTouchEnd=t=>{this.emitter.emit(\"scroll\",{deltaX:this.lastDelta.x,deltaY:this.lastDelta.y,event:t})},this.onWheel=i=>{let{deltaX:e,deltaY:s,deltaMode:o}=i;e*=1===o?t:2===o?this.windowWidth:1,s*=1===o?t:2===o?this.windowHeight:1,e*=this.wheelMultiplier,s*=this.wheelMultiplier,this.emitter.emit(\"scroll\",{deltaX:e,deltaY:s,event:i})},this.onWindowResize=()=>{this.windowWidth=window.innerWidth,this.windowHeight=window.innerHeight},this.element=i,this.wheelMultiplier=e,this.touchMultiplier=s,this.touchStart={x:null,y:null},this.emitter=new Emitter,window.addEventListener(\"resize\",this.onWindowResize,!1),this.onWindowResize(),this.element.addEventListener(\"wheel\",this.onWheel,{passive:!1}),this.element.addEventListener(\"touchstart\",this.onTouchStart,{passive:!1}),this.element.addEventListener(\"touchmove\",this.onTouchMove,{passive:!1}),this.element.addEventListener(\"touchend\",this.onTouchEnd,{passive:!1})}on(t,i){return this.emitter.on(t,i)}destroy(){this.emitter.destroy(),window.removeEventListener(\"resize\",this.onWindowResize,!1),this.element.removeEventListener(\"wheel\",this.onWheel),this.element.removeEventListener(\"touchstart\",this.onTouchStart),this.element.removeEventListener(\"touchmove\",this.onTouchMove),this.element.removeEventListener(\"touchend\",this.onTouchEnd)}}class Lenis{constructor({wrapper:t=window,content:i=document.documentElement,wheelEventsTarget:e=t,eventsTarget:s=e,smoothWheel:o=!0,syncTouch:n=!1,syncTouchLerp:l=.075,touchInertiaMultiplier:r=35,duration:h,easing:a=(t=>Math.min(1,1.001-Math.pow(2,-10*t))),lerp:c=.1,infinite:d=!1,orientation:u=\"vertical\",gestureOrientation:p=\"vertical\",touchMultiplier:m=1,wheelMultiplier:v=1,autoResize:g=!0,prevent:w,virtualScroll:S,__experimental__naiveDimensions:f=!1}={}){this.__isScrolling=!1,this.__isStopped=!1,this.__isLocked=!1,this.userData={},this.lastVelocity=0,this.velocity=0,this.direction=0,this.onPointerDown=t=>{1===t.button&&this.reset()},this.onVirtualScroll=t=>{if(\"function\"==typeof this.options.virtualScroll&&!1===this.options.virtualScroll(t))return;const{deltaX:i,deltaY:e,event:s}=t;if(this.emitter.emit(\"virtual-scroll\",{deltaX:i,deltaY:e,event:s}),s.ctrlKey)return;const o=s.type.includes(\"touch\"),n=s.type.includes(\"wheel\");this.isTouching=\"touchstart\"===s.type||\"touchmove\"===s.type;if(this.options.syncTouch&&o&&\"touchstart\"===s.type&&!this.isStopped&&!this.isLocked)return void this.reset();const l=0===i&&0===e,r=\"vertical\"===this.options.gestureOrientation&&0===e||\"horizontal\"===this.options.gestureOrientation&&0===i;if(l||r)return;let h=s.composedPath();h=h.slice(0,h.indexOf(this.rootElement));const a=this.options.prevent;if(h.find((t=>{var i,e,s,l,r;return t instanceof Element&&(\"function\"==typeof a&&(null==a?void 0:a(t))||(null===(i=t.hasAttribute)||void 0===i?void 0:i.call(t,\"data-lenis-prevent\"))||o&&(null===(e=t.hasAttribute)||void 0===e?void 0:e.call(t,\"data-lenis-prevent-touch\"))||n&&(null===(s=t.hasAttribute)||void 0===s?void 0:s.call(t,\"data-lenis-prevent-wheel\"))||(null===(l=t.classList)||void 0===l?void 0:l.contains(\"lenis\"))&&!(null===(r=t.classList)||void 0===r?void 0:r.contains(\"lenis-stopped\")))})))return;if(this.isStopped||this.isLocked)return void s.preventDefault();if(!(this.options.syncTouch&&o||this.options.smoothWheel&&n))return this.isScrolling=\"native\",void this.animate.stop();s.preventDefault();let c=e;\"both\"===this.options.gestureOrientation?c=Math.abs(e)>Math.abs(i)?e:i:\"horizontal\"===this.options.gestureOrientation&&(c=i);const d=o&&this.options.syncTouch,u=o&&\"touchend\"===s.type&&Math.abs(c)>5;u&&(c=this.velocity*this.options.touchInertiaMultiplier),this.scrollTo(this.targetScroll+c,Object.assign({programmatic:!1},d?{lerp:u?this.options.syncTouchLerp:1}:{lerp:this.options.lerp,duration:this.options.duration,easing:this.options.easing}))},this.onNativeScroll=()=>{if(clearTimeout(this.__resetVelocityTimeout),delete this.__resetVelocityTimeout,this.__preventNextNativeScrollEvent)delete this.__preventNextNativeScrollEvent;else if(!1===this.isScrolling||\"native\"===this.isScrolling){const t=this.animatedScroll;this.animatedScroll=this.targetScroll=this.actualScroll,this.lastVelocity=this.velocity,this.velocity=this.animatedScroll-t,this.direction=Math.sign(this.animatedScroll-t),this.isScrolling=\"native\",this.emit(),0!==this.velocity&&(this.__resetVelocityTimeout=setTimeout((()=>{this.lastVelocity=this.velocity,this.velocity=0,this.isScrolling=!1,this.emit()}),400))}},window.lenisVersion=\"1.1.9\",t&&t!==document.documentElement&&t!==document.body||(t=window),this.options={wrapper:t,content:i,wheelEventsTarget:e,eventsTarget:s,smoothWheel:o,syncTouch:n,syncTouchLerp:l,touchInertiaMultiplier:r,duration:h,easing:a,lerp:c,infinite:d,gestureOrientation:p,orientation:u,touchMultiplier:m,wheelMultiplier:v,autoResize:g,prevent:w,virtualScroll:S,__experimental__naiveDimensions:f},this.animate=new Animate,this.emitter=new Emitter,this.dimensions=new Dimensions({wrapper:t,content:i,autoResize:g}),this.updateClassName(),this.userData={},this.time=0,this.velocity=this.lastVelocity=0,this.isLocked=!1,this.isStopped=!1,this.isScrolling=!1,this.targetScroll=this.animatedScroll=this.actualScroll,this.options.wrapper.addEventListener(\"scroll\",this.onNativeScroll,!1),this.options.wrapper.addEventListener(\"pointerdown\",this.onPointerDown,!1),this.virtualScroll=new VirtualScroll(s,{touchMultiplier:m,wheelMultiplier:v}),this.virtualScroll.on(\"scroll\",this.onVirtualScroll)}destroy(){this.emitter.destroy(),this.options.wrapper.removeEventListener(\"scroll\",this.onNativeScroll,!1),this.options.wrapper.removeEventListener(\"pointerdown\",this.onPointerDown,!1),this.virtualScroll.destroy(),this.dimensions.destroy(),this.cleanUpClassName()}on(t,i){return this.emitter.on(t,i)}off(t,i){return this.emitter.off(t,i)}setScroll(t){this.isHorizontal?this.rootElement.scrollLeft=t:this.rootElement.scrollTop=t}resize(){this.dimensions.resize()}emit(){this.emitter.emit(\"scroll\",this)}reset(){this.isLocked=!1,this.isScrolling=!1,this.animatedScroll=this.targetScroll=this.actualScroll,this.lastVelocity=this.velocity=0,this.animate.stop()}start(){this.isStopped&&(this.isStopped=!1,this.reset())}stop(){this.isStopped||(this.isStopped=!0,this.animate.stop(),this.reset())}raf(t){const i=t-(this.time||t);this.time=t,this.animate.advance(.001*i)}scrollTo(t,{offset:i=0,immediate:e=!1,lock:s=!1,duration:o=this.options.duration,easing:n=this.options.easing,lerp:l=this.options.lerp,onStart:r,onComplete:h,force:a=!1,programmatic:c=!0,userData:d={}}={}){if(!this.isStopped&&!this.isLocked||a){if(\"string\"==typeof t&&[\"top\",\"left\",\"start\"].includes(t))t=0;else if(\"string\"==typeof t&&[\"bottom\",\"right\",\"end\"].includes(t))t=this.limit;else{let e;if(\"string\"==typeof t?e=document.querySelector(t):t instanceof HTMLElement&&(null==t?void 0:t.nodeType)&&(e=t),e){if(this.options.wrapper!==window){const t=this.rootElement.getBoundingClientRect();i-=this.isHorizontal?t.left:t.top}const s=e.getBoundingClientRect();t=(this.isHorizontal?s.left:s.top)+this.animatedScroll}}if(\"number\"==typeof t&&(t+=i,t=Math.round(t),this.options.infinite?c&&(this.targetScroll=this.animatedScroll=this.scroll):t=clamp(0,t,this.limit),t!==this.targetScroll)){if(this.userData=d,e)return this.animatedScroll=this.targetScroll=t,this.setScroll(this.scroll),this.reset(),this.preventNextNativeScrollEvent(),this.emit(),null==h||h(this),void(this.userData={});c||(this.targetScroll=t),this.animate.fromTo(this.animatedScroll,t,{duration:o,easing:n,lerp:l,onStart:()=>{s&&(this.isLocked=!0),this.isScrolling=\"smooth\",null==r||r(this)},onUpdate:(t,i)=>{this.isScrolling=\"smooth\",this.lastVelocity=this.velocity,this.velocity=t-this.animatedScroll,this.direction=Math.sign(this.velocity),this.animatedScroll=t,this.setScroll(this.scroll),c&&(this.targetScroll=t),i||this.emit(),i&&(this.reset(),this.emit(),null==h||h(this),this.userData={},this.preventNextNativeScrollEvent())}})}}}preventNextNativeScrollEvent(){this.__preventNextNativeScrollEvent=!0,requestAnimationFrame((()=>{delete this.__preventNextNativeScrollEvent}))}get rootElement(){return this.options.wrapper===window?document.documentElement:this.options.wrapper}get limit(){return this.options.__experimental__naiveDimensions?this.isHorizontal?this.rootElement.scrollWidth-this.rootElement.clientWidth:this.rootElement.scrollHeight-this.rootElement.clientHeight:this.dimensions.limit[this.isHorizontal?\"x\":\"y\"]}get isHorizontal(){return\"horizontal\"===this.options.orientation}get actualScroll(){return this.isHorizontal?this.rootElement.scrollLeft:this.rootElement.scrollTop}get scroll(){return this.options.infinite?function modulo(t,i){return(t%i+i)%i}(this.animatedScroll,this.limit):this.animatedScroll}get progress(){return 0===this.limit?1:this.scroll/this.limit}get isScrolling(){return this.__isScrolling}set isScrolling(t){this.__isScrolling!==t&&(this.__isScrolling=t,this.updateClassName())}get isStopped(){return this.__isStopped}set isStopped(t){this.__isStopped!==t&&(this.__isStopped=t,this.updateClassName())}get isLocked(){return this.__isLocked}set isLocked(t){this.__isLocked!==t&&(this.__isLocked=t,this.updateClassName())}get isSmooth(){return\"smooth\"===this.isScrolling}get className(){let t=\"lenis\";return this.isStopped&&(t+=\" lenis-stopped\"),this.isLocked&&(t+=\" lenis-locked\"),this.isScrolling&&(t+=\" lenis-scrolling\"),\"smooth\"===this.isScrolling&&(t+=\" lenis-smooth\"),t}updateClassName(){this.cleanUpClassName(),this.rootElement.className=`${this.rootElement.className} ${this.className}`.trim()}cleanUpClassName(){this.rootElement.className=this.rootElement.className.replace(/lenis(-\\w+)?/g,\"\").trim()}}\n//# sourceMappingURL=lenis.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/locomotive-scroll/node_modules/lenis/dist/lenis.mjs\n"));

/***/ })

}]);