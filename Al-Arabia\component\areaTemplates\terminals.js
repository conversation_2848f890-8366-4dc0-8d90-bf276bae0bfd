import React, { useState, useEffect } from "react";
import style from "@/styles/terminals.module.scss";
import HeadMarquee from "@/component/HeadMarquee";
import InnerBanner from "@/component/InnerBanner";
import Tab from "@mui/material/Tab";
import TabContext from "@mui/lab/TabContext";
import rtl from "@/styles/rtl.module.scss";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import { Box } from "@mui/material";
import comon from "@/styles/comon.module.scss";
import Image from "next/image";
import Link from "next/link";
import Head from "next/head";
import { gsap } from "gsap";
import Masonry from "react-masonry-css";
import SliderBannerText from "@/component/SliderBannerText";
import ImageGrid from "@/component/ImageGrid";
import { useRouter } from "next/router";
import StartCampaign from "../StartCampaign";
import ContactSection from "../ContactSection";
import parse from "html-react-parser";
const terminals = ({ pageData }) => {
    const { locale } = useRouter();
    const [value, setValue] = React.useState("1");

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };

    const Tabs = [
        "Terminal 1",
        "Terminal 2",
        "Terminal 3",
        "Terminal 4",
        "Terminal 5",
    ];

    const [isSticky, setIsSticky] = useState(false);

    useEffect(() => {
        window.addEventListener("scroll", function () {
            const tab = document.getElementById("tab_head");
            const offsetTop = tab?.offsetTop;

            if (window.scrollY >= offsetTop - 67) {
                setIsSticky(true);
            } else {
                setIsSticky(false);
            }
        });
    }, [isSticky]);

    const images = [
        "/images/altanfeethi-gallerynew5.jpg",
        "/images/terminal12.png",
        "/images/airport_gallery8.jpg",
        "/images/terminal14.png",

        "/images/terminal16.png",
        "/images/terminal17.png",
        "/images/terminal18.png",
        "/images/terminal19.png",
        "/images/terminal110.png",
    ];
    const images2 = [
        "/images/altanfeethi-gallerynew5.jpg",
        "/images/terminal16.png",
        "/images/terminal17.png",
        "/images/terminal18.png",
        "/images/terminal19.png",
        "/images/terminal12.png",
        "/images/terminal14.png",


        "/images/airport_gallery8.jpg",
        "/images/terminal110.png",
    ];
    const allImages = pageData.acf.terminals
        .map(terminal => terminal.gallery)
        .flat();

    const breakpointColumns = {
        default: 3,
        1366: 3,
        1100: 3,
        700: 2,

    };

    return (
        <div>
            <HeadMarquee />

            {pageData.acf.banner_details.description_field ? (

                <SliderBannerText
                    title={pageData.acf.banner_details.title}
                    paragraph={pageData.acf.banner_details.description_}
                    details={pageData.acf.banner_details}
                    className={'width_870'}
                    bgColor={true}
                    extra_height_class={true}
                    full_height_banner={true}
                />
            ) : (
                <InnerBanner showLink={false} title={pageData.acf.banner_details.title} details={pageData.acf.banner_details} />
            )}

            <section className={`${comon.pt_30} ${comon.pb_30}`}>
                <div className={`${comon.wrap}  `}>
                    <div className={comon.bread_crumb_mian}>
                        <ul
                            className={`${comon.bread_crumb_mian_ul} ${rtl.bread_crumb_mian_ul}`}
                        >
                            <li>
                                <Link href={"/"}>
                                    <div className={` ${comon.bread_icon}  `}>
                                        <Image
                                            src={"/images/brud_icn.svg"}
                                            height={30}
                                            width={30}
                                            alt=""
                                        />
                                    </div>
                                </Link>
                            </li>
                            {Array.isArray(pageData?._embedded?.up) && pageData._embedded.up[0]?.parent_slug && pageData._embedded.up[0]?.parent_title && (
                                <li>
                                    <Link href={pageData._embedded.up[0].parent_slug}>
                                    {pageData._embedded.up[0].parent_title}
                                    </Link>
                                </li>
                            )}
                            {pageData.parent_title && (
                                <li>
                                    <Link href={`${pageData.parent_slug}`}>{pageData.parent_title}</Link>
                                </li>
                            )}
                            <li>
                                <span>{pageData.title.rendered}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </section>
            <section className={`  ${comon.pb_50}`}>
                <div className={` custom_tabs d_flex_tab`}>
                    <Box sx={{ width: "100%", typography: "body1" }}>
                        <TabContext value={value}>
                            <div
                                id="tab_head"
                                className={` ${isSticky == true ? comon.tab_sticky : ""}`}
                            >
                                <div className={`${comon.wrap}`}>
                                    <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                                        <TabList
                                            onChange={handleChange}
                                            aria-label="lab API tabs example"
                                        >
                                            {/* All Tab */}
                                            {/* <Tab
                                                className={` ${isSticky == true ? "sticky_tab" : ""}`}
                                                label={locale == "ar" ? 'الكل' : 'All'}
                                                value="1"
                                            /> */}
                                            {/* Terminal Tabs */}
                                            {pageData.acf.terminals.map((item, index) => {
                                                return (
                                                    <Tab
                                                        key={index}
                                                        className={` ${isSticky == true ? "sticky_tab" : ""} base_font`}
                                                        // label={<span className="base_font">{item.terminal_name}</span>}
                                                        label={parse(item.terminal_name)}
                                                        value={(index + 1).toString()}

                                                    />
                                                );
                                            })}

                                            <style jsx>{`
                                                .rtl .base_font {
                                                    font-family: Aller, sans-serif !important;
                                                }
                                            `}</style>
                                        </TabList>
                                    </Box>
                                </div>
                            </div>

                            {/* All TabPanel */}
                            {/* <TabPanel value="1">
                                <div className={comon.mt_30}>
                                    <ImageGrid images={allImages} />
                                </div>
                            </TabPanel> */}

                            {/* Individual TabPanels */}
                            {pageData.acf.terminals.map((terminal, terminalIndex) => {
                                return (
                                    <TabPanel
                                        key={terminalIndex}
                                        value={(terminalIndex + 1).toString()} // Ensure the value matches the corresponding Tab
                                    >
                                        {/* <Masonry
                                            breakpointCols={breakpointColumns}
                                            className="my-masonry-grid"
                                            columnClassName="my-masonry-grid_column"
                                        >
                                            {terminal.gallery.map((src, index) => (
                                                <div
                                                    className={style.img_body}
                                                    key={index}
                                                    data-aos="fade-in"
                                                    data-aos-duration="1000"
                                                    data-aos-delay={index * 100}
                                                >
                                                    <Image
                                                        src={src}
                                                        height={300}
                                                        width={400}
                                                        alt={`Image ${terminalIndex + 1}-${index + 1}`}
                                                    />
                                                </div>
                                            ))}
                                        </Masonry> */}

                                        <div className={comon.mt_30}>
                                            <ImageGrid images={terminal.gallery} />
                                        </div>
                                    </TabPanel>
                                );
                            })}
                        </TabContext>

                    </Box>
                </div>
            </section>
            <StartCampaign />

            <section className={`${comon.pt_60} `}>
                <ContactSection
                    title="Request for Quotation"
                    paragraph="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore."
                    button="Request a Quote"
                    showAutoMargin={false}
                />
            </section>
        </div>
    );
};

export default terminals;
