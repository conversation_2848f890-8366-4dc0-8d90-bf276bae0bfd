import React, { useState, useEffect, } from "react";
import contactF from "@/styles/contactForm.module.scss";
import comon from "@/styles/comon.module.scss";
import rtl from "@/styles/rtl.module.scss";
import Link from "next/link";
import { useRouter } from 'next/router';


const RequestSeasonCard = ({ button, showAutoMargin, classNameSecond, whiteBtn }) => {

    const { locale } = useRouter();
    const langCode = locale === 'ar' ? 'ar' : 'en';

    const [formData1, setFormData] = useState({
        Fullname: '',
        EmailAddress: '',
        PhoneNumber: '',
        company: '',
        Message: '',
    });

    const [validationErrors, setValidationErrors] = useState({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [responseMessage, setResponseMessage] = useState('');
    const [sresponseMessage, setSresponseMessage] = useState('');
    const [fieldErrors, setFieldErrors] = useState({});

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData({ ...formData1, [name]: value });
        setResponseMessage('');
        setValidationErrors({})
        setSresponseMessage('');
        if (name === "PhoneNumber") {
            const numericValue = value.replace(/\D/g, "");
            setFormData({ ...formData1, [name]: numericValue });
        }
        if (name === "FirstName" || name == "LastName") {
            const alphabeticValue = value.replace(/[^a-zA-Z]/g, "");
            setFormData({ ...formData1, [name]: alphabeticValue });
        }
    };

    const formId = langCode === 'ar' ? 3399 : 943;
    const unitTag = langCode === 'ar' ? 'wpcf7-f3399-o1' : 'wpcf7-f943-o1';

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);
        setResponseMessage('');

        // Validate form data
        // if (!validateForm()) {
        // 	setIsSubmitting(false);
        // 	return;
        // }

        // Create a new FormData object
        const formData = new FormData();
        formData.append('Fullname', formData1.Fullname);
        formData.append('EmailAddress', formData1.EmailAddress);
        formData.append('PhoneNumber', formData1.PhoneNumber);
        formData.append('company', formData1.company);
        formData.append('Message', formData1.Message);
        formData.append("_wpcf7_unit_tag", unitTag);

        try {
            const response = await fetch(`${process.env.NEXT_PUBLIC_CONTACT_API_URL}/${formId}/feedback`, {
                method: 'POST',
                body: formData,
                redirect: 'follow',
            });

            const result = await response.json();
            console.log(result)
            setIsSubmitting(false);

            if (response.ok) {
                if (result.status === "validation_failed") {
                    const fieldErrors = result.invalid_fields.reduce((acc, fieldError) => {
                        acc[fieldError.field] = fieldError.message;
                        return acc;
                    }, {});
                    setFieldErrors(fieldErrors);
                    // setResponseMessage('Error in fields');
                    // if (fieldErrors['EmailAddress']) {
                    //     setFormData(prevData => ({ ...prevData, EmailAddress: '' }));
                    // }
                    setTimeout(() => {
                        setResponseMessage('');
                        setFieldErrors({});
                    }, 3000);


                } else if (result.status === "mail_sent") {
                    setSresponseMessage(result.message);
                    setTimeout(() => {
                        setSresponseMessage('');
                    }, 3000);
                    setFormData({ Fullname: '', company: '', EmailAddress: '', PhoneNumber: '', Message: '' });
                } else {
                    setResponseMessage('An unexpected error occurred. Please try again.');
                    setTimeout(() => {
                        setResponseMessage('');
                    }, 3000);
                }

            } else {
                setResponseMessage(result.message || 'Something went wrong. Please try again.');
                setTimeout(() => {
                    setResponseMessage('');
                }, 3000);
            }
        } catch (error) {
            console.error('Error:', error);
            setIsSubmitting(false);
            setResponseMessage('An error occurred while submitting the form.');
        }
    };

    return (
        <form action="#" onSubmit={handleSubmit}>
            <ul
                className={`${contactF.contact_form_main_ul} ${classNameSecond ? contactF[classNameSecond] : ""
                    }`}
            >
                <li data-aos="fade-in" data-aos-duration="1000" className={`${contactF.form_li}`}>
                    <input
                        className={`${contactF.input_fld} ${contactF.font_14}`}
                        type="text"
                        placeholder={locale == "ar" ? "الاسم" : "Name"}
                        value={formData1.Fullname}
                        onChange={handleChange}
                        name="Fullname"
                    ></input>
                    {fieldErrors.Fullname && <p className={`${contactF.error_msg} ${rtl.error_msg}`} style={{ color: 'red', marginTop: '5px', fontSize: '12px', padding: '0 8px' }}>{fieldErrors.Fullname}</p>}
                </li>
                <li data-aos="fade-in" data-aos-duration="1000" className={`${contactF.form_li}`}>
                    <input
                        className={`${contactF.input_fld} ${contactF.font_14}`}
                        type="text"
                        placeholder={locale == "ar" ? "البريد الإلكتروني" : "Email"}
                        value={formData1.EmailAddress}
                        onChange={handleChange}
                        name="EmailAddress"
                    ></input>
                    {fieldErrors.EmailAddress && <p className={`${contactF.error_msg} ${rtl.error_msg}`} style={{ color: 'red', marginTop: '5px', fontSize: '12px', padding: '0 8px' }}>{fieldErrors.EmailAddress}</p>}
                </li>
                <li data-aos="fade-in" data-aos-duration="1000" className={`${contactF.form_li}`}>
                    <input
                        className={`${contactF.input_fld} ${contactF.font_14}`}
                        type="text"
                        placeholder={locale == "ar" ? "رقم الهاتف" : "Phone"}
                        value={formData1.PhoneNumber}
                        onChange={handleChange}
                        name="PhoneNumber"
                    ></input>
                    {fieldErrors.PhoneNumber && <p className={`${contactF.error_msg} ${rtl.error_msg}`} style={{ color: 'red', marginTop: '5px', fontSize: '12px', padding: '0 8px' }}>{fieldErrors.PhoneNumber}</p>}
                </li>
                <li data-aos="fade-in" data-aos-duration="1000" className={`${contactF.form_li}`}>
                    <input
                        className={`${contactF.input_fld} ${contactF.font_14}`}
                        type="text"
                        placeholder={locale == "ar" ? "الشركة" : "Company"}
                        value={formData1.company}
                        onChange={handleChange}
                        name="company"
                    ></input>
                    {fieldErrors.company && <p className={`${contactF.error_msg} ${rtl.error_msg}`} style={{ color: 'red', marginTop: '5px', fontSize: '12px', padding: '0 8px' }}>{fieldErrors.company}</p>}
                </li>

                <li data-aos="fade-in" data-aos-duration="1000" className={`${contactF.form_li} ${comon.w_100}`}>
                    <textarea
                        className={`${contactF.input_fld} ${contactF.font_14} ${contactF.textarea}`}
                        placeholder={locale == "ar" ? "الرسالة" : "Message"}
                        value={formData1.Message}
                        onChange={handleChange}
                        name="Message"
                    ></textarea>
                    {fieldErrors.Message && <p className={`${contactF.error_msg} ${rtl.error_msg}`} style={{ color: 'red', marginTop: '5px', fontSize: '12px', padding: '0 8px' }}>{fieldErrors.Message}</p>}
                </li>

                <li data-aos="fade-in" data-aos-duration="1000" className={`${comon.w_100} ${comon.pt_30}`}>
                    {isSubmitting ? (
                        <input
                            type="submit"
                            className={`${comon.buttion} ${comon.but_h_02} ${contactF.contact_but
                                } ${contactF.contact_but_width}
                        ${comon.but_fill} ${whiteBtn ? comon.whiteBtn : ""} ${showAutoMargin ? comon.ml_auto : ""
                                }`}
                            value="..."
                            style={{ maxWidth: '180px', textAlign: 'center' }}
                        />
                    ) : (
                        <input
                            type="submit"
                            className={`${comon.buttion} ${comon.but_h_02} ${contactF.contact_but
                                } ${contactF.contact_but_width}
                        ${comon.but_fill} ${whiteBtn ? comon.whiteBtn : ""} ${showAutoMargin ? comon.ml_auto : ""
                                }`}
                            value={locale == "ar" ? "إرسال " : "Submit"}
                            style={{ maxWidth: '180px', textAlign: 'center' }}
                        />
                    )}
                </li>
                {responseMessage && <p className={`${contactF.form_response_msg} ${rtl.form_response_msg}  `} style={{ color: 'red' }}>{responseMessage}</p>}
                {sresponseMessage && <p className={`${contactF.form_response_msg} ${rtl.form_response_msg}  `} style={{ color: 'green' }}>{sresponseMessage}</p>}
            </ul>
        </form>
    );
};

export default RequestSeasonCard;
