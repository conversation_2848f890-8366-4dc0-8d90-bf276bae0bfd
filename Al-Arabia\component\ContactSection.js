import React, { useState, useEffect } from "react";
import contact from "@/styles/contact.module.scss";
import comon from "@/styles/comon.module.scss";
import ContactForm from "@/component/ContactForm";
import { useRouter } from "next/router";
import parse from 'html-react-parser';
import RequestQuotation from "./form/RequestQuotation";
const ContactSection = ({	
	showAutoMargin,
	whiteBtn,
	className = "",
	classNameSecond = "",
	button
}) => {

	const { locale } = useRouter(); 
	const [options, setOptions] = useState(null); 

		useEffect(() => {
			const fetchOptions = async () => {
			  try {
				const res = await fetch(
				  `${process.env.NEXT_PUBLIC_API_BASE_URL}/option-field/request_detyails?lang=${locale}`
				);
				const data = await res.json();
				setOptions(data);
			  } catch (error) {
				console.error("Error fetching options:", error);
			  }
			};
		
			fetchOptions();
		  }, [locale]);
		  
		  if (!options) {
			return <header></header>;
		  }
	return (
		<div className={`${comon.wrap} ${comon.d_flex_wrap} ${comon.pb_60}  `}>
			<div className={`${contact.contact_left_block} ${className}`}>
				<div
					data-aos="fade-in"
					data-aos-duration="1000"
					className={`${comon.title_30} ${comon.text_collor} ${comon.pb_10} ${comon.mt_10}`}
				>
					<h3>{parse(options.title)}</h3>
				</div>

				<div
					data-aos="fade-in"
					data-aos-duration="1000"
					className={`${comon.text_collor}  ${contact.text_wrap}`}
				>
					<p>{parse(options.description)}</p>
				</div>
			</div>

			<div
				className={`${contact.contact_right_block}`}
				data-aos="fade-in"
				data-aos-duration="1000"
			>
				{/* <RequestQuotation button={button} whiteBtn={whiteBtn} classNameSecond={classNameSecond}
				 className={className} showAutoMargin={showAutoMargin} /> */}

				{/* <ContactForm
					button={button}
					showAutoMargin={showAutoMargin}
					classNameSecond={classNameSecond}
					whiteBtn={whiteBtn}
				/> */}

				<RequestQuotation 
				button={button}
				showAutoMargin={showAutoMargin}
				classNameSecond={classNameSecond}
				whiteBtn={whiteBtn}
				/>
			</div>
		</div>
	);
};

export default ContactSection;
