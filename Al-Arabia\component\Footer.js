import React, { useEffect, useState } from "react";
import Link from "next/link";
import comon from "@/styles/comon.module.scss";
import footer from "@/styles/footer.module.scss";
import rtl from "@/styles/rtl.module.scss";
import Image from "next/image";
import Newsletter from "./form/newsletter";
import { useRouter } from "next/router";

const Footer = (props) => {
	const [isMobile, setIsMobile] = useState(false);

	useEffect(() => {
		// Function to check if the screen is mobile size
		const checkScreenSize = () => {
			setIsMobile(window.innerWidth <= 768); // Mobile breakpoint
		};

		// Check screen size on load
		checkScreenSize();

		// Add event listener for resize
		window.addEventListener("resize", checkScreenSize);

		// Cleanup event listener on unmount
		return () => {
			window.removeEventListener("resize", checkScreenSize);
		};
	}, []);
	const { locale } = useRouter();
	const [options, setOptions] = useState(null);
	useEffect(() => {
		const fetchOptions = async () => {
			try {
				const res = await fetch(
					`${process.env.NEXT_PUBLIC_API_BASE_URL}/option-field/footer_group?lang=${locale}`
				);
				const data = await res.json();
				setOptions(data);
			} catch (error) {
				console.error("Error fetching options:", error);
			}
		};

		fetchOptions();
	}, [locale]);

	const [isAccordion, setIsAccordion] = useState();

	const AccordionShow = (index) => {
		if (isAccordion != index) {
			setIsAccordion(index);
		} else {
			setIsAccordion();
		}
	};

	const sizes = [
		{ width: 20, height: 15 },
		{ width: 15, height: 15 },
		{ width: 15, height: 15 },
		{ width: 15, height: 15 },
	];
	if (!options) {
		return;
	}



	return (
		<footer className={`${footer.footer} ${comon.pt_50}`}>
			<div className={`${comon.wrap}`}>
				<div className={`${comon.d_flex_wrap} ${footer.mobile_footer}`}>
					<div className={`${footer.footer_cl_01} ${rtl.footer_cl_01}`}>
						<div className={`${comon.w_100} ${comon.mb_15}`}>
							<Image
								src={options.footer_logo}
								width={41}
								height={37}
								alt="Footer Logo"
								style={{
									height: "auto",
									maxWidth: "100%",
									display: "block",
								}}
								quality={100}
							/>
						</div>
						<p className={`${footer.mobile_hide}`} dangerouslySetInnerHTML={{ __html: options.text }}></p>

						<div className={`${footer.subscribe_block}`}>
							<p dangerouslySetInnerHTML={{ __html: options.newsletter_text }}></p>
							<Newsletter />
						</div>


						<ul
							className={`${comon.mt_70} ${footer.social_ul} ${isMobile && footer.mobile_social_ul} ${isMobile ? comon.mob_show : ""
								}`}
						>
							{options.social_media && options.social_media.map((item, index) => {
								const { width, height } = sizes[index] || { width: 15, height: 15 };
								return (
									<li key={index}>
										<Link href={item.url}
											target="_blank"
										>
											<Image
												src={item.icon}
												width={width}
												height={height}
												alt="Facebook"
												style={{
													height: "auto",
													maxWidth: "100%",
													display: "block",
												}}
												quality={100}
											/>
										</Link>
									</li>
								)
							})}
						</ul>







					</div>
					<div className={`${footer.footer_cl_02}`}>
						<div className={`${footer.footer_bot_link_ul_outer}`}>
							<ul className={`${footer.footer_ul}`}>

								<li className={`${footer.footer_link_cl_01} ${footer.accordion_block_li}`}>
									<h5>{options.title}</h5>
									<ul
										className={`${footer.footer_link} ${isAccordion == 1
											? footer.accordion_show
											: footer.accordion_hide
											}`}
									>
										{options?.menu_.map((item, index) => {
											return (
												<li key={index}>
													<Link href={item.menu_item.url}>{item.menu_item.title}</Link>
												</li>
											)
										})}


									</ul>
								</li>


								<li className={`${footer.footer_link_cl_02} ${footer.accordion_block_li}`}>
									<h5 style={{ minHeight: '20px' }}>{options.title_1}</h5>
									<ul
										className={`${footer.footer_link} ${isAccordion == 2
											? footer.accordion_show
											: footer.accordion_hide
											}`}
									>
										{options.menu__2 && options.menu__2.map((item, index) => {
											return (
												<li key={index}>
													<Link href={item.menu_item.url}>{item.menu_item.title}</Link>
												</li>
											)
										})}
									</ul>
								</li>

								<li className={`${footer.footer_link_cl_03}`}>
									<h5>{options.title_2}</h5>
									<p>
										<Link href={`mailto:${options.mail}`}>
											{options.mail}
										</Link>
									</p>
									<p>
										<Link href={`tel:${options.phone}`}
											// style={{ fontFamily: 'Aller !important' }}
											className="base_font"

										>
											{options.phone}
										</Link>
									</p>
									<p dangerouslySetInnerHTML={{ __html: options.address }}></p>

								</li>
							</ul>
						</div>

						{isMobile && (
							<ul
								className={`${comon.mt_70} ${footer.social_ul} ${comon.mob_show}`}
							>
								{options.social_media && options.social_media.map((item, index) => {
									const { width, height } = sizes[index] || { width: 15, height: 15 };
									return (
										<li key={index}>
											<Link href={item.url}>
												<Image
													src={item.icon}
													width={width}
													height={height}
													alt="Facebook"
													style={{
														height: "auto",
														maxWidth: "100%",
														display: "block",
													}}
													quality={100}
												/>
											</Link>
										</li>
									)
								})}

							</ul>
						)}
					</div>
				</div>
			</div>

			<div className={`${footer.footer_bot_link_outer}`}>
				<div
					className={`${comon.wrap} ${comon.d_flex_wrap} ${comon.justify_space_bet}`}
				>
					<div className={`${footer.footer_bot_copy_block}`}>
						<p dangerouslySetInnerHTML={{ __html: options.copyright_text }}></p>
					</div>
					<div className={`${footer.footer_bot_link_outer_bot}  ${rtl.footer_bot_link_outer_bot}`}>
						<ul className={`${footer.footer_bot_link_ul} ${rtl.footer_bot_link_ul}`}>
							{options.bottom_bar_menu && options.bottom_bar_menu.map((menu, item) => {
								return (
									<li>
										<Link href={menu.menu_item.url}>{menu.menu_item.title}</Link>
									</li>
								)
							})}

						</ul>
					</div>
				</div>
			</div>
		</footer>
	);
};

export default Footer;


