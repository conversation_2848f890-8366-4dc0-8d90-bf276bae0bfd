import React, { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import header from "@/styles/header.module.scss";
import { gsap } from "gsap";
import { useRouter } from "next/router";
import Head from "next/head";
import rtl from "@/styles/rtl.module.scss";
const HeaderCenterNew = () => {
	const [isSticky, setIsSticky] = useState(false);
	const [isMobile, setIsMobile] = useState(false);
	const router = useRouter();
	const [iswhiteBackground, setIsWhiteBackground] = useState(false);
	const [mobMenu, setMobmenu] = useState(false);
	const [submenuActive, setSubmenuActive] = useState(false);
	const { locale } = useRouter();
	console.log("isMobile", isMobile);
	const [options, setOptions] = useState(null);
	useEffect(() => {
		const isNewsDetail = router.asPath.startsWith("/news/") && router.asPath !== "/news";
		const isContactPage = router.asPath === "/contact-us";

		if (isNewsDetail || isContactPage) {
			setIsWhiteBackground(true);
		} else {
			setIsWhiteBackground(false);
		}
	}, [router]);


	useEffect(() => {
		const handleResize = () => {
			// if (window.innerWidth <= 700) {
			if (window.innerWidth <= 1024) {
				setIsMobile(true);
			} else {
				setIsMobile(false);
				setMobmenu(false);
				setSubmenuActive(false);

				// ---------------
			}
		};

		window.addEventListener("resize", handleResize);

		handleResize();

		return () => {
			window.removeEventListener("resize", handleResize);
		};
	}, []);

	useEffect(() => {
		const menuItems = document.querySelectorAll(".menu-ul > li");
		if (menuItems.length > 0) {
			// Check if the logo element already exists
			if (!document.querySelector(".menu-ul .main-logo")) {
				const middleIndex = Math.floor((menuItems.length - 1) / 2);
				const middleItem = menuItems[middleIndex];

				const newLogoItem = document.createElement("li");
				newLogoItem.classList.add("main-logo");

				// Render a React component within the DOM element
				const logoContainer = document.createElement("div");
				newLogoItem.appendChild(logoContainer);
				middleItem.parentNode.insertBefore(newLogoItem, middleItem.nextSibling);

				// Hydrate the new element with React content
				import("react-dom").then(({ render }) => {
					render(
						<a href={locale === 'ar' ? '/ar/' : '/'}>
							<Image
								src={
									isSticky === true
										? options.logo_
										:
										// "/images/al_arabia_logo11.svg"
										options.logo_white

								}
								alt="Logo"
								width={96}
								height={22}
								priority
								quality={100}
							/>
						</a>,
						logoContainer
					);
				});
			} else {
				// Update the logo when isSticky changes
				const logoContainer = document.querySelector(".menu-ul .main-logo div");
				import("react-dom").then(({ render }) => {
					render(
						<a href={locale === 'ar' ? '/ar/' : '/'}>
							<Image
								src={
									isSticky === true
										? options.logo_
										:
										// "/images/al_arabia_logo11.svg"
										options.logo_white

								}
								alt="Logo"
								width={96}
								height={22}
								priority
								quality={100}
							/>
						</a>,
						logoContainer
					);
				});
			}
		}
	}, [isSticky, options]);

	const switchLocale = locale === "en" ? "ar" : "en";
	const { asPath } = useRouter();

	useEffect(() => {
		const handleScroll = () => {
			const header = document.getElementById("header");
			if (!header) return;
			const stickyPoint = header.offsetTop;

			if (window.pageYOffset > stickyPoint || window.pageYOffset > 0) {
				if (!isSticky) {
					setIsSticky(true);
					gsap.to(header, { duration: 0 });
				}
			} else {
				if (isSticky) {
					setIsSticky(false);
					gsap.to(header, { duration: 0 });
				}
			}
		};

		window.addEventListener("scroll", handleScroll);

		return () => {
			window.removeEventListener("scroll", handleScroll);
		};
	}, [isSticky]);

	const hamburger = () => {
		setMobmenu((prev) => !prev);
		setIsSticky(true);

		// if (isSticky == false) {

		// } else {
		// 	setIsSticky(false)

		// }
	};

	const subMenuShow = () => {
		// if (window.innerWidth <= 700) {
		if (window.innerWidth <= 1024) {
			setSubmenuActive((prev) => !prev);
		} else {
			setSubmenuActive(false);
		}
	};

	//   ----------------mobile menu-------------

	const [ismobileMenu, setIsmobileMenu] = useState();

	const mobileMenu = (index) => {
		if (ismobileMenu != index) {
			setIsmobileMenu(index);
		} else {
			setIsmobileMenu();
		}
	};

	// const ismenuHide = () => {
	// 	console.log('sdfdsfdsfsdfdfsfsdfdsfsfsd');
	// 	setSubmenuActive(false)
	// }

	const [disableHover, setDisableHover] = useState(false);

	const ismenuHide = () => {
		setSubmenuActive(false);

		setDisableHover(true);
		setTimeout(() => setDisableHover(false), 1000);
	};
	// console.log('{router.asPath ', router);
	// console.log('{router.asPath ', router.asPath);


	// console.log('kjfjdshfjdshfdsfsdfgvdsfs', disableHover);


	useEffect(() => {
		const fetchOptions = async () => {
			try {
				const res = await fetch(
					`${process.env.NEXT_PUBLIC_API_BASE_URL}/option-field/heder_details?lang=${locale}`
				);
				const data = await res.json();
				setOptions(data);
			} catch (error) {
				console.error("Error fetching options:", error);
			}
		};

		fetchOptions();
	}, [locale]);

	if (!options) {
		return;
	}
	return (
		<>
			{/* <Head>

				<title>Al Arabia</title>
				<meta name="description" content="Generated by create next app" />
				<link rel="icon" href="/favicon-new.ico" />
			</Head> */}

			{isMobile == false ? (
				<header
					className={`${header.header_main} ${router.asPath == '/' ? header.header_top_home : ''} ${isSticky == true ? header.sticky : ""
						}`}
					id="header"
				>
					<div className={`${header.menu_block_02}`}>
						<Link href={"#"} className={`${header.mob_logo}`}>
							<Image
								src={
									isSticky === true
										? options.logo_
										:
										// "/images/al_arabia_logo11.svg"
										options.logo_white

								}
								alt="Logo"
								width={96}
								height={22}
								priority
								quality={100}
							/>
						</Link>

						<div
							className={`${header.style_mob_menu}  
								${isSticky ? header.active_nav : ""}
								${mobMenu ? header.active_nav : ""}
								${submenuActive ? header.active_nav : ""}`}
						>
							<ul
								className={`${header.menu_ul_block} menu-ul 
								${isSticky ? header.sticky : ""}
								${mobMenu ? header.active1 : ""}
								${submenuActive ? header.active : ""}`}
							>
								{options.menu.slice(0, 4).map((item, index) => {
									const isSubmenu = item.submenu && item.sub_menu && item.sub_menu.length > 0;
									const isActiveSubmenu = submenuActive === index + 1;

									return (
										<li
											key={index}
											className={disableHover ? "disable-hover" : ""}
											onMouseEnter={() => !disableHover && isSubmenu && setSubmenuActive(index + 1)}
											onMouseLeave={() => !disableHover && isSubmenu && setSubmenuActive(false)}
										>
											<Link
												href={item.menu_item.url || "#"}
												className={`${header.dropdown_link} ${router.asPath === item.menu_item.url ||
													(router.asPath === "/" && item.menu_item.url === "/")
													? header.active_nav_head
													: ""
													}`}
											>
												{item.menu_item.title}
											</Link>

											{isSubmenu && (
												<>
													<span className={`${header.dropdown_icon}`}>
														<Image
															src="/images/menu_arrow.svg"
															width={14}
															height={8}
															style={{ objectFit: "cover" }}
															alt=""
															quality={100}
														/>
													</span>

													<ul className={`${header.dropdown_menu} ${isActiveSubmenu ? header.submenuActive : ""}`}>
														{item.sub_menu.map((sub, subIndex) => (
															<li key={subIndex}>
																<Link
																	href={sub.menu_item.url}
																	onClick={ismenuHide}
																	className={
																		router.asPath === sub.menu_item.url
																			? header.active_nav_head
																			: ""
																	}
																>
																	{sub.menu_item.title}
																</Link>
															</li>
														))}
													</ul>
												</>
											)}
										</li>
									);
								})}
							</ul>
						</div>

						{/* <Link
							href={""}
							className={`${header.language_switch} ${
								isSticky == true ? header.sticky : ""
							}`}
						>
							AR
						</Link> */}
						{/* <Link
							href={""}
							className={`${header.language_switch_globe} ${header.language_switch
								} ${isSticky == true ? header.sticky : ""}`}
						>
							{iswhiteBackground == false ?
								<div className={`${header.icon}`}>
									<Image
										src={"/images/globe_icon.svg"}
										height={30}
										width={30}
										alt=""
									/>
								</div>
								:
								<div className={`${header.icon} ${header.no_shadow}`}>
									<Image
										src={"/images/globe_icon1.svg"}
										height={30}
										width={30}
										alt=""
									/>
								</div>
							}
						</Link> */}
						<span
							className={`${header.language_switch_globe} ${rtl.language_switch_globe} ${header.language_switch
								} ${rtl.language_switch
								} ${isSticky == true ? header.sticky : ""}`}
							onClick={() =>
								window.location.replace(
									switchLocale === "ar" ? `/ar${asPath}` : asPath
								)
							}
							style={{ cursor: 'pointer' }}
						// locale={switchLocale}
						>
							{isSticky == false && iswhiteBackground == false ? (
								<div className={`${header.icon}`}>
									<Image
										// src={"/images/globe_icon.svg"}
										src={"/images/lang_switch.svg"}
										height={30}
										width={30}
										alt=""
										quality={100}
									/>
								</div>
							) : (
								<div className={`${header.icon} ${header.no_shadow}`}>
									<Image
										// src={"/images/globe_icon1.svg"}
										src={"/images/lang_switch1.svg"}
										height={30}
										width={30}
										alt=""
										quality={100}
									/>
								</div>
							)}
						</span>

						<div
							className={`${header.hamburger} ${mobMenu == true ? header.active : ""
								}  ${isSticky == true ? header.sticky : ""}`}
							onClick={hamburger}
						>
							<span></span>
						</div>
					</div>
				</header >
			) : (
				// ----------------------Mobile-------------------

				<header
					className={`${header.header_main} ${router.asPath == '/' ? header.header_top_home : ''} }`}
					// ${isSticky == true ? header.sticky : ""
					id="header"
				>
					<div className={`${header.menu_block_02}  ${header.center_item}`}>
						<Link href={"/"} className={`${header.mob_logo}`}>
							<Image
								src={
									isSticky === true
										? options.logo_white
										:
										// "/images/al_arabia_logo11.svg"
										options.logo_white

								}
								alt="Logo"
								width={96}
								height={22}
								quality={100}
								priority
							/>
						</Link>


						<div
							className={`${header.style_mob_menu}  
							 ${mobMenu ? header.active_nav : ""} ${submenuActive ? header.active_nav : ""}`}
						>
							<ul
								className={`${header.menu_ul_block} menu-ul ${isSticky ? header.sticky : ""} ${mobMenu ? header.active : ""} ${submenuActive ? header.active : ""}`}
							>
								{options.menu.map((item, index) => (
									<li
										key={index}
										className={`${item.submenu ? header.mobile_menu_sub_list : ""} ${rtl.mobile_menu_sub_list}`}
									>

										<Link
											href={item.menu_item.url}
											className={`${header.dropdown_link} ${ismobileMenu === index && header.active_link}`}
											onClick={() => {
												setMobmenu(false);
												setSubmenuActive(false);
											}}
										>
											{item.menu_item.title}
										</Link>

										{item.submenu && Array.isArray(item.sub_menu) && item.sub_menu.length > 0 && (
											<>
												<span
													className={`${header.dropdown_icon} ${ismobileMenu === index ? header.active : ""}`}
													onClick={() => mobileMenu(index)}
												>
													<Image
														src="/images/menu_arrow.svg"
														width={14}
														height={8}
														style={{ objectFit: "cover" }}
														alt=""
														quality={100}
													/>
												</span>

												{ismobileMenu === index && (
													<ul className={`${header.submenu_list} ${rtl.submenu_list} `} >
														{item.sub_menu.map((subItem, subIndex) => (
															<li key={subIndex}>
																<Link
																	href={subItem.menu_item.url}
																	className={`${router.asPath === subItem.menu_item.url ? header.mob_active_nav_head : ""}`}
																	onClick={() => {
																		setMobmenu(false);
																		setSubmenuActive(false);
																	}}
																>
																	{subItem.menu_item.title}
																</Link>
															</li>
														))}
													</ul>
												)}
											</>
										)}
									</li>
								))}
								<li className={`${header.justify_end} ${header.mob_lang_btn}`}>
									<a
										// onClick={() => window.location.replace(`/${switchLocale}${asPath}`)}
										onClick={() =>
											window.location.replace(
												switchLocale === "ar" ? `/ar${asPath}` : asPath
											)
										}
										style={{ cursor: 'pointer' }}
									// locale={switchLocale}
									>
										{/* {locale === "en" ? "AR" : "EN"} */}
										<div className={`${header.mob_lang_icon}`}>
											<Image
												// src={"/images/globe_icon.svg"}
												src={"/images/lang_switch.svg"}
												height={30}
												width={30}
												alt=""
												quality={100}
											/>
										</div>
									</a>
								</li>
							</ul>
						</div>

						<a
							className={`${header.language_switch} ${header.mob_hide} ${isSticky == true ? header.sticky : ""
								}`}
							// onClick={() => window.location.replace(`/${switchLocale}${asPath}`)}
							onClick={() =>
								window.location.replace(
									switchLocale === "ar" ? `/ar${asPath}` : asPath
								)
							}
							style={{ cursor: 'pointer' }}
						// locale={switchLocale}
						>
							{locale === "en" ? "AR" : "EN"}
						</a>

						<div
							className={`${header.hamburger} ${mobMenu ? header.active : ""} ${isSticky ? header.sticky : ""
								}`}
							onClick={() => {
								hamburger();
								setIsmobileMenu();
								subMenuShow();

								// Toggle class on body
								// if (mobMenu) {
								// 	document.body.classList.remove("menu-active");
								// } else {
								// 	document.body.classList.add("menu-active");
								// }
							}}
						>
							<span></span>
						</div>
					</div>
				</header>
			)}
		</>
	);
};

export default HeaderCenterNew;