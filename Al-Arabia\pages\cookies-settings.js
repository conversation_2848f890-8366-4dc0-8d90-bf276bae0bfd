import React from "react";
import style from "@/styles/PrivacyPolicy.module.scss";
import comon from "@/styles/comon.module.scss";
import InnerBanner from "@/component/InnerBanner";
import { fetchPageById } from "@/lib/api/pageApi";
import parse from "html-react-parser";

const privacypolicy = ({ pageData }) => {
    const banner = pageData.acf.banner_details;
    return (
        <div className={`${style.privacy_policy_container}  `}>
            <InnerBanner showLink={false} title={banner.title} details={banner} />

            <div className={`${style.privacy_policy_section} ${comon.wrap} `}>
                <div className={`${style.content_sec} ${comon.pt_40} ${comon.pb_40}  `}>
                    {pageData.acf.privacy_policy && parse(pageData.acf.privacy_policy)}
                </div>
            </div>
        </div>

    );
};

export default privacypolicy;

export async function getStaticProps({ locale }) {
    const langCode = locale === "ar" ? "ar" : "en";
    //const langCode = "en"
    try {
        const pageData = await fetchPageById("cookies-settings", langCode);
        return {
            props: {
                pageData,
            },
            revalidate: 10,
        };
    } catch (error) {
        console.error("Error fetching data:", error);
        return {
            props: {
                pageData: null,
            },
        };
    }
}
