@import "variable", "base", "mixin";

.banner_main {
    background: #ccc;
    height: 100vh;


    .banner_slide_main {
        align-items: center;
        display: flex;
        flex-wrap: wrap;

        .banner_main_swipper {
            height: 100%;
            width: 100%;
        }

        .banner_main_img {
            width: 25%;
            height: 100%;

            img {
                width: auto;
                height: 100%;
                object-fit: contain;
            }
        }

        .banner_txt_block {
            width: 100%;
            height: 100%;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
        }
    }

}


.banner_main {
    background: linear-gradient(107.56deg,
            rgba(103, 90, 167, 1) 0%,
            rgba(58, 48, 100, 1) 100%);

    position: relative;

    .banner_txt_block {
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;

        h2 {
            color: #fff;
            font-size: 10rem;
            font-weight: 700;
            text-align: center;
            line-height: 100%;
            font-family: var(aller_bd);

            span {
                width: 100%;
                display: block;
                font-weight: 400;
                font-family: var(--aller_lt);
                color: #fff;
            }

            @media #{$media-700} {
                font-size: 4rem;
            }

        }
    }


    .banner_video_main {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;

        // .video_block{  transform: translate3d(0px, -18vh, 0px) scale3d(0.5, 0.5, 1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
        //             transform-style: preserve-3d;}
    }

    .video_block {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}



.banner_bottom_img_block {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    margin-left: -6%;
    margin-right: -6%;
    margin-bottom: 35px;

    li {
        list-style: none;
        padding: 0 6%;

        h5 {
            color: #fff;
            @include rem(17);
            font-weight: 500;

            span {
                font-weight: 400;
                display: block;
            }
        }


        img {
            width: 100%;
        }
    }
}

.banner_bot_box {
    width: 15%;
    position: absolute;
    bottom: 0;

    @media #{$media-700} {
        width: 40%;
    }

    @media #{$media-700} {
        width: 60%;
    }

}



.banner_logo_box {
    width: 37%;
}

.banner_sub_txt_box {
    width: 63%;

}





@keyframes animName {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}


.paralax_block {
    width: 25%;
    margin-bottom: 100px;
    margin-top: 100px;
}





.inner_banner_main {
    min-height: 300px;
    background: linear-gradient(107.56deg,
            rgba(103, 90, 167, 1) 0%,
            rgba(58, 48, 100, 1) 100%);

    h1 {
        color: #fff;
    }
}