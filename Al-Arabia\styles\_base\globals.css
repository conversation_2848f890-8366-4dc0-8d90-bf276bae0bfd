 

/* :root {
  --background: #ffffff;
  --foreground: #171717;
} */

@media (prefers-color-scheme: dark) {
  body {
    background-color: #0a0a0a;
    color: #ededed;
  }
}


body {

  font-family: var(--aller_rg);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #ECECEC;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
  transition: all .4s ease-out 0s;
  -moz-transition: all .4s ease-out 0s;
  -webkit-transition: all .4s ease-out 0s;
  -o-transition: all .4s ease-out 0s;
}

.responsiveImage {
  height: auto;
  max-width: 100%;
  display: block;
}

ul {
  margin: 0;
  padding: 0;
}

ul li {
  list-style: none;
}



@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}





#font_size {
  font-size: 100%;
}

@media (min-resolution: 120dpi) {
  #font_size {
    font-size: 75%;
  }
}

/* @media only screen and (max-width: 1440px) {
  #font_size {
    font-size: 75%;
  }


} */

@media only screen and (max-width: 1024px) {
  #font_size {
    font-size: 70%;
  }


  #container_txt {
    min-height: unset;
  }
}

@media only screen and (max-width: 1000px) {
  #font_size {
    font-size: 60%;
  }
}

@media only screen and (max-width: 700px) {
  #font_size {
    font-size: 70%;
  }


  .svg-container {
    width: 118%;
    left: -11%;
    top: -7%;
  }

  .rtl .svg-container {
    left: 7%;
  }

  /* .rtl  .svg-container{} */
}









@media only screen and (max-width:700px) {

  .menu-ul .main-logo {

    display: none;
  }
}






.swipper_home .swiper-slide {
  position: relative;
  width: var(--swiper-width);
  transform: scale(.84);
  transition: all .3s ease-in-out;
  overflow: hidden;


}

.swipper_home .swiper-slide {
  transform: scale(.84) translateZ(0) translateY(50px) !important;

}

.swipper_home .swiper-slide-active {
  transform: scale(1) !important;
  opacity: 1 !important;
}

.swiper-slide-active {
  transform: scale(1) translateZ(0) !important;
}

.custom-scrollbar .swiper-scrollbar-drag {
  background: #6758B6;
  z-index: 100;
  cursor: pointer;
  transition: all .4s ease-out 0s;
  -moz-transition: all .4s ease-out 0s;
  -webkit-transition: all .4s ease-out 0s;
  -o-transition: all .4s ease-out 0s;
}

.custom-scrollbar .swiper-scrollbar-drag:hover {
  background: #836fe4;
}

.gm-style-iw-d {
  color: #000;
}

.gm-ui-hover-effect {
  outline: none;
  width: 35px !important;
  height: 35px !important;
}

.gm-ui-hover-effect>span {
  padding: 0;
  margin: 0 !important;
}


/* styles/globals.css */
.parallax-container {
  position: relative;
  will-change: transform;
  overflow: hidden;
}


@media (prefers-color-scheme: dark) {
  body{ background: #836fe4;}
}