import React, { useState, useEffect } from "react";
import campaign from "@/styles/campaign.module.scss";
import comon from "@/styles/comon.module.scss";
import Link from "next/link";
import MultiDatePicker from "@/component/MultiDatePicker";
import contactF from "@/styles/contactForm.module.scss";
import Image from "next/image";
import RangeCalendar from "./Calendar";



const CampaignPreview = ({ onNext, darkTheme = false }) => {


  return (
    <>
      <div className={`${comon.w_100} ${comon.d_flex_wrap} ${comon.justify_space_bet} `} >
        <div className={`${campaign.campaign_detail_left} ${comon.pt_20}  ${darkTheme == true ? campaign.dark_theme : ''}`} >

          <div className={`${comon.title_20} ${comon.head_20} ${comon.mb_20}`} >
            <h3>Network Selected</h3>
          </div>

          <ul className={`${campaign.selected_list} ${comon.mb_20}`}>
            <li>Abu Dhabi</li>
            <li>Business Bay</li>
            <li>DIFC Freezone</li>
            <li>DIFC Lift</li>
          </ul>


          <div className={`${comon.title_20} ${comon.head_20} ${comon.mb_20}`} >
            <h3>Dates Selected</h3>
          </div>
          <ul className={`${campaign.selected_list} ${comon.mb_20}`}>
            <li>18 January 2025 to 24 January 2025</li>
          </ul>




          <div className={`${comon.title_20} ${comon.head_20} ${comon.mb_20}`} >
            <h3>Total Days</h3>
          </div>
          <ul className={`${campaign.selected_list} ${comon.mb_40}`}>
            <li>06 Days</li>
          </ul>

        </div>
        <div className={`${campaign.campaign_detail_right} ${darkTheme == true ? campaign.dark_theme : ''}`} >


          <ul
            className={`${contactF.contact_form_main_ul}`}
          >
            <li data-aos="fade-in" data-aos-duration="1000" className={`${contactF.form_li} ${comon.w_100}`}>
              <input
                className={`${contactF.input_fld}`}
                type="text"
                placeholder="Name" ></input>
            </li>

            <li data-aos="fade-in" data-aos-duration="1000" className={`${contactF.form_li} ${comon.w_100}`}>
              <input
                className={`${contactF.input_fld}`}
                type="text"
                placeholder="Email" ></input>
            </li>


            <li data-aos="fade-in" data-aos-duration="1000" className={`${contactF.form_li} ${comon.w_100}`}>
              <input
                className={`${contactF.input_fld}`}
                type="text"
                placeholder="Phone" ></input>
            </li>


            <li data-aos="fade-in" data-aos-duration="1000" className={`${contactF.form_li} ${comon.w_100}`}>
              <input
                className={`${contactF.input_fld}`}
                type="text"
                placeholder="Company" ></input>
            </li>


            <li data-aos="fade-in" data-aos-duration="1000" className={`${contactF.form_li}  ${contactF.form_li_mb_40}     ${comon.w_100}`}>
              <textarea
                className={`${contactF.input_fld} ${contactF.textarea}`}
                placeholder="Message"
              ></textarea>
            </li>



          </ul>
        </div>

      </div>
    </>
  );
};

export default CampaignPreview;
