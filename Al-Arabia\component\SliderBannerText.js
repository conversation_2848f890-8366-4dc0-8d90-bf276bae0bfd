import React, { useState, useEffect } from "react";
import banner from "@/styles/banner.module.scss";
import parse from 'html-react-parser';
import comon from "@/styles/comon.module.scss";
const SliderBannerText = ({
    title,
    paragraph,
    details,
    bgColor = false,
    className = false,
    extra_height_class = false,
    mob_padding_120 = false,
    padding_220 = false,
    full_height_banner = false,
    black_overlay = false
}) => {

    const [isMobile, setIsMobile] = useState(false);
    useEffect(() => {
        const checkScreenSize = () => {
            setIsMobile(window.innerWidth <= 767);
        };
        checkScreenSize();
        window.addEventListener("resize", checkScreenSize);
        return () => {
            window.removeEventListener("resize", checkScreenSize);
        };
    }, []);

    return (
        <section
            className={` ${full_height_banner == true ? banner.full_view_section : ""}  ${banner.slider_banner_main} ${comon.pt_200}  ${bgColor == true ? comon.linear_bg : ""
                } ${extra_height_class == true ? banner.extra_height : ""}  ${padding_220 == true ? comon.pt_220 : ""}  ${mob_padding_120 == true ? banner.mob_pt_150 : ""}`}
        >
            <div className={`${banner.inner_banner_video} ${black_overlay == true && banner.black_gradient}`}>
                {details.video && !details.banner_image_ ? (
                    <video
                        autoPlay
                        muted
                        loop
                        playsInline={true}
                        width="100%"
                        height="auto"
                    >
                        {isMobile ? (
                            <source src={details.video_mobile || details.video.url} type="video/mp4" />
                        ) : (
                            <source src={details.video.url} type="video/mp4" />
                        )}
                        {/* <source src={details.video.url} type="video/mp4" /> */}
                    </video>
                ) : (
                    <Image
                        src={details.banner_image}
                        height={30}
                        width={30}
                        style={{ width: '100%', height: '100%' }}
                        alt=""
                        quality={100}
                    />
                )}
            </div>

            <div className={comon.wrap}>
                <div className={`${banner.slider_head_sec} ${className == "width_870" ? comon.width_870 : ""}`} >
                    <h3>{title}</h3>
                    <p>{paragraph && parse(paragraph)}</p>
                </div>
            </div>
        </section>
    );
};

export default SliderBannerText;
