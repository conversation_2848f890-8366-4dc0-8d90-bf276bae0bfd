import React, { useState, useRef, useEffect } from "react";
import {
  Google<PERSON><PERSON>,
  <PERSON><PERSON>,
  InfoWindow,
  useLoadScript,
} from "@react-google-maps/api";
import style from "@/styles/contactMap.module.scss";
import comon from "@/styles/comon.module.scss";
import rtl from "@/styles/rtl.module.scss";
import Link from "next/link";
import { useRouter } from "next/router";
import Image from "next/image";
import parse from "html-react-parser";
require("dotenv").config();

const mapStyles = [
  {
    featureType: "administrative",
    elementType: "geometry",
    stylers: [{ color: "#7F75C6" }],
  },
  {
    featureType: "administrative",
    elementType: "labels.text.fill",
    stylers: [{ color: "#aaa5d8" }],
    stylers: [{ visibility: "off" }], // Hide water labels
  },
  {
    featureType: "administrative",
    elementType: "labels.text.stroke",
    stylers: [{ color: "#4a4671" }],
    stylers: [{ visibility: "off" }], // Hide water labels
  },
  {
    featureType: "landscape",
    elementType: "all",
    stylers: [{ color: "#7268c0" }],
  },
  {
    featureType: "poi",
    elementType: "all",
    stylers: [{ visibility: "off" }],
  },
  {
    featureType: "road",
    elementType: "all",
    stylers: [{ saturation: -100 }, { lightness: 45 }],
  },
  {
    featureType: "road",
    elementType: "geometry",
    stylers: [{ color: "#7F75C6" }],
  },
  {
    featureType: "road",
    elementType: "labels.icon",
    // stylers: [{ hue: "#7600ff" }, { saturation: "30" }, { lightness: "-17" }],
    stylers: [{ visibility: "off" }],
  },
  {
    featureType: "road",
    elementType: "labels.text",
    stylers: [{ visibility: "off" }], // Hide road labels
  },
  {
    featureType: "transit",
    elementType: "all",
    stylers: [{ visibility: "off" }],
  },
  {
    featureType: "water",
    elementType: "geometry.fill",
    stylers: [{ color: "#6758bb" }],
  },
  {
    featureType: "water",
    elementType: "geometry.stroke",
    stylers: [{ color: "#6758bb" }, { visibility: "on" }],
  },
  {
    featureType: "water",
    elementType: "labels.text",
    stylers: [{ visibility: "off" }], // Hide water labels
  },
];
const containerStyle = {
  width: "100%",
  height: "100%",
};



const ContactmapHover = ({
  textBlockShow = true,
  isHideMoreLocation = false,
  mapDetails,
  locationList,
  textBtn_zoomBtn_hide = false
}) => {

  // console.log("locationlist", locationList)
  const [isMobile, setIsMobile] = useState(false);
  const [isProductShow, setIsProductShow] = useState(false);
  const [scrollWheelEnabled, setScrollWheelEnabled] = useState(false);

  const [isStage, setIsStage] = useState(1);
  const { locale } = useRouter();
  useEffect(() => {
    // Function to check if the screen is mobile size
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 767); // Mobile breakpoint
    };

    // Check screen size on load
    checkScreenSize();

    // Add event listener for resize
    window.addEventListener("resize", checkScreenSize);

    // Cleanup event listener on unmount
    return () => {
      window.removeEventListener("resize", checkScreenSize);
    };
  }, []);

  const goToTopHandler = () => {
    const targetElement = document.getElementById("scrollable-div");
    if (targetElement) {
      // Scroll smoothly to the target element
      targetElement.scrollIntoView({ behavior: "smooth", block: "start" });
    } else {
      // console.log("targetElement not found");
    }
  };

  const { isLoaded } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY,
  });

  // const [selectedCountry, setSelectedCountry] = useState("saudiArabia");
  // const [activeLocations, setActiveLocations] = useState(locations.saudiArabia);
  // const [center, setCenter] = useState(locations.saudiArabia[0].coords);
  // const [zoom, setZoom] = useState(6);

  const [activeInfoWindow, setActiveInfoWindow] = useState(null);
  const mapRef = useRef(null);
  const [selectedCountry, setSelectedCountry] = useState();
  const [activeLocations, setActiveLocations] = useState([]);
  const [activeLocationsBox, setActiveLocationsBox] = useState([]);
  // const [center, setCenter] = useState(initialLocation.saudiArabia);
  const [center, setCenter] = useState({ lat: 25.0, lng: 40.0 });
  const [zoom, setZoom] = useState(4);
  const [countryCode, setCountryCode] = useState(0);

  const [customMarkerIcon, setCustomMarkerIcon] = useState(null);
  const scrollRef = useRef(null);

  useEffect(() => {
    if (mapDetails && locationList) {
      const defaultCountry = locationList[0];
      if (defaultCountry?.country?.name) {
        setSelectedCountry(defaultCountry.country.name);
        setActiveLocationsBox(defaultCountry.cities);
        const defaultCoords = {
          lat: parseFloat(defaultCountry.country.lat),
          lng: parseFloat(defaultCountry.country.long),
        };
        setCenter(defaultCoords);
      }

    }

    //// console.log("lissssssssssssssst", locationList)
  }, [mapDetails]);
  // useEffect(() => {
  //     if (mapDetails && locationList) {
  //       const defaultCountry = locationList[0];
  //       setSelectedCountry(defaultCountry.country);
  //       setActiveLocations(defaultCountry.cities);
  //       const defaultCoords = {
  //         lat: parseFloat(defaultCountry.cities[0].lat),
  //         lng: parseFloat(defaultCountry.cities[0].long),
  //       };
  //       setCenter(defaultCoords);
  //     }
  //   }, [mapDetails]);

  // useEffect(() => {
  //   // Set the initial markers to show all three countries

  const showCountryHandler = () => {
    //alert("t")
    const countryLocations = locationList.map((country) => ({
      name: country.country.name,
      coords: { lat: parseFloat(country.country.lat), lng: parseFloat(country.country.long) },
    }));

    setActiveLocations(countryLocations);
    //setCenter({ lat: 25.0, lng: 40.0 });
    setZoom(4);
    setIsProductShow(false);
    setIsStage(1);
    if (mapRef.current) {
      setActiveLocations(countryLocations);
      setCenter(countryLocations[0].coords);
      mapRef.current.setZoom(4);
      mapRef.current.panTo(countryLocations[0].coords);
    }
  };
  // }, []);
  const handleLocationClick = (id) => {

    setCountryCode(id)
    //// console.log("data", data);
    //// console.log("locations", locations[data]);
    const country = locationList[id];
    if (country) {
      const countries = country.cities.map((city) => ({
        name: city.city_name,
        coords: { lat: parseFloat(city.lat), lng: parseFloat(city.long) },
      }));

      if (id != undefined) {
        setActiveLocations(countries);

        setCenter(countries[0].coords);
        setZoom(7);

        setActiveInfoWindow(countries);
      }

      if (isProductShow == false) {
        setIsProductShow(true);
        mapRef.current.setZoom(7);
        mapRef.current.panTo(countries[0].coords);
        setIsStage(isStage + 1);
      }
    }
  };
  useEffect(() => {
    if (isLoaded && typeof window !== "undefined" && window.google) {
      setCustomMarkerIcon({
        // url: "/images/location_pin_1.svg",
        url: "/images/location_pin1.svg",
        scaledSize: new window.google.maps.Size(60, 60),
        origin: new window.google.maps.Point(0, 0),
        anchor: new window.google.maps.Point(20, 20),
      });

      // if (isStage >= 3) {
      //   setCustomMarkerIcon({
      //     url: "/images/location_pin_1.svg",

      //     // url: "/images/map_popup.png",
      //     scaledSize: new window.google.maps.Size(50, 50),
      //   });
      // }
    }
  }, [isLoaded, isStage]);
  // const handleCountryClick = (country) => {
  //   setSelectedCountry(country);
  //   const countryLocations = locations[country];
  //   setActiveLocations(countryLocations);

  //   const firstLocation = countryLocations[0].coords;
  //   animateMapTo(firstLocation, 6);
  //   setActiveInfoWindow(null);
  // };

  const handleCountryClick = (id) => {
    const country = locationList[id];
    setCountryCode(id)
    if (!country) return;
    // setCountryCode(id)
    // Update the selected country name
    setSelectedCountry(country.country.name);

    // Map city data to coordinates and names
    const countries = country.cities.map((city) => ({
      name: city.city_name,
      coords: { lat: parseFloat(city.lat), lng: parseFloat(city.long) },
    }));
    if (country) {
      // Update map-related states
      setActiveLocations(countries);
      setActiveLocationsBox(country.cities);
      setIsStage(2);

      setCenter(countries[0].coords);
      mapRef.current.setZoom(7);
      mapRef.current.panTo(countries[0].coords);
    }
    // Adjust the map view to fit all the cities in the selected country
    // if (mapRef.current) {
    //   const bounds = new window.google.maps.LatLngBounds();
    //   countries.forEach((city) => bounds.extend(city.coords));
    //   mapRef.current.fitBounds(bounds);
    // }
  };


  // const handleLocationClick = (location) => {
  //   animateMapTo(location.coords, 14);
  //   setActiveInfoWindow(location.name);
  // };

  // console.log("setIsStage", isStage);

  const animateMapTo = (coords, zoomLevel) => {
    if (mapRef.current) {
      mapRef.current.panTo(coords);
      mapRef.current.setZoom(zoomLevel);
    }
    setCenter(coords);
    setZoom(zoomLevel);
  };

  const productDetailShow = (cid) => {
    // console.log("locaaaaaaaaaationclick,", cid)
    // console.log(countryCode);
    //alert("product")
    const country = locationList[countryCode];
    if (cid == undefined) {
      alert("City product needs to be added.");
      return;
    }
    setIsStage(3);
    if (country?.cities[cid]?.co_ords) {
      const citycords = country.cities[cid].co_ords.map((pro) => ({
        name: pro.name,
        coords: { lat: parseFloat(pro.lat), lng: parseFloat(pro.long) },
        icon: pro.icon
      }));

      if (country) {
        setActiveLocations(citycords);
        setCenter(citycords[0].coords);
        //setZoom(15);
        setActiveInfoWindow(citycords);
        mapRef.current.setZoom(13);
        mapRef.current.panTo(citycords[0].coords);
      }
    }
  };


  const handleLocationBox = (cid) => {
    // console.log("locaaaaaaaaaationclick,", cid)
    // console.log(countryCode);
    const country = locationList[countryCode];
    if (cid == undefined) {
      alert("City product needs to be added.");
      return;
    }
    setIsStage(3);
    if (country?.cities[cid]?.co_ords) {
      const countries = country.cities[cid].co_ords.map((pro) => ({
        name: pro.name,
        coords: { lat: parseFloat(pro.lat), lng: parseFloat(pro.long) },
        icon: pro.icon
      }));

      if (country) {
        setActiveLocations(countries);
        //setCenter(countries.coords);
        setCenter(countries[0].coords);

        //setZoom(15);
        setActiveInfoWindow(countries);
        mapRef.current.setZoom(15);
        mapRef.current.panTo(countries[0].coords);
      }
    }
  };



  // const showCountryHandlerBox = (data) => {

  //   setIsStage(2);


  //   if (data != undefined) {
  //     setActiveLocations(locations[data]);

  //     setCenter(data.coords);
  //     // setZoom(12);
  //     setActiveInfoWindow(locations[data]);
  //     mapRef.current.setZoom(6.5);
  //   }

  //   if (isProductShow == false) {
  //     setIsProductShow(true);
  //     mapRef.current.setZoom(6);
  //     mapRef.current.panTo(locations[data][0].coords);
  //   }


  // }


  const [options, setOptions] = useState(null);
  useEffect(() => {
    const fetchOptions = async () => {
      try {
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/option-field/location_details_titles?lang=${locale}`
        );
        const data = await res.json();
        setOptions(data);
      } catch (error) {
        console.error("Error fetching options:", error);
      }
    };

    fetchOptions();
  }, [locale]);

  useEffect(() => {
    if (locationList) {
      showCountryHandler();
    }
  }, [locationList]);

  if (!isLoaded) return <div>Loading...</div>;

  return (
    <div className={style.mapContainer}>


      {/* <div className={`${comon.zoom_btn_sec} ${comon.main_page}`}>
        <button onClick={() => { if (zoom >= 4) { setZoom(zoom + 2) } }}>
          <div className={comon.zoom_img}>

            <Image src={'/images/zoom-in.svg'} height={30} width={30} alt="" />
          </div>
        </button>
        <button onClick={() => { if (zoom > 4) { setZoom(zoom - 2) } }}>
          <div className={comon.zoom_img}>

            <Image src={'/images/zoom-out.svg'} height={30} width={30} alt="" />
          </div>
        </button>
      </div> */}
      <div className={`${comon.zoom_btn_sec} ${comon.main_page} ${textBtn_zoomBtn_hide == true && style.textBtn_zoomBtn_hide}`}>
        <button
          onClick={() => {
            if (mapRef.current) {
              const currentZoom = mapRef.current.getZoom();
              mapRef.current.setZoom(currentZoom + 1);
            }
          }}
        >
          <div className={comon.zoom_img}>
            <Image src="/images/zoom-in.svg" height={30} width={30} alt="Zoom In" />
          </div>
        </button>

        <button
          onClick={() => {
            if (mapRef.current) {
              const currentZoom = mapRef.current.getZoom();
              mapRef.current.setZoom(currentZoom - 1);
            }
          }}
        >
          <div className={comon.zoom_img}>
            <Image src="/images/zoom-out.svg" height={30} width={30} alt="Zoom Out" />
          </div>
        </button>
      </div>

      <div className={`${comon.p_relative} ${style.map_block_03}`}>

        <div className={style.map_absolute}>

          <div className={`${style.map_wrap_02} ${comon.p_relative}`}>
            {textBlockShow == true ? (
              <div className={`${style.map_txt_block} ${textBtn_zoomBtn_hide == true && style.textBtn_zoomBtn_hide} ${rtl.map_txt_block}`}>
                {options && (
                  <div>
                    <h3 data-aos="fade-in" data-aos-duration="1000">
                      {parse(options.title)}
                    </h3>
                    <div
                      data-aos="fade-in"
                      data-aos-delay="300"
                      data-aos-duration="1000"
                    >
                      {options.button && (
                        <span
                          className={`${comon.buttion} ${comon.but_blue}  ${comon.but_white}   ${comon.mt_30}`}
                          onClick={showCountryHandler}
                        >
                          {options.button.title}
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              ""
            )}

            <div className={`${style.map_right_block} ${rtl.map_right_block}`}>
              <h5  >
                {options && parse(options.location_list_title)}
              </h5>
              <div className={style.buttonContainer}>
                <ul
                  className={`${style.tab_ul} stylSCroll`}
                   
                // style={{display:'flex', flexWrap:'nowrap', overflowX:'auto' }}
                >
                  {locationList.map((country, cindex) => {
                    return (
                      <li
                        key={cindex}
                        className={
                          selectedCountry === country.country.name ? style.active : ""
                        }
                        onClick={() => handleCountryClick(cindex)}
                      // style={{whiteSpace:'nowrap'}}
                      >
                        {country.country.name}
                      </li>
                    );
                  })}


                </ul>
              </div>


              <ul
                className={`${style.locationList} ${rtl.locationList}`}
                data-lenis-prevent="true"
              >
                {activeLocationsBox.map((location, index3) => (
                  <li
                    key={location.city_name}
                    onClick={() => {
                      handleLocationBox(index3);
                      // goToTopHandler();
                    }}
                  // data-aos="fade-in"
                  // data-aos-delay={index * 200}
                  // data-aos-duration="1000"
                  >
                    {location.city_name}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
        <div
          className={`${comon.p_relative} location_popup_hover ${comon.h_100} ${style.map_block_04}`}
          ref={scrollRef}
          id="scrollable-div"
        >
          <GoogleMap
            mapContainerStyle={containerStyle}
            center={center}
            zoom={zoom}
            options={{
              styles: mapStyles,
              disableDefaultUI: true,
              gestureHandling: 'greedy',
              scrollwheel: scrollWheelEnabled,
              keyboardShortcuts: true
            }}
            onLoad={(map) => (mapRef.current = map)}
            onClick={() => setScrollWheelEnabled(true)}
          >
            {activeLocations.map((location, index2) => (
              <>
                {isStage < 2 ? (
                  <>
                    {isMobile == false ? (
                      <Marker
                        key={location.name}
                        position={location.coords}
                        icon={customMarkerIcon}
                        onMouseOver={() => setActiveInfoWindow(location.coords)}
                        onMouseOut={() => setActiveInfoWindow(null)}
                        onClick={() => handleLocationClick(index2)}
                      >
                        {activeInfoWindow === location.coords && ( // Always show on mobile
                          <InfoWindow>
                            <div className="location_popup_img">
                              <h5>{location.name && parse(location.name)}</h5>
                            </div>
                          </InfoWindow>
                        )}
                      </Marker>
                    ) : (
                      <Marker
                        key={location.name}
                        position={location.coords}
                        icon={customMarkerIcon}
                        onMouseOver={() => setActiveInfoWindow(location.name)}
                        onMouseOut={() => setActiveInfoWindow(null)}
                        onClick={() => handleLocationClick(index2)}
                        className="location_product_sec">


                        <InfoWindow position={location.coords}>
                          <div className="location_popup_img">
                            <h5>{location.name && parse(location.name)}</h5>
                          </div>
                        </InfoWindow>
                      </Marker>
                    )}{" "}
                  </>
                ) : (
                  <>
                    {isMobile == false ? (
                      <Marker
                        key={location.name}
                        position={location.coords}
                        icon={customMarkerIcon}
                        onMouseOver={() => setActiveInfoWindow(location.coords)}
                        onMouseOut={() => setActiveInfoWindow(null)}
                        {...(isStage < 3
                          ? {
                            onClick: () => {
                              productDetailShow(index2);
                              setActiveInfoWindow(location.coords);
                            },
                          }
                          : {
                            onClick: null, // explicitly disable clicking in stage 3
                          })}
                      >
                        {activeInfoWindow === location.coords && (
                          <InfoWindow
                            className="location_sec "
                          >
                            <div className="location_popup_img img_sec ">
                              {isStage == 3 &&

                                <div className={`popup_icon ${style.product_pin_logo}`}>
                                  {location.icon && (
                                    <Image src={location.icon} height={100} width={100} alt=""
                                      quality={100}
                                    />
                                  )}
                                </div>

                              }
                              <h5>
                                {location.name && parse(location.name)}
                              </h5>
                            </div>
                          </InfoWindow>
                        )}
                      </Marker>

                    ) : (
                      <Marker
                        key={location.name}
                        position={location.coords}
                        icon={customMarkerIcon}
                        onMouseOver={() => setActiveInfoWindow(location.coords)}
                        onMouseOut={() => setActiveInfoWindow(null)}
                        onClick={() => {
                          productDetailShow(index2),
                            setActiveInfoWindow(location.coords);
                        }}
                      >
                        {activeInfoWindow === location.coords && (
                          <InfoWindow position={location.coords}>
                            <div className="location_popup_img">
                              {isStage == 3 &&

                                <div className="popup_icon ">
                                  <Image src={location.productImage} height={30} width={30} alt=""
                                    quality={100}
                                  />
                                </div>

                              }
                              <h5>{location.name}</h5>
                            </div>
                          </InfoWindow>
                        )}
                      </Marker>
                    )}
                  </>
                )}
              </>
            ))}
          </GoogleMap>
        </div>
      </div>
    </div>

  );
};

export default ContactmapHover;
