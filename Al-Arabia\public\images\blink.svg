<svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
  <style type="text/css">
    .st0 {
      opacity: 0.6;
      fill: none;
      stroke:rgba(226, 223, 237, 0.79);
      stroke-width:1;
    }
    .st1 {
      opacity: 0.6;
      fill: none;
      stroke:rgba(226, 223, 237, 0.62);
      stroke-width:5;
    }

    /* Animation for blinking effect */
    circle {
      animation: blink 2s infinite;
    }

    .circle-inner {
      animation-delay: .3s; /* Inner circle starts with a 1s delay */
    }

    @keyframes blink {
   0% { opacity: 1; r: 15; } /* Initial radius */
      50% { opacity: 0.5; r: 7; }  /* Shrink and fade */
      100% { opacity: 1; r: 15; } /* Restore radius */
    }
  </style>
  
  <circle class="circle-outer st0" cx="27" cy="27" r="15" />
  <circle class="circle-inner st1" cx="27" cy="27" r="10" />
</svg>
