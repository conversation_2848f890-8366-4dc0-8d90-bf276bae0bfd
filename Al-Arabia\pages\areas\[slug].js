import React from 'react'
import Dubai from '@/component/areaTemplates/dubai'
import { fetchByPost } from "@/lib/api/pageApi";
import <PERSON>jec<PERSON>ulevard from '@/component/areaTemplates/boulevard';
import Projectmezah from '@/component/areaTemplates/projectmezah';
import Projectmaalem from '@/component/areaTemplates/projectmaalem';
import Airports from '@/component/areaTemplates/airports';
import KingKhaledInternationalAirport from '@/component/areaTemplates/king-khaled-international-airport';
import Altanfeethi from '@/component/areaTemplates/altanfeethi';
import Terminals from '@/component/areaTemplates/terminals';
import Accessroad from '@/component/areaTemplates/access-road';
import Tuwaiq from '@/component/areaTemplates/tuwaiq';
import ComingSoon from '@/component/areaTemplates/comingsoon';

const index = ({pageData}) => {

  return (
    <>
      {pageData.template == "dubai.php" ? (        
        <Dubai pageData={pageData} />
      ): pageData.template == "boulevard.php" ? (
        <Projecboulevard pageData={pageData}  />
      ): pageData.template == "smartriyad1.php" ? (
        <Projectmezah pageData={pageData} />
      ): pageData.template == "smartriyad2.php" ?(
        <Projectmaalem pageData={pageData} />
      ): pageData.template == "airportMain.php" ?(
        <Airports pageData={pageData} />
      ): pageData.template === "airport1.php" ? (
        <KingKhaledInternationalAirport pageData={pageData} />
      ) : pageData.template === "airport2.php" ? (
        <Altanfeethi pageData={pageData}  />
      ) : pageData.template === "airportAllterminal.php" ? (
        <Terminals pageData={pageData} />
      ) : pageData.template === "template2.php" ? (
        <Accessroad pageData={pageData} />
      ) : pageData.template === "tuwaiq.php" ? (
        <Tuwaiq pageData={pageData} />
      ): pageData.template === "coming-soon.php" ? (
        <ComingSoon pageData={pageData}  />
      ) : (
        <h2> NO Page Found</h2>
      )

    
  }
        
    </>
  )
}

export default index

export async function getServerSideProps(context) {

    const { params, locale } = context;  
    const slug = params.slug; 
    const langCode = locale === "ar" ? "ar" : "en";

    try {
      const pageData = await fetchByPost('areas',slug,langCode); 
      if (!pageData) {
        return { notFound: true }; 
    }

      return {
        props: {
            pageData
        },
      };
    } catch (error) {
      console.error('Error fetching data:', error);
      return {
        props: {
            pageData: null, 
        },
      };
    }
  }