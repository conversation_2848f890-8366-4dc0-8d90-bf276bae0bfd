import React, { useEffect, useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import comon from "@/styles/comon.module.scss";
  import CssFilterConverter from 'css-filter-converter';
import "swiper/css/effect-coverflow";
import "swiper/css/pagination";
import project from "@/styles/project.module.scss";
import { useRouter } from "next/router";
import {
    Autoplay,
    EffectCoverflow,
    Navigation,
    Pagination,
} from "swiper/modules";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";

import gsap from "gsap";
import ScrollTrigger from "gsap/dist/ScrollTrigger";
import Link from "next/link";

gsap.registerPlugin(ScrollTrigger);

const SliderAnimDubai = ({ images, ImageSrc, className, bgColor, bgColor2, textColor, singlecolor, arrowColor, gradient }) => {
    const [activeIndex, setActiveIndex] = useState(0);


    const [HeadName, setHeadname] = useState(images[0].description);
    // console.log('HeadName', SliderHead[activeIndex]);


    const sliderCount = (swiper) => {
        // const activeIndex = swiper.activeIndex;
        setHeadname(images[activeIndex].description);
    };


    const getActiveImagePath = () => {
        if (images[activeIndex]) {
            return images[activeIndex].img;
        }
        return "";
    };

    // ---working gsap---
    // useEffect(() => {
    //     const imgContainer = document.getElementById("activeImage");
    //     const containerBody = document.getElementById("activeImageSection");

    //     if (imgContainer && containerBody) {
    //         // Ensure the image starts with the correct transform and scale
    //         gsap.set("#activeImage", {
    //             // scale: 10,
    //             scale: 7,
    //             opacity: 1,
    //             bottom: "45%",
    //             transform: "translate(-50%, -50%)",
    //         });

    //         // Create the ScrollTrigger-based animation
    //         gsap.to("#activeImage", {
    //             scale: 1,
    //             ease: "power1.out",
    //             duration: 2,
    //             bottom: "0%",

    //             scrollTrigger: {
    //                 trigger: "#activeImageSection", // The element to watch
    //                 start: "top 30%", // Start when the top of the container touches the top of the viewport
    //                 end: "bottom bottom", // Optional: define the end of the animation if needed
    //                 toggleActions: "play none none none", // Only play the animation once
    //             },
    //             onComplete: () => {
    //                 // Reduce opacity over 0.3s
    //                 gsap.to("#activeImage", { opacity: 0, duration: 0.3 });
    //             },
    //         });
    //     }
    // }, []);

    useEffect(() => {
        const mm = gsap.matchMedia(); // Initialize GSAP media queries

        mm.add("  (min-width: 1601px)     ", () => {
            // Desktop animation
            gsap.set("#activeImage", {
                scale: 7,
                opacity: 1,
                bottom: "45%",
                transform: "translate(-75%, -295px) skewY(358deg)",
            });

            // translate(-46%, -225%) translate(0.004px, 0.004px) scale(7) skewY(358deg)

            gsap.to("#activeImage", {
                scale: 1,
                ease: "power1.out",
                duration: 1.5,
                bottom: "0%",
                transform: "translate(-50%, -208px)",

                scrollTrigger: {
                    trigger: "#activeImageSection",
                    start: "top 60%",
                    end: "bottom bottom",
                    toggleActions: "play none none none",
                },
                onComplete: () => {
                    gsap.to("#activeImage", { opacity: 0, duration: 0.3 });
                },
            });
        });
        mm.add("(max-width: 1600px) and (min-width: 1441px)     ", () => {
            // Desktop animation
            gsap.set("#activeImage", {
                scale: 5.5,
                opacity: 1,
                bottom: "45%",
                transform: "translate(-75%, -305px) skewY(358deg)",
            });

            // translate(-46%, -225%) translate(0.004px, 0.004px) scale(7) skewY(358deg)

            gsap.to("#activeImage", {
                scale: 1,
                ease: "power1.out",
                duration: 1.5,
                bottom: "0%",
                transform: "translate(-50%, -208px)",

                scrollTrigger: {
                    trigger: "#activeImageSection",
                    start: "top 60%",
                    end: "bottom bottom",
                    toggleActions: "play none none none",
                },
                onComplete: () => {
                    gsap.to("#activeImage", { opacity: 0, duration: 0.3 });
                },
            });
        });
        mm.add(" (max-width: 1440px) and (min-width: 1101px)", () => {
            // Desktop animation
            gsap.set("#activeImage", {
                scale: 5,
                opacity: 1,
                bottom: "45%",
                transform: "translate(-60%, -300px) skewY(358deg)",
            });

            // translate(-46%, -225%) translate(0.004px, 0.004px) scale(7) skewY(358deg)

            gsap.to("#activeImage", {
                scale: 1,
                ease: "power1.out",
                duration: 1.5,
                bottom: "0%",
                transform: "translate(-50%, -208px)",

                scrollTrigger: {
                    trigger: "#activeImageSection",
                    start: "top 60%",
                    end: "bottom bottom",
                    toggleActions: "play none none none",
                },
                onComplete: () => {
                    gsap.to("#activeImage", { opacity: 0, duration: 0.3 });
                },
            });
        });
        mm.add("(max-width: 1100px)", () => {
            // Desktop animation
            gsap.set("#activeImage", {
                scale: 6,
                opacity: 1,
                y: "240%",
                transform: "translate(-50%, -50%)",
            });

            gsap.to("#activeImage", {
                scale: 1,
                ease: "power1.out",
                duration: 1.5,
                y: "50.8%",
                bottom: "0%",
                scrollTrigger: {
                    trigger: "#activeImageSection",
                    start: "top 60%",
                    end: "bottom bottom",
                    toggleActions: "play none none none",
                },
                onComplete: () => {
                    gsap.to("#activeImage", { opacity: 0, duration: 0.3 });
                },
            });
        });
        mm.add("(max-width: 1024px)", () => {
            // Desktop animation
            gsap.set("#activeImage", {
                scale: 6,
                opacity: 1,
                y: "250%",
                transform: "translate(-50%, -50%)",
            });

            gsap.to("#activeImage", {
                scale: 1,
                ease: "power1.out",
                duration: 1.5,
                y: "50.8%",
                bottom: "0%",
                scrollTrigger: {
                    trigger: "#activeImageSection",
                    start: "top 60%",
                    end: "bottom bottom",
                    toggleActions: "play none none none",
                },
                onComplete: () => {
                    gsap.to("#activeImage", { opacity: 0, duration: 0.3 });
                },
            });
        });
        mm.add("(max-width: 1020px) and (min-width: 820px)", () => {
            gsap.set("#activeImage", {
                scale: 6,
                opacity: 1,
                y: "192%",
                transform: "translate(-56%, -56%) skewY(358deg)",

            });

            gsap.to("#activeImage", {
                scale: 1,
                ease: "power1.out",
                duration: 1.5,
                y: "50.8%",
                bottom: "0%",
                transform: "translate(-50%, -50%)",

                scrollTrigger: {
                    trigger: "#activeImageSection",
                    start: "top 60%",
                    end: "bottom bottom",
                    toggleActions: "play none none none",
                },
                onComplete: () => {
                    gsap.to("#activeImage", { opacity: 0, duration: 0.3 });
                },
            });
        });

        mm.add("(max-width: 819px)", () => {
            // Desktop animation
            gsap.set("#activeImage", {
                scale: 6,
                opacity: 1,
                y: "0%",
                transform: "translate(0%, 0%)",
            });

            gsap.to("#activeImage", {
                scale: 1,
                ease: "power1.out",
                duration: 1.5,
                y: "0%",
                bottom: "0%",
                scrollTrigger: {
                    trigger: "#activeImageSection",
                    start: "top 30%",
                    end: "bottom bottom",
                    toggleActions: "play none none none",
                },
            });
        });

        return () => mm.revert(); // Cleanup on unmount
    }, []);

    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
        // Function to check if the screen is mobile size
        const checkScreenSize = () => {
            setIsMobile(window.innerWidth <= 819); // Mobile breakpoint
        };

        // Check screen size on load
        checkScreenSize();

        // Add event listener for resize
        window.addEventListener("resize", checkScreenSize);

        // Cleanup event listener on unmount
        return () => {
            window.removeEventListener("resize", checkScreenSize);
        };
    }, []);

    useEffect(() => {
        const mm = gsap.matchMedia(); // GSAP media query system

        mm.add("(min-width: 1280px)", () => {
            // Runs only if screen width is >= 820px
            gsap.set(".slider_anim_new", {
                y: 150,
                scale: 0.9,
            });

            gsap.to(".slider_anim_new", {
                y: 0,
                scale: 1,

                ease: "power1.out",
                duration: 1.5,
                scrollTrigger: {
                    trigger: "#activeImageSection",
                    start: "top 60%",
                    end: "bottom bottom",
                    toggleActions: "play none none none",
                },

            });
        });

        return () => mm.revert(); // Cleanup on unmount
    }, []);

    const router = useRouter();
    const swiperRef = useRef()

    const extendedImages = [...images, ...images, ...images];
    const [rgbaColor, setRgbaColor] = useState("");
    const [rgbAlphaColor, setRgbAlphaColor] = useState(""); // for rgb(... / 10%) color

    useEffect(() => {
        const x = gradient ? bgColor2 : bgColor;
    if (x) {
        // Convert rgb(...) to rgba(..., 0.911)
        const rgba = x.replace("rgb", "rgba").replace(")", ", 0.911)");
        setRgbaColor(rgba);
    }

    if (singlecolor) {
        // Convert rgb(...) to rgb(... / 10%)
        const rgbValues = singlecolor.match(/\d+/g);
        if (rgbValues && rgbValues.length >= 3) {
        const rgbWithSpaces = rgbValues.join(" ");
        const rgbAlpha = `rgb(${rgbWithSpaces} / 10%)`;
        
        setRgbAlphaColor(rgbAlpha);
        }
    }
    // if(arrowColor){

    // }
    }, [bgColor, singlecolor, arrowColor, bgColor2]);

    function rgbSlashToRgba(rgbSlash, alpha = 0.7) {
        const rgbValues = rgbSlash.match(/\d+/g); // extract numbers
        if (!rgbValues || rgbValues.length < 3) return rgbSlash;
        const [r, g, b] = rgbValues;
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
        }
        
        const result = CssFilterConverter.hexToFilter(arrowColor);
       // console.log(result.color); 

      const [gradientBackground, setGradientBackground] = useState("");
        const [solidBackground, setSolidBackground] = useState("");

        useEffect(() => {
        if (gradient === "True") {
            const color1 = bgColor ;
            const color2 = bgColor2 ;

            // Extract alpha values from both rgba colors
            const alpha1 = parseFloat(color1.match(/rgba?\([\d\s]*,[\d\s]*,[\d\s]*,([\d.]+)\)/)?.[1] || "0");
            const alpha2 = parseFloat(color2.match(/rgba?\([\d\s]*,[\d\s]*,[\d\s]*,([\d.]+)\)/)?.[1] || "0");

            // Convert alpha to percentages
            const percent1 = Math.round(alpha1 * 100) + "%";
            const percent2 = Math.round(alpha2 * 100) + "%";

            const gradient = `linear-gradient(107.56deg, ${color1} 0%, ${color2} 57%)`;
            setGradientBackground(gradient);
        } else {
            setSolidBackground(bgColor ); // fallback
        }
        }, [bgColor, bgColor2, gradient]);


    return (
        <>
      
        <div
        style={{
            background: gradient === "True" ? gradientBackground : solidBackground,
        }}
        >
            {isMobile == false ? (
                <div className="h_100">

                    <div className="overlap_image dubai_page" id="activeImage">
                        <Image
                            src={getActiveImagePath()}
                            alt="slider1"
                            width={500}
                            height={500}
                            quality={100}
                            loading="lazy"
                        />
                    </div>


                    <div className={`${comon.wrap}     `} id="activeImageSection">
                        <div className="slidernew1 dubai">
                            <Swiper
                                centeredSlides={true}
                                slidesPerView={5}
                                onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
                                loop={true}
                                spaceBetween={20}
                                coverflowEffect={{
                                    rotate: 30,
                                    stretch: 0,
                                    depth: 200,
                                    modifier: 1,
                                    slideShadows: true,
                                }}
                                onSwiper={(swiper) => (swiperRef.current = swiper)}

                                navigation={{
                                    nextEl: ".swiper-button.next",
                                    prevEl: ".swiper-button.prev",
                                }}
                                dir={router.locale == "ar" || router.locale == "rtl" ? "rtl" : "ltr"}
                                pagination={{ clickable: true }}
                                className={`slider_anim_new dubai_swiper dubai_swiper_new  ${className == "maleem" ? "maleem_border" : ""
                                    }`}
                                modules={[Navigation, Autoplay, EffectCoverflow]}
                            >
                                {extendedImages.map((data, index) => (

                                    <SwiperSlide onClick={() => swiperRef.current?.slideToLoop(index)}
                                        key={index} className="fade-up-img">
                                        <div className={`slider_anim_new_img  dubai_page_img `}>
                                            <Image
                                                src={data.img}
                                                alt={`slider-${index + 1}`}
                                                width={500}
                                                height={500}
                                                quality={100}
                                            />
                                        </div>
                                    </SwiperSlide>


                                ))}

                            </Swiper>

                            <div className={`slider_anim_new_btn dubai_btn dubai project_slider_btn`}>
                                <button className="swiper-button prev"
                               style={result.color ? { filter: result.color } : {}}

                                ></button>
                                <button className="swiper-button next"
                               style={result.color ? { filter: result.color } : {}}

                                ></button>
                            </div>
                            {/* {SliderHead.length !== 0 && ( */}
                            <div className="slider_heading dubai dubai_page">
                                {/* <h4>{HeadName}</h4> */}
                                {/* <h4>{SliderHead[activeIndex]}</h4> */}


                               {extendedImages[activeIndex]?.condition == 'Text' && (
                                    <h4 style={{color:textColor}}>{extendedImages[activeIndex]?.description}</h4>
                                )}
                                {extendedImages[activeIndex]?.condition == 'Image' && (
                                    <img src={extendedImages[activeIndex]?.titleimg} 
                                     style={{maxWidth:'150px', display:'block', marginBottom:'-15px'}}
                                    />
                                )}

                            </div>
                            {/* )} */}
                        </div>
                    </div>
                </div >
            ) : (
                <div className={`${comon.pt_60} ${comon.pb_60}`}>
                    <Swiper
                        centeredSlides={true}
                        slidesPerView={1}
                        onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)} // `realIndex` is more accurate for looped swipers
                        loop={true}
                        spaceBetween={20}
                        coverflowEffect={{
                            rotate: 30,
                            stretch: 0,
                            depth: 200,
                            modifier: 1,
                            slideShadows: true,
                        }}
                        navigation={{
                            nextEl: ".swiper-button.next",
                            prevEl: ".swiper-button.prev",
                        }}
                        breakpoints={{
                            600: {
                                slidesPerView: 2.5
                            }
                        }}
                        pagination={{ clickable: true }}
                        modules={[Navigation, Autoplay, EffectCoverflow]}
                        className="slider_anim dubai_mobile_swiper dubai"
                    >
                        {extendedImages.map((data, index) => (
                            <SwiperSlide key={index}>
                                <div className="slider_anim_img">
                                    <Image
                                        src={data.img}
                                        alt={`slider-${index + 1}`}
                                        width={500}
                                        height={500}
                                        quality={100}
                                    />
                                </div>
                            </SwiperSlide>
                        ))}
                    </Swiper>

                    <div className={`slider_anim_new_btn dubai project_slider_btn`}>
                        <button className="swiper-button prev"
                       style={result.color ? { filter: result.color } : {}}

                        ></button>
                        <button className="swiper-button next"
                       style={result.color ? { filter: result.color } : {}}

                        ></button>
                    </div>
                    {/* {SliderHead.length !== 0 && (
                        <div className="slider_anim_heading dubai_page">
                            <h4>{HeadName}</h4>
                           
                        </div>
                    )} */}

                    <div className="slider_anim_heading dubai_page">

                       {extendedImages[activeIndex]?.condition == 'Text' && (
                            <h4 style={{color:textColor}}>{extendedImages[activeIndex]?.description}</h4>
                        )}
                        {extendedImages[activeIndex]?.condition == 'Image' && (
                            <img src={extendedImages[activeIndex]?.titleimg} 
                            style={{maxWidth:'150px', display:'block',marginLeft:'auto',marginRight:'auto', marginBottom:'-15px'}}
                            />
                        )}


                    </div>



                </div>
            )}
        </div>
           {rgbaColor && (
            <style jsx>{`
                :global(.slider_anim_new.dubai_swiper .swiper-slide-active .slider_anim_new_img::after) {
                background: linear-gradient(to top, ${rgbaColor}, rgba(0, 128, 0, 0));
                }
            `}</style>
            )}

            {rgbAlphaColor && (
            <style jsx>{`
                 :global(.swiper-slide.swiper-slide-active.fade-up-img) {
                    background: transparent !important;
                    border:unset !important;
                }
                :global(.slider_anim_new .swiper-slide) {
                    background-color: ${rgbAlphaColor} !important;
                    border: 1px solid ${rgbSlashToRgba(rgbAlphaColor, 0.7)};
                }
            `}</style>
            )}

        </>
    );
};

export default SliderAnimDubai;
