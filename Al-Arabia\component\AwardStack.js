import React, { useEffect, useState } from "react";

import comon from "@/styles/comon.module.scss";
import style from "@/styles/AwardSlide.module.scss";
import Image from "next/image";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";


const AwardStack = () => {
    const slides = [
        {
            backgroundImage: "/images/bg_csr11.jpg",
            title: "AWARD Winning",
            subtitle: "AlArabia Gift Campaign",
            imageSrc: "/images/card_img.jpg",
            description:
                "On the 90th Saudi National Day, AlArabia launched a campaign across the country to support 90 startup businesses to overcome the limitations caused by Covid-19. AlArabia allocated 18 million Saudi Riyal to be distributed among the 90 winners.",
        },
        {
            backgroundImage: "/images/bg_csr1.jpg",
            title: "AWARD Winning",
            subtitle: "Biggest Art Gallery in The World",
            imageSrc: "/images/bg_csr1.jpg",
            description:
                "AlArabia launched the biggest art gallery in the world on its billboards across the country to support young Saudi artists. The campaign helped raise awareness of hundreds of Saudi artists and thousands of artworks. It won six international awards and created international buzz.",
        },
        {
            backgroundImage: "/images/bg_csr2.jpg",
            title: "AWARD Winning",
            subtitle: "The Campaign Won 5 International Awards",
            imageSrc: "/images/bg_csr2.jpg",
            description:
                "BE A MAN Prostate cancer has been steadily increasing in KSA as men avoid getting themselves checked. Therefore, AlArabia redefined manhood through a CSR campaign to encourage men to get themselves checked. The 360-campaign revealed that manhood means being responsible towards one’s family and health. It created buzz and raised awareness which led to a significant increase in early checkup numbers. The campaign caught the attention of the Kingdom’s Ministry of Health in which they offered their support.",
        },
    ];

    useEffect(() => {
        // Register the ScrollTrigger plugin
        gsap.registerPlugin(ScrollTrigger);

        const cards = gsap.utils.toArray(".card");


        const spacer = 0; // Space between pinned elements
        const minScale = 1; // Minimum scale for cards

        const distributor = gsap.utils.distribute({ base: minScale, amount: 0.2 });

        console.log('cards.length * spacer', cards.length * spacer);

        // Loop through each card and apply animations
        cards.forEach((card, index) => {
            const scaleVal = distributor(index, cards[index], cards);

            // Scaling animation
            gsap.to(card, {
                scrollTrigger: {
                    trigger: card,
                    start: `top top`,
                    scrub: true,
                    markers: false, // Debugging markers
                    invalidateOnRefresh: true,
                },
                ease: "none",
                scale: 1,
            });

            // Pinning configuration
            ScrollTrigger.create({
                trigger: card,
                start: `top-=${index * spacer} top`,
                endTrigger: ".cards",
                // end: `bottom center+=${200 + cards.length * spacer}`,
                end: `bottom bottom`,
                pin: true,
                pinSpacing: false,
                markers: false, // Debugging markers
                id: "pin",
                invalidateOnRefresh: true,
            });
        });

        // Cleanup on unmount
        return () => {
            ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
        };
    }, []);

    return (


        <div
            className={` ${style.card_container}`} >
            <ul className="cards " >

                {slides && slides.map((slide, index) => (
                    <li key={index} className="card">
                        <div
                            className={`${comon.pt_90} ${comon.pb_90} ${style.card_section}`}
                            style={{
                                backgroundImage: `url(${slide.backgroundImage})`,
                                backgroundSize: "cover",
                                backgroundPosition: "center top",
                                backgroundRepeat: "no-repeat",
                            }}
                        >
                            <div
                                className={style.gift_block}
                                data-aos="fade-in"
                                data-aos-duration="1000"
                            >
                                <div className={style.gift_content}>
                                    <div
                                        // data-aos="fade-in"
                                        // data-aos-duration="1000"
                                        className={`${comon.w_100} ${comon.pb_20}`}
                                    >
                                        <h6>{slide.title}</h6>
                                        <h5>{slide.subtitle}</h5>
                                    </div>
                                    <div
                                        // data-aos="fade-in"
                                        // data-aos-duration="1000"
                                        className={`${comon.pb_20} ${comon.d_flex_wrap} ${comon.justify_center}`}
                                    >
                                        <Image
                                            src={slide.imageSrc}
                                            className={comon.image_res}
                                            alt=""
                                            quality={100}
                                            width={412}
                                            height={209}
                                        />
                                    </div>
                                    <div
                                        // data-aos="fade-in"
                                        // data-aos-duration="1000"
                                        className={style.gift_txt_block}
                                    >
                                        <p>{slide.description}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                ))}
            </ul>

        </div>




    );
};

export default AwardStack;
