@import "variable", "base", "mixin";

.image_container {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;

    .ul_sec {


        &.template1 {
            display: flex;
            width: 40%;

            gap: 5px;

            li {
                width: calc(50% - 5px);
                display: flex;
                flex-direction: column;
                gap: 5px;

                .img_body {
                    width: 100%;
                    height: auto;

                    >img {
                        height: 100%;
                        width: 100%;
                        display: block;
                        object-fit: cover;
                    }
                }

                &:last-child {
                    width: 50%;

                    .img_body {
                        height: 100%;
                    }
                }
            }

            @media #{$media-767} {
                width: 100%;


            }
        }

        &.template2 {
            display: flex;
            width: calc(60% - 5px);
            gap: 5px;
            flex-wrap: wrap;

            li {
                width: 70%;
                gap: 5px;

                .img_body {
                    width: 100%;
                    height: 100%;
                    aspect-ratio: 5/2;

                    >img {
                        height: 100%;
                        width: 100%;
                        display: block;
                        object-fit: cover;
                    }
                }

                &:nth-child(2),
                &:nth-child(3) {
                    width: calc(30% - 5px);

                    .img_body {
                        aspect-ratio: 1/1;
                    }
                }
            }

            @media #{$media-767} {
                width: 100%;


            }
        }

        &.template3 {
            display: flex;
            width: 100%;
            gap: 5px;
            flex-wrap: wrap;

            li {
                // width: 20%;
                    width: calc(21% - 5px);


                .img_body {
                    width: 100%;

                    >img {

                        width: 100%;
                        display: block;
                        object-fit: cover;
                    }
                }

                @media #{$media-767} {

                    width: calc(50% - 2.5px);


                }

                &:first-child {
                    // width: calc(60% - 10px);
                    width: 58%;

                    .img_body {
                        img {

                            aspect-ratio: 16/9;
                        }
                    }


                    @media #{$media-767} {

                        width: 100%;


                    }

                }
            }
        }


    }
}

.img_body {
    width: 100%;
    height: auto;
    >img {
        height: 100%;
        width: 100%;
        display: block;
        object-fit: cover;
    }
}
