import React, { useState, useEffect, useRef } from "react";
import Head from "next/head";
import Link from "next/link";
// ---buttion-import--start
import But<PERSON> from "@/component/button/Buttons";
import Marquee from "@/component/Marquee";
// ---buttion-import--end

import Image from "next/image";
import InnerBanner from "@/component/InnerBanner";
import Parallax from "@/component/Paralax";
import Map from "@/component/contactmap";
// Import Swiper styles
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/scrollbar";
import { Scrollbar, Autoplay, Navigation } from "swiper/modules";
import comon from "@/styles/comon.module.scss";
import project from "@/styles/project.module.scss";
import buttion from "@/styles/buttion.module.scss";
import DynamicImage from "@/component/DynamicImage";

import ProjectSlider from "@/component/ProjectSlider";
import ProjectSliderNew from "@/component/ProjectSliderNew";
import AOS from "aos";
import "aos/dist/aos.css";
import HeadMarquee from "@/component/HeadMarquee";
import SliderSection from "@/component/SliderSection";
import ContactSection from "@/component/ContactSection";
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import SliderNew from "@/component/SliderNew";
import StartCampaign from "@/component/StartCampaign";
import SliderBannerText from "@/component/SliderBannerText";
import SliderAnimMezah from "@/component/SliderAnimMezah";
import parse from 'html-react-parser';
import { fetchByPost } from "@/lib/api/pageApi";
import { useRouter } from "next/router";

// -----slider--mapping------start
export default function Projectmezah({pageData}) {
	const [isMobile, setIsMobile] = useState(false);

	useEffect(() => {
		const handleResize = () => {
			// setIsMobile(window.innerWidth < 768);
			setIsMobile(window.innerWidth < 1024);
		};

		handleResize(); // Check on mount
		window.addEventListener("resize", handleResize);

		return () => window.removeEventListener("resize", handleResize);
	}, []);

	const [isClient, setIsClient] = useState(false);

	const [activeIndex, setActiveIndex] = useState(0);
	const swiperRef = useRef(null);

	const slides = [
		"/images/project-slide1.png",
		"/images/project-slide2.png",
		"/images/project-slide3.png",
		"/images/project-slide4.png",
		"/images/project-slide5.png",
	];

	// Initialize AOS and set client-side state after the component mounts
	useEffect(() => {
		setIsClient(true);
		AOS.init();
	}, []);

	const router = useRouter();
	const [hideSliderMainImg, setHideslidermainImg] = useState(false)
	useEffect(() => {
		if (router.query.id === "section") {
			setHideslidermainImg(true)

		}
	}, [router.query.id]);


	useEffect(() => {
		if (!router.isReady) return; // Ensure router is ready

		// Run the effect after a small delay to allow re-renders
		setTimeout(() => {
			const offset = 50;
			const targetElement = document.getElementById("mezah_section");

			if (router.query.id === "section" && targetElement) {
				const topPosition = targetElement.getBoundingClientRect().top + window.scrollY;
				window.scrollTo({ top: topPosition - offset, behavior: "smooth" });
			}
		}, 300);
	}, [router.query.id, router.isReady]);

	if (!isClient) {
		return null; // Prevent SSR mismatch by rendering nothing until the component is mounted on the client
	}

	return (
		<>
			{/* <Head>
				<title>Project mezah</title>
				<meta name="description" content="Generated by create next app" />
			</Head> */}
			<HeadMarquee />

			{/* <section className={`  ${comon.pb_90} ${comon.pt_200}`} >
				<div className={`${comon.wrap}`}>
						<ProjectSliderNew/>
				</div>
			</section> */}

			<div className={comon.linear_bg}>
			{pageData.acf.banner_details.description_field ? (
		  
				<SliderBannerText
					title={pageData.acf.banner_details.title}
					paragraph={pageData.acf.banner_details.description_}
					details={pageData.acf.banner_details}
					extra_height_class={true}
					padding_220={true}
					full_height_banner={true}
				/>
				):(
				<InnerBanner  showLink={false} title={pageData.acf.banner_details.title} details={pageData.acf.banner_details} />
				)}

				<section
					className={`${project.project_slider_section} ${comon.pb_90}`}
					id="mezah_section"
				>
					{/* <div className={``}> */}

					<SliderAnimMezah 
						sliderData={pageData.acf.details} secondData={pageData.acf.description} 
					/>
				</section>
			</div>

			<section
				className={`${project.section_project_category} ${comon.pt_35} ${comon.pb_30}`}
			>
				<div className={`${comon.wrap} ${comon.d_flex_wrap}`}>
					<div
						className={`${project.project_logo}`}
					
					>
						<Image
							src={pageData.acf.features.logo}
							width={203}
							height={86}
							alt="button image"
							quality={100}
						/>
					</div>

					<div
						className={`${project.project_icons_list}`}
						// data-aos="fade-up"
						// data-aos-duration="1000"
					>
						<ul className={`${comon.d_flex_wrap}`}>
							{pageData.acf.features.facilities_list.map((item,index) => {
								return (
									<li data-tooltip-id={`my-tooltip${index+1}`}>
										<div className={`${project.project_icon}`}>
											<Image
												src={item.icon}
												width={60}
												height={60}
												alt="button image"
												quality={100}
											/>
										</div>
										<h4>{item.text}</h4>

										{/* <div className={`${project.tooltip} tooltip`}>
											<p>
												{parse(item.description)}
											</p>
										</div> */}
									</li>
								)
							})}
							
						</ul>
					</div>
				</div>
			</section>

			<SliderSection
				images={pageData.acf.gallery_details.gallery.map((item) => ({
					img: item,
					description:''
				  }))}
				title={pageData.acf.gallery_details.gallery_title}
				paragraph={pageData.acf.gallery_details.gallery_description}
			/>

			<StartCampaign />

			<section className={`${comon.pt_60} `} style={{ background: "#E1E2E1" }}>
				<ContactSection
					title="Request for Quotation"
					paragraph="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore."
					button="Request a Quote"
					showAutoMargin={false}
				/>
			</section>
		</>
	);
}
