@import "variable", "base", "mixin";

.list_ul {
    // margin: 0 -0.5%;
    display: flex;
    flex-wrap: wrap;
    gap: 6px;

    li {
        min-width: calc(25% - 5px);
        align-items: center;
        list-style: none;
        // margin: 0 0.5%;
        // margin-bottom: 6px;

        color: #3b3064;

        >label {
            // background: #fff;
            background: #7F75B6;
            border: solid 1px #d3d3d3;
            padding: 10px 7px;
            color: white;
            display: flex;
            width: 100%;
            display: flex;
            gap: 3px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;

            @media #{$media-700} {
                font-size: 13px;
            }

            &:hover {
                border: solid 1px #40269ec2;
            }

            &.selected {
                border: solid 1px #3b306475;
            }

            &.sub_label {
                color: black;
                background: #ffffff00 !important;
                border: solid 1px #d3d3d300;

            }
        }
    }

    &.special_label {
        li {
            min-width: unset !important;

            >label {
                padding-right: 15px;
                max-width: unset !important;
            }
        }
    }
}

.label_checkbox_input {
    position: relative;
    height: 15px;
    margin: 0 5px;
    width: 15px !important;
    cursor: pointer;
}

.label_checkbox_input::before {
    position: absolute;
    content: "";
    border: 1px solid #d9d9d9;
    // background-color: white;
    background-color: rgba(255, 255, 255, 0);
    // height: 15px;
    // width: 15px;
    height: 13px;
    width: 13px;
    border-radius: 1px;
    top: 0;
    left: 0;
}

.label_checkbox:checked+.label_checkbox_input::before {
    border: 1px solid #3b3064;
}

.label_checkbox:checked+.label_checkbox_input::after {
    position: absolute;
    content: "";
    height: 3px;
    width: 9px;
    border-left: 2px solid #3b3064;
    border-bottom: 2px solid #3b3064;
    top: 4px;
    left: 2px;
    transform: rotate(320deg);
    z-index: 2;
}

.select_network_block {
    // width: 48%;
    width: calc(50% - 20px);
    height: fit-content;

    &.width_620 {
        width: 50%;

        @media #{$media-700} {
            width: 100%;
        }
    }

    &.first_element {
        padding: 0;
        border: 0;
        width: 100%;

        li {
            label {
                width: auto;
                max-width: 140px;
            }
        }

        @media #{$media-700} {
            width: 100%;
            padding: 0 !important;
        }
    }

    @media #{$media-1366} {
        margin-top: 30px;
    }

    @media #{$media-1280} {
        margin-top: 20px;
    }

    @media #{$media-1024} {
        margin-top: 10px;
    }

    @media #{$media-700} {
        width: 100%;
    }

    &.dark_mode {
        border-color: #606060 !important;

        li {
            border-color: #606060 !important;
        }

        label {
            background-color: #242424 !important;
            color: #fff;

            ::before {
                border-color: #717171 !important;
                background-color: transparent !important;
            }

            ::after {
                border-color: #fff !important;
            }

            &:hover {
                // border-color: #717171 !important;
                border-color: #71717100 !important;

            }

            &.selected {
                border-color: #717171 !important;
            }

            &.sub_label {
                background-color: #24242400 !important;
                color: white;

            }
        }

        span {
            color: #fff;
            background-color: #000;
        }
    }
}

.block_style_01 {
    border: solid 1px #b0b0b0;
    padding: 30px 1% 1% 1%;
    position: relative;
    margin-bottom: 45px;

    .title_lable {
        position: absolute;
        background: #ececec;
        top: -9px;
        padding: 0 7px;
        color: #3b3064;
        font-size: 17px;

        @media #{$media-1280} {
            padding: 0 5px;
        }

        @media #{$media-700} {
            font-size: 16px;
        }
    }

    @media #{$media-1366} {
        margin-bottom: 30px;
    }

    @media #{$media-1024} {
        margin-bottom: 20px;
    }

    @media #{$media-700} {
        padding-left: 3% !important;
        padding-right: 3% !important;
    }
}

.select_network_container {

    @media #{$media-700} {
        flex-direction: column-reverse;

    }
}

.date_picker_block {
    padding-top: 20px;
    width: 100%;
    // background: #ff0000;
    display: flex;
    justify-content: flex-end;

    img {
        display: block;
        object-fit: contain;
        height: auto;
        width: 100%;
    }

    @media #{$media-1366} {
        padding-top: 10px;


    }

    @media #{$media-700} {
        width: 100%;
        justify-content: center;
        margin-bottom: 20px;
        padding-top: 5px;

    }
}

.campaign_detail_left {
    width: 40%;
    position: relative;
    // border-right: solid 1px #ccc;

    &::after {
        position: absolute;
        height: calc(100% + 32px);
        width: 1px;
        background-color: rgb(212, 212, 212);
        content: '';
        right: 0;
        top: 20px;


        @media #{$media-700} {
            position: unset;

        }
    }



    @media #{$media-700} {
        width: 100%;
        border-right: none;
    }

    &.dark_theme {
        ul {
            li {
                color: white !important;
            }
        }
    }
}

:global(body.rtl) .campaign_detail_left::after {
    right: inherit;
    left: 0;
}

.campaign_detail_right {
    width: 45%;

    @media #{$media-700} {
        width: 100%;
    }

    &.dark_theme {
        ul {
            li {

                input,
                textarea {
                    color: white !important;

                    &::placeholder {
                        color: white !important;
                    }
                }

                &::after {
                    background-color: white !important;
                }
            }
        }
    }
}

.selected_list {
    li {
        padding-bottom: 10px;
        font-size: 14px;
        color: #3b3064;

        @media #{$media-700} {
            padding-bottom: 10px;
            font-size: 13px;
        }
    }
}

.campaign_wrap_block {
    display: flex;
    width: calc(50% - 20px);
    flex-wrap: wrap;
    flex-direction: column;
    // justify-content: flex-start;
    justify-content: space-between;

    >li {
        width: 100%;
        padding-bottom: 30px !important;
    }

    @media #{$media-700} {
        width: 100%;
    }
}