import React, { useState, useEffect } from "react";
import comon from "@/styles/comon.module.scss";
import Link from "next/link";
import { useRouter } from 'next/router';
import parse from 'html-react-parser';
const StartCampaign = ({ bgColor = "#6758B6", darkColor = false, plainBlack = false }) => {
    console.log("darkColor", darkColor);
    const { locale } = useRouter(); 
    const [options, setOptions] = useState(null); 
    const [optionsShow, setOptionsShow] = useState(null);
    useEffect(() => {
        const fetchOptions = async () => {
          try {
            const res = await fetch(
              `${process.env.NEXT_PUBLIC_API_BASE_URL}/option-field/campaign_bar?lang=${locale}`
            );
            const data = await res.json();
            setOptions(data);
          } catch (error) {
            console.error("Error fetching options:", error);
          }
        };

         const fetchShowPagesOption = async () => {
            try {
                const res = await fetch(
                    `${process.env.NEXT_PUBLIC_API_BASE_URL}/option-field/show_in_pages?lang=${locale}`
                );
                const data = await res.json();
                // console.log("hhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh", setOptionsShow);
                setOptionsShow(data);
            } catch (error) {
                console.error("Error fetching show pages options:", error);
            }
        };
    
        fetchShowPagesOption();
        fetchOptions();
      }, [locale]);
      
      useEffect(()=> {
        console.log("hhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh", optionsShow);
      },[optionsShow])
      if (!options || optionsShow != true) {
        return;
      }
    return (
        <div>
            <section
                className={`${comon.pt_60} ${comon.pb_65} ${darkColor == true ? `${comon.pt_90} ${comon.pb_85}` : ""
                    }`}
                style={{ backgroundColor: bgColor }}
            >
                <div className={`${comon.wrap}`}>
                    <div
                        className={`${comon.d_flex_wrap} ${comon.justify_space_bet}  ${comon.flex_center}  ${comon.w_100}`}
                    >
                        <div

                            className={`${comon.title_block_main}  ${darkColor == true ? comon.dark_colour : ""
                                } ${comon.white_color}`}
                        >
                            <div className={`${comon.title_30} ${comon.mb_15}`}>
                                <h3>{parse(options.title)}</h3>
                            </div>
                            <div className={`${comon.text_white}   ${comon.font_15}`}>
                                <p>{parse(options.sub_title)}</p>
                            </div>
                        </div>
                        {/* <Link data-aos="fade-in" data-aos-duration="1000"
                            className={`${comon.buttion} ${comon.but_blue}  ${comon.custom_btn} ${comon.but_h_02} ${darkColor == true ? comon.dark_bg : ''}  ${comon.but_dark}`}
                            href={"/start-your-campaign"}
                        >
                            Get Started Now
                        </Link> */}
                        {options.button && (
                        <Link

                            className={`${comon.buttion} ${comon.but_blue}  ${comon.custom_btn
                                } ${comon.but_h_02} ${darkColor == true ? comon.dark_bg : ""}   ${comon.but_white
                                }`}
                                href={`/start-your-campaign${darkColor == true ? '?theme' : ''}${plainBlack == true ? 'black' : ''}`}
                        // onClick={isDarkThemeEnable}
                        >
                            {options.button.title}
                        </Link>
                        )}
                    </div>
                </div>
            </section>
        </div>
    );
};

export default StartCampaign;
