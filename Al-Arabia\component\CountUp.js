import React from "react";
import comon from "@/styles/comon.module.scss";
import CountUp from "react-countup";
import { useInView } from "react-intersection-observer";

const CountUpSec = ({
  counterData,
  extraClass = false,
  isMaxWidthDisable = false,
  bgColor, textColor, innerBg
}) => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 1.0,
  });

  return (
    <div className={`${comon.count_up_secton} ${comon.count_up_new}`}
    style={{backgroundColor:bgColor}}
    >
      <div
        className={`${!isMaxWidthDisable ? comon.wrap : comon.airport_px_3
          } ${comon.pb_60} ${comon.pt_60}`}
      >
        <ul
          ref={ref}
          className={`${comon.count_up_field} ${extraClass ? comon.count_up_field_min_height : ""
            } ${counterData.length % 2 === 0 ? comon.even_number : ""}`}
        >
          {counterData.map((data, index) => (
            <li key={index}
            style={{backgroundColor:innerBg}}
            >
              <div>
                <span className={comon.count_up_span} style={{color:textColor}}>
                  {data.postfix}
                  {data.value && (
                    <CountUp
                      start={0}
                      end={inView ? data.value : 0}
                      duration={4}
                      delay={0}
                      className="base_font"
                      style={{color:textColor}}
                    >
                      {({ countUpRef }) => (
											<span ref={countUpRef} className="base_font" 
                      style={{color:textColor}}
                      />
										)}
                      </CountUp>
                  )}
                </span>
                <p style={{color:textColor}} >{data.text_}</p>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default CountUpSec;
