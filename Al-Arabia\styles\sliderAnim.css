.h_100 {
    overflow: hidden !important;
    position: relative;
}

/* .overlap_image {
    position: absolute;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    z-index: 5;
    left: 45.2%;
    top: 6.35%;

} */

.overlap_image {
    position: absolute;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    /* width: 488px !important; */
    pointer-events: none;
    width: 410px !important;
    z-index: 5;
    left: 50%;

    /* top: 50.5%; */
    top: 47%;
}

.overlap_image.dubai_page {
    /* top: 44.1%; */
    /* top: 48.47%; */
    top: 54.47%;
    width: 344px !important;
}

.img_logo {
    width: 110px;
    height: auto;
    padding-bottom: 25px;
}

.img_logo>img {
    height: auto;
    width: 100%;
    display: block;
    object-fit: contain;
}

.overlap_image img {
    height: auto;
    width: 100%;
    object-fit: contain;
    object-position: top;
    /* Ensures the image fills the container */
    display: block;

    /* aspect-ratio: 1/1; */
}

.overlap_image.mezah {
    aspect-ratio: 1/1;
}

.overlap_image.mezah img {
    height: 100%;
}

.slider_anim_new {
    width: 100% !important;
}

.slider_anim_new .swiper-wrapper {
    justify-content: space-between !important;
}

.slidernew1 {
    position: relative;
    margin-top: 70px;
}

.slidernew1.dubai {
    padding-bottom: 50px;
    padding-top: 50px;
}

.slidernew1.mezah_page {
    margin: 0 -3.5%;
    width: auto;
}

.slider_anim_new.dubai_swiper .swiper-slide-active .slider_anim_new_img {
    height: auto !important;
    position: relative;
    aspect-ratio: 1/1;
}

.slider_anim_new.dubai_swiper .swiper-slide-active .slider_anim_new_img::after {
    content: "";
    position: absolute;
    height: 90px;
    width: 50%;
    /* background: linear-gradient(to top,
            rgba(236, 236, 236, 0.568),
            rgba(0, 128, 0, 0)); */

    background: linear-gradient(to top,
            rgba(236, 236, 236, 0.911),
            rgba(0, 128, 0, 0));
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}

.slider_anim_new.dubai_swiper .swiper-slide-active .slider_anim_new_img>img {
    height: auto !important;
}

.slider_anim_new_img {
    width: 100%;
    height: 100%;
    transition: all 0.3s ease !important;
}

.swiper-slide .slider_anim_new_img.mezah {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(160, 151, 200, 0.03);
}

.swiper-slide-active .slider_anim_new_img.mezah {
    background-color: rgba(255, 0, 0, 0);
    transition: all 0.3s ease !important;
}

.swiper-slide-active .slider_anim_new_img:hover {
    transform: scale(1.05) !important;

}

.swiper-slide .slider_anim_new_img.mezah>img {
    height: 80%;
    width: 80%;
}

.swiper-slide-active .slider_anim_new_img.mezah>img {
    height: 100%;
    width: 100%;
}

.slider_anim_new_img img {
    height: 100%;
    width: 100%;
    object-fit: contain;
    display: block;
}

.slider_anim_new .swiper-slide-next,
.slider_anim_new .swiper-slide-prev,
.slider_anim_new .swiper-slide-active {
    transition: all 0.3s ease !important;
}

.slider_anim_new .swiper-slide {
    transform: scale(0.6) translateZ(0) !important;
    /* transition: all 0.3s ease !important; */
    margin-top: 350px !important;
    /* border: 1px solid #a097c8; */
    border: 1px solid hsla(251, 31%, 69%, 0.7);

    /* background-color: #42376f !important; */
    background-color: hsla(252, 34%, 33%, 0.1) !important;
    aspect-ratio: 1/1;
    display: flex;
    align-items: center;
}

.dubai_swiper_new .swiper-slide .slider_anim_new_img.dubai_page_img {
    justify-content: center;
    display: flex;
    align-items: center;
}

.dubai_swiper_new .swiper-slide .slider_anim_new_img.dubai_page_img>img {
    height: 80%;
    width: 80%;
    display: block;
}

.dubai_swiper_new .swiper-slide-active .slider_anim_new_img.dubai_page_img>img {
    height: 100%;
    width: 100%;
}

.slider_anim_new.maleem_border .swiper-slide {
    border: 1px solid #ffffff;
    background-color: #42376f00 !important;
    margin-right: 0 !important;
    margin-left: 0 !important;
}

.slider_anim_new.dubai_swiper_new .swiper-slide {
    transform: scale(0.65) translateZ(0) !important;
}

.slider_anim_new.dubai_swiper_new .swiper-slide-next,
.slider_anim_new.dubai_swiper_new .swiper-slide-prev {
    transform: scale(0.82) translateZ(0) !important;
}

.slider_anim_new .swiper-slide-next,
.slider_anim_new .swiper-slide-prev {
    transform: scale(0.8) translateZ(0) !important;
    margin-top: 250px !important;
    /* border: 1px solid #a097c8b4; */
    /* border: 1px solid rgba(47, 0, 255, 0.5); */
}

.slider_anim_new.dubai_swiper_new .swiper-slide {
    margin-top: 280px !important;
}

.slider_anim_new.dubai_swiper_new .swiper-slide-next,
.slider_anim_new.dubai_swiper_new .swiper-slide-prev {
    margin-top: 208px !important;
}

.mezah_page .slider_anim_new .swiper-slide {
    transform: scale(0.63) translateZ(0) !important;
}

/* .mezah_page .slider_anim_new .swiper-slide-next,
.mezah_page .slider_anim_new .swiper-slide-prev {
    transform: scale(0.838) translateZ(0) !important;
} */

.mezah_page .slider_anim_new .swiper-slide-next,
.mezah_page .slider_anim_new .swiper-slide-prev {
    transform: scale(0.78) translateZ(0) !important;
}

.mezah_page .slider_anim_new .swiper-slide-prev {
    left: -60px !important;
}

.mezah_page .slider_anim_new .swiper-slide-next {
    left: 60px !important;
}

.slider_anim_new .swiper-slide-next {
    position: relative;
    right: -65px;
}

.slider_anim_new.dubai_swiper_new .swiper-slide-next {
    right: -55px;
}

.rtl .slider_anim_new.dubai_swiper_new .swiper-slide-next {
    right: 55px;
}

.slider_anim_new.dubai_swiper_new .swiper-slide-prev {
    left: -55px;
}

.rtl .slider_anim_new.dubai_swiper_new .swiper-slide-prev {
    left: 55px;
}

.slider_anim_new .swiper-slide-prev {
    position: relative;
    left: -65px;
}

.slider_anim_new.maleem_border .swiper-slide-next,
.slider_anim_new.maleem_border .swiper-slide-prev {
    border: 1px solid #ffffffb4;
}

.slider_anim_new.maleem_border .swiper-slide-active,
.slider_anim_new .swiper-slide-active,
.mezah_page .slider_anim_new .swiper-slide-active {
    margin-top: 135px !important;
    /* transform: scale(2) translateZ(0) !important; */
    transform: scale(2.2) translateZ(0) !important;
    border: 1px solid rgba(255, 255, 255, 0);
    background-color: #ffffff00 !important;
    margin-top: 20px;
}

.mezah_page .slider_anim_new .swiper-slide-active {
    transform: scale(1.72) translateZ(0) !important;
}

.slider_anim_new.dubai_swiper .swiper-slide-active {
    margin-top: 100px !important;
    transform: scale(1.55) translateZ(0) !important;
}

.slider_anim_new .swiper-slide-next .project_slide_new_img,
.slider_anim_new .swiper-slide-prev .project_slide_new_img {
    transform: scale(0.8) !important;
}

.slider_anim_new_btn {
    z-index: 1;
    text-align: center;
    position: absolute;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
}

.slider_anim_new_btn.mezah_page {
    left: 49.5%;
    transform: translateX(-49.5%);
}

.slider_anim_new_btn.dubai {
    bottom: 25%;
    display: flex;
    gap: 20px;
}

.slider_anim_new_btn.dubai_btn {
    /* bottom: 25% !important; */
    /* bottom: 19% !important; */
    bottom: 15% !important;
}

/* .slider_anim_new_btn.bt_60 {
    display: flex;
    gap: 5px;
    bottom: 60px;
} */
.slider_anim_new_btn.bt_60 {
    display: flex;
    gap: 30px;
    /* bottom: 20%; */
    bottom: 17%;
}

@media screen and (max-width: 1024px) {
    .slider_anim_new_btn.bt_60 {
        bottom: 21%;
    }
}

.slider_heading {
    text-align: center;
    margin: auto;
    color: white;
    font-size: 25px;
    position: absolute;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
}

.slider_heading.dubai {
    color: #3a3064;
}

@media screen and (max-width: 1440px) {
    .mezah_page .slider_anim_new .swiper-slide-prev {
        left: -50px !important;
    }

    .mezah_page .slider_anim_new .swiper-slide-next {
        left: 50px !important;
    }
}

@media screen and (max-width: 1280px) {
    .slider_anim_new.dubai_swiper_new .swiper-slide-next {
        right: -40px;
    }

    .rtl .slider_anim_new.dubai_swiper_new .swiper-slide-next {
        right: 40px;
    }

    .slider_anim_new.dubai_swiper_new .swiper-slide-prev {
        left: -40px;
    }

    .rtl .slider_anim_new.dubai_swiper_new .swiper-slide-prev {
        left: 40px;
    }
}

@media screen and (max-width: 1100px) {
    .mezah_page .slider_anim_new .swiper-slide-prev {
        left: -30px !important;
    }

    .mezah_page .slider_anim_new .swiper-slide-next {
        left: 30px !important;
    }

    .img_logo {
        width: 90px;

        padding-bottom: 15px;
    }

    .overlap_image.mezah {
        width: 283px !important;
        left: 49.99%;
        top: 45.4%;
    }

    .slider_anim_new.dubai_swiper .swiper-slide-active .slider_anim_new_img {
        height: 100% !important;
    }

    .slider_anim_new.dubai_swiper .swiper-slide-active .slider_anim_new_img>img {
        height: 100% !important;
    }

    .overlap_image.dubai_page {
        top: 15.8%;

        width: 283px !important;
        height: 283px !important;
    }

    .overlap_image.dubai_page>img {
        height: 100%;
    }
}

@media screen and (max-width: 1024px) {
    .slidernew1.dubai {
        margin-top: 0px;
    }

    .slider_anim_new.dubai_swiper .swiper-slide-active .slider_anim_new_img {
        height: 100% !important;
    }

    .slider_anim_new.dubai_swiper .swiper-slide-active .slider_anim_new_img>img {
        height: 100% !important;
    }

    /* .overlap_image.dubai_page {
        top: 12.8%;

        width: 261px !important;
        height: 261px !important;
    } */
    .overlap_image.dubai_page {
        top: 11.6%;
        width: 270px !important;
        height: 270px !important;
        display: none;
    }

    .overlap_image.mezah {
        width: 296px !important;
        left: 50.1%;
        /* top: 45%; */
        top: 53%;
        display: none;
    }

    .mezah_page .slider_anim_new .swiper-slide-active {
        margin-top: 70px !important;
    }

    .overlap_image.dubai_page>img {
        height: 100%;
    }

    .slider_anim_new .swiper-slide {
        margin-top: 250px !important;
    }

    .slider_anim_new .swiper-slide-next,
    .slider_anim_new .swiper-slide-prev {
        margin-top: 150px !important;
    }

    .slider_anim_new .swiper-slide-active {
        margin-top: 60px !important;
    }

    .slider_anim_new.dubai_swiper .swiper-slide-active {
        margin-top: 60px !important;
        transform: scale(1.55) translateZ(0) !important;
    }

    .slider_anim_new.mezah_page .swiper-slide-active {
        transform: scale(1.7) translateZ(0) !important;
    }

    .slider_anim_new_btn {
        bottom: 100px;
    }

    .slider_heading {
        font-size: 20px;
        bottom: 50px;
    }
}

.slider_secton_new.new_slider .swiper-slide {
    width: 15%;
}

.slider_secton_new.new_slider .swiper-slide-active {
    width: 35%;
}

@media screen and (max-width: 820px) {
    .slider_anim_new .swiper-slide-next {
        right: -35px;
    }

    .slider_anim_new .swiper-slide-prev {
        left: -35px;
    }

    .overlap_image.mezah {
        width: 231px !important;
        left: 50%;
        top: 49.5%;
    }

    .overlap_image.dubai_page {
        top: 14.8%;
        width: 211px !important;
        height: 211px !important;
    }
}

@media screen and (max-width: 819px) {
    .overlap_image {
        display: none;
    }

    .slider_anim_new_btn.project_slider_btn {
        position: unset !important;
        transform: unset !important;
    }

    .slider_anim_new .swiper-slide,
    .slider_anim_new.dubai_swiper.mobile .swiper-slide {
        margin-top: 0px !important;
        transform: scale(1) !important;
        border: 1px solid #a097c800;
        background-color: unset !important;
    }

    .slider_heading.dubai {
        position: unset !important;
        transform: unset;
        margin-top: 20px;
    }

    .slider_heading.mezah_btn {
        position: relative !important;
        transform: unset !important;
        margin-top: 20px;
        z-index: 5 !important;
        filter: invert(1);
    }

    .slider_anim_new_btn.dubai.project_slider_btn {
        position: unset;
        width: 100% !important;
        display: flex;
        transform: unset;
        justify-content: center;
        margin-top: 10px;
    }

    .slider_anim.dubai .swiper-slide .slider_anim_img {
        height: auto;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .slider_anim.dubai .swiper-slide .slider_anim_img>img {
        aspect-ratio: 1/1;
        height: auto;
        width: 100%;
        display: block;
        object-fit: contain;
    }

    .slider_anim_heading {
        text-align: center;
        width: 100%;
        padding-top: 20px;
        color: #3a3064;
        font-size: 20px;
    }

    .slider_anim.dubai_mobile_swiper .swiper-slide {
        transform: scale(0.65) translateZ(0) !important;
        transition: all 0.5s ease;
    }

    .slider_anim.dubai_mobile_swiper .swiper-slide-active {
        transform: scale(1) translateZ(0) !important;
    }
}

@media screen and (max-width: 768px) {
    .slidernew1.dubai {
        padding-bottom: 30px;
        padding-top: 30px;
    }

    .slider_anim_new .swiper-slide {
        margin-top: 150px !important;
    }

    .slider_anim_new .swiper-slide-next,
    .slider_anim_new .swiper-slide-prev {
        margin-top: 100px !important;
    }

    .slider_anim_new .swiper-slide-active {
        margin-top: 50px !important;
    }
}

@media screen and (max-width: 600px) {
    .project_mezah_img_logo {
        display: none;
    }

    .slider_anim.dubai .swiper-slide .slider_anim_img>img {
        width: 70%;
    }

    .slider_anim_heading {
        padding-top: 15px;
        font-size: 16px;
    }

    .slider_anim.dubai_mobile_swiper .swiper-slide {
        transform: scale(1) translateZ(0) !important;
    }
}

.rtl .mezah_page .slider_anim_new .swiper-slide-next {
    left: unset !important;
    right: 50px !important;
}

.rtl .mezah_page .slider_anim_new .swiper-slide-prev {
    right: -50px !important;
    left: unset !important;
}

.rtl .project_slider_btn {
    flex-direction: row-reverse;
}

.thumb_swiper_slide_btn {
    position: absolute;
    top: 50%;
    left: 50%;

    transform: translate(-50%, -50%);
    width: 95%;

    z-index: 5;
    display: flex;
    align-items: center;
    justify-content: space-between;
    pointer-events: none;
}

.thumb_swiper_slide_btn.top_adjusted {
    /* top: 60.5%; */
    top: 0%;
    width: 100%;
    padding-top: 20px;
    /* height: 353px; */
    height: 100%;
    left: 0;
    transform: translate(0, 40px);

}


.thumb_swiper_slide_btn.top_adjusted .swiper_btn.prev_btn {
    transform: scale(0.79) scaleX(-1) translateX(20px);
}

.rtl .thumb_swiper_slide_btn.top_adjusted .swiper_btn.prev_btn {
    transform: scale(0.79) scaleX(1) translateX(20px);
}

.rtl .thumb_swiper_slide_btn.top_adjusted .swiper_btn.next_btn {
    transform: scale(0.79) scaleX(-1) translateX(20px);
}

.thumb_swiper_slide_btn.top_adjusted .swiper_btn {
    transform: scale(0.79) translateX(20px);
    /* background-image: linear-gradient(to left, rgb(0, 0, 0), rgba(0, 128, 0, 0)); */
    /* background-image: linear-gradient(to left, #3b3064, rgba(0, 128, 0, 0)); */
    background-image: linear-gradient(to left, #ececec, rgba(0, 128, 0, 0));
    height: 100%;
    width: 80px;
    display: flex;

    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.thumb_swiper_slide_btn.top_adjusted.black_bg {
    padding-top: 0px;
    /* margin-top: -1px; */

}

@media screen and (max-width: 700px) {
    .thumb_swiper_slide_btn.top_adjusted.black_bg {

        transform: translate(0, 25px);

    }



}

.thumb_swiper_slide_btn.top_adjusted.black_bg .swiper_btn {
    background-image: linear-gradient(to left, #000000, rgba(0, 128, 0, 0)) !important;


}


.thumb_swiper_slide_btn.top_adjusted .swiper_btn.swiper-button-disabled {
    opacity: 0;
}

.thumb_swiper_slide_btn.top_adjusted .swiper_btn img {
    height: auto;
    aspect-ratio: 1/1;
    width: 50%;
}

.thumb_swiper_slide_btn .swiper_btn {
    pointer-events: all;
    height: auto;
    width: 15px;
    cursor: pointer;

}

.thumb_swiper_slide_btn .swiper_btn img {
    height: auto;
    width: 100%;
    object-fit: contain;
    display: block;
}

.thumb_swiper_slide_btn .prev_btn {
    transform: scaleX(-1);
}

.rtl .thumb_swiper_slide_btn .prev_btn {
    transform: scaleX(1);
}

.rtl .thumb_swiper_slide_btn .next_btn {
    transform: scaleX(-1);
}


.thumb_swiper_slide_btn.top_adjusted.hover_slide_btn {
    padding-top: 0px;
    transform: translate(0, 0px);

}

.thumb_swiper_slide_btn.top_adjusted.black_bg.padding__top_space {
    padding-top: 25px;

}

@media screen and (max-width: 1024px) {

    .thumb_swiper_slide_btn.top_adjusted.hover_slide_btn {
        padding-top: 0px;
    }

    .thumb_swiper_slide_btn.top_adjusted.black_bg.padding__top_space {
        padding-top: 0px;

    }

}



.thumb_swiper_slide_btn.top_adjusted.hover_slide_btn .prev_btn {

    transform: scale(1) scaleX(-1) translateX(0px);
}

.thumb_swiper_slide_btn.top_adjusted.hover_slide_btn .next_btn {

    transform: scale(1) scaleX(1) translateX(0px);
}






@media screen and (max-width: 700px) {


    .thumb_swiper_slide_btn .swiper_btn {

        width: 10px;
    }

}

@media screen and (max-width: 500px) {


    .thumb_swiper_slide_btn.top_adjusted .swiper_btn {
        width: 60px;
    }


}