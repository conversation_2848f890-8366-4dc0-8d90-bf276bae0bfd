import React, { useState, useEffect } from "react";
import contact from "@/styles/contact.module.scss";
import comon from "@/styles/comon.module.scss";
import ContactForm from "@/component/ContactForm";
import { useRouter } from "next/router";
import parse from 'html-react-parser';
import RequestQuotation from "./form/RequestQuotation";
const ContactPageSection = ({	
	showAutoMargin,
	whiteBtn,
	className = "",
	classNameSecond = "",
	button,
	title,
	paragraph
}) => {

	const { locale } = useRouter(); 
	const [options, setOptions] = useState(null); 


	return (
		<div className={`${comon.wrap} ${comon.d_flex_wrap} ${comon.pb_60}  `}>
			<div className={`${contact.contact_left_block} ${className}`}>
				<div
					data-aos="fade-in"
					data-aos-duration="1000"
					className={`${comon.title_30} ${comon.text_collor} ${comon.pb_10} ${comon.mt_10}`}
				>
					<h3>{parse(title)}</h3>
				</div>

				<div
					data-aos="fade-in"
					data-aos-duration="1000"
					className={`${comon.text_collor}  ${contact.text_wrap}`}
				>
					<p>{parse(paragraph)}</p>
				</div>
			</div>

			<div
				className={`${contact.contact_right_block}`}
				data-aos="fade-in"
				data-aos-duration="1000"
			>
				{/* <RequestQuotation button={button} whiteBtn={whiteBtn} classNameSecond={classNameSecond}
				 className={className} showAutoMargin={showAutoMargin} /> */}

				<ContactForm
					button={button}
					showAutoMargin={showAutoMargin}
					classNameSecond={classNameSecond}
					whiteBtn={whiteBtn}
				/>
{/* 
				<RequestQuotation 
				button={button}
				showAutoMargin={showAutoMargin}
				classNameSecond={classNameSecond}
				whiteBtn={whiteBtn}
				/> */}
			</div>
		</div>
	);
};

export default ContactPageSection;
