import { useRef, useEffect } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";
import { useInView } from "react-intersection-observer";
import Image from "next/image"; // Import Next.js Image component

gsap.registerPlugin(ScrollTrigger);

const ImageParallax = ({ src, alt, speed = 0.5, height = "500px" }) => {
  const imageRef = useRef(null);
  const containerRef = useRef(null);

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  useEffect(() => {
    if (inView) {
      const tl = gsap.timeline();
      tl.to(imageRef.current, {
        y: -speed * 100,
        ease: "none",
        scrollTrigger: {
          trigger: containerRef.current,
          start: "top bottom",
          end: "bottom top",
          scrub: true,
        },
      });
    }
  }, [inView, speed]);

  return (
    <div
    ref={(el) => {
      ref(el);
      containerRef.current = el;
    }}
    style={{
      position: "relative",
      overflow: "hidden",
      height: height, // Match the desired height
      width: "100%",  // Ensure it spans the full width
    }}
  >
    <div
      ref={imageRef}
      style={{
        position: "absolute",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
      }}
    >
      <Image
        src={src}
        alt={alt}
        layout="fill"
        objectFit="cover"
        quality={100}
      />
    </div>
  </div>
  
  );
};

export default ImageParallax;

// Usage Example
// npm install react-intersection-observer
// npm install gsap
// npm install next
// <ImageParallax src="/path/to/image.jpg" alt="Sample Image" speed={0.8} height="600px" />
