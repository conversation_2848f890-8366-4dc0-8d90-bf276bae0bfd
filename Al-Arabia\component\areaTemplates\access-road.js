import HeadMarquee from "@/component/HeadMarquee";
import React, { useState, useEffect } from "react";
import InnerBanner from "@/component/InnerBanner";
import comon from "@/styles/comon.module.scss";
import rtl from "@/styles/rtl.module.scss";
import Link from "next/link";
import Image from "next/image";
import Head from "next/head";
import { useRouter } from "next/router";
import Masonry from "react-masonry-css";
import style from "@/styles/terminals.module.scss";
import StartCampaign from "@/component/StartCampaign";
import parse from "html-react-parser";
import SliderBannerText from "@/component/SliderBannerText";
import ContactSection from "@/component/ContactSection";
import { Box, Modal } from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, FreeMode, Thumbs } from "swiper/modules";

import "swiper/css";
import "swiper/css/pagination";

const accessroad = ({ pageData }) => {
	const [tabSelect, setTabSelect] = useState();
	const [selectedIndex, setSelectedIndex] = useState(0);

	const scrollToSection = (sectionId) => {
		setTabSelect(sectionId);
		const section = document.getElementById(sectionId);
		if (section) {
			section.scrollIntoView({ behavior: "smooth" });
		}
	};

	const breakpointColumns = {
		default: 3,
		1366: 3,
		1100: 3,
		700: 2,
	};
	// const imageChunks = chunkArray(images, 10);

	const [open, setOpen] = useState(false);
	const [thumbsSwiper, setThumbsSwiper] = useState(null);
	const [imageSlides, setImageSlides] = useState([]);

	// Handle modal open and close
	const handleOpen = (index) => {
		setSelectedIndex(index);
		setOpen(true);
	};

	const handleClose = () => {
		setThumbsSwiper(null);
		setOpen(false);
	};

	const chunkArray = (arr, size) =>
		Array.from({ length: Math.ceil(arr.length / size) }, (_, i) =>
			arr.slice(i * size, i * size + size)
		);
	const imageChunks = chunkArray(pageData.acf.gallery_images, 10);

	return (
		<div>
			{/* <Head>
				<title>Access Road</title>
				<meta name="description" content="Generated by create next app" />
			</Head> */}
			<HeadMarquee />
			{pageData.acf.banner_details.description_field ? (
				<SliderBannerText
					title={pageData.acf.banner_details.title}
					paragraph={pageData.acf.banner_details.description_}
					details={pageData.acf.banner_details}
					className={"width_870"}
					bgColor={true}
					extra_height_class={true}
					full_height_banner={true}
				/>
			) : (
				<InnerBanner
					showLink={false}
					title={pageData.acf.banner_details.title}
					details={pageData.acf.banner_details}
				/>
			)}
			<div className={`${comon.wrap} ${comon.mb_50} ${comon.mt_35}  `}>
				<ul
					className={`${comon.mb_30} ${comon.detail_breadcrumb}  ${rtl.detail_breadcrumb} `}
				>
					<li>
						<Link href={"/"}>
							<div className={` ${comon.bread_icon}  `}>
								<Image
									src={"/images/breadcumb_home.svg"}
									height={30}
									width={30}
									alt=""
									quality={100}
								/>
							</div>
						</Link>
					</li>
					{Array.isArray(pageData?._embedded?.up) &&
						pageData._embedded.up[0]?.parent_slug &&
						pageData._embedded.up[0]?.parent_title && (
							<li>
								<Link href={pageData._embedded.up[0].parent_slug}>
									{pageData._embedded.up[0].parent_title}
								</Link>
							</li>
						)}

					{pageData.parent_title && (
						<li>
							<Link href={`${pageData.parent_slug}`}>
								{pageData.parent_title}
							</Link>
						</li>
					)}

					<li>
						<Link href={"/access-road"} className={` ${comon.active}`}>
							{pageData.title.rendered}
						</Link>
					</li>
				</ul>
			</div>

			<section className={`${comon.mt_30} ${comon.mb_30}`}>
				<div className={`${comon.wrap}`}>
					<div className={`${comon.center_text}`}>
						<div class={`${comon.title_30} ${comon.mb_10}`}>
							<h3>{parse(pageData.acf.description.Title)}</h3>
						</div>
						<>{parse(pageData.acf.description.description)}</>
					</div>
				</div>
			</section>

			<section
				className={`${comon.airport_gallery_main}`}
				data-aos="fade-in"
				data-aos-duration="1000"
			>
				{imageChunks?.map((chunk, groupIndex) => {
					let x = 1;
					return (
						<div className={`${comon.image_grid_layout}`} key={groupIndex}>
							{chunk.map((data, index) => {
								const content = (
									<div
										className={`grid_sec${x} ${comon.image_sec}`}
										key={index}
										onClick={() => handleOpen(groupIndex * 10 + index)}
									>
										<Image
											src={data}
											height={2400}
											width={2500}
											alt={`Image ${groupIndex * 10 + index + 1}`}
											quality={100}
										/>
									</div>
								);
								x++;
								if (x > 9) x = 1;
								return content;
							})}
						</div>
					);
				})}
			</section>

			<StartCampaign />

			<section className={`${comon.pt_60} `} style={{ background: "#E1E2E1" }}>
				<ContactSection
					title="Request for Quotation"
					paragraph="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore."
					button="Request a Quote"
					showAutoMargin={false}
				/>
			</section>

			<div>
				<Modal
					className="ModalBlockSlideSection"
					open={open}
					data-lenis-prevent="true"
					onClose={() => {
						setThumbsSwiper(false);
						setOpen(false);
					}}
				>
					<Box
						className={`${comon.popup_container}  ${comon.swiper_popup_container} ${comon.min_height}`}
					>
						<div
							onClick={() => {
								setThumbsSwiper(false);
								setOpen(false);
							}}
							className={`${comon.swiper_image} ${comon.swiper_popup_image} ${comon.swiper_image_contain} ${comon.swiper_image_cover}`}
						>
							<Image
								src={pageData.acf.gallery_images[selectedIndex]}
								alt={`Slide  `}
								quality={100}
								height={2400}
								width={2500}
							/>
							{/* <button
								className={comon.popup_close}
								onClick={() => {
									setThumbsSwiper(false);
									setOpen(false);
								}}
							>
								<Image
									src="/images/close_btn.svg"
									height={30}
									width={30}
									alt="Close"
								/>
							</button> */}
						</div>

						{/* <Swiper
							initialSlide={selectedIndex}
							spaceBetween={10}
							onSwiper={setThumbsSwiper}
							// navigation
							navigation={{
								prevEl: '.prev_btn',
								nextEl: '.next_btn'
							}}
							thumbs={{
								swiper:
									thumbsSwiper && !thumbsSwiper.destroyed ? thumbsSwiper : null,
							}}
							modules={[FreeMode, Navigation, Thumbs]}
							className="mySwiper2 ImageBlockSlide"
						>
							{pageData.acf.gallery_images.map((data, index) => (
								<SwiperSlide key={index}>
									<div className={`${comon.swiper_image} ${comon.swiper_image_contain} ${comon.swiper_image_cover}`}>
										<Image src={data} alt={`Slide ${index + 1}`}
										quality={100}
										height={2400}
											width={2500}
										/>
									</div>
								</SwiperSlide>
							))}
							<div className="thumb_swiper_slide_btn">
								<div className="prev_btn swiper_btn ">
									<Image
										src="/images/next_icon1.svg"
										height={30}
										width={30}
										alt="next"
									/>
								</div>
								<div className="next_btn swiper_btn">
									<Image
										src="/images/next_icon1.svg"
										height={30}
										width={30}
										alt="prev"
									/>
								</div>
							</div>
						</Swiper> */}

						{/* -----thumbs------ */}
						{/* <Swiper
							onSwiper={setThumbsSwiper}
							spaceBetween={10}
							slidesPerView={3}
							freeMode={true}
							watchSlidesProgress={true}
							modules={[FreeMode, Navigation, Thumbs]}
							className="mySwiper ImageBlockSlideThumbs"
							breakpoints={{
								500: {
									slidesPerView: 4,
								},
							}}
						>
							{pageData.acf.gallery_images.map((data, index) => (
								<SwiperSlide key={index}>
									<div
										className={`${comon.swiper_image_thumbs} ${comon.border}`}
									>
										<img src={data} alt={`Thumbnail ${index + 1}`} />
									</div>
								</SwiperSlide>
							))}
						</Swiper> */}
					</Box>
				</Modal>
			</div>
		</div>
	);
};

export default accessroad;
