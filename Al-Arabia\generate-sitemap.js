const fs = require("fs");
const path = require("path");

// Load environment variables in order of priority
// .env.local takes precedence over .env
if (fs.existsSync('.env.local')) {
  require('dotenv').config({ path: '.env.local' });
} else {
  require('dotenv').config();
}

const DOMAIN = process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000";

const staticPaths = [
   "/",
  "/about-us" ,
  "/contact-us",
  "/careers",
  "/csr",
  "/start-your-campaign",
  "/news",
  "/areas",
  "/all-around",
  "/privacy-policy",
  "/cookies-settings",
  "/sitemap",
];

async function generateSitemap() {
  const urls = staticPaths.map(
    (url) =>
      `<url>
        <loc>${DOMAIN}${url}</loc>
        <changefreq>daily</changefreq>
        <priority>0.8</priority>
      </url>`
  );

  const sitemapContent = `<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      ${urls.join("")}
    </urlset>`;

  fs.writeFileSync(path.join(__dirname, "public", "sitemap.xml"), sitemapContent);
  console.log("✅ Sitemap successfully generated!");
}

generateSitemap();
