@import "variable", "base", "mixin";

.img_left_block {
  width: 50%;
  position: relative;
  overflow: hidden;

  @media #{$media-700} {
    width: 100%;
    margin-bottom: 25px;
  }

  .latest_model_swiper_img {
    height: 100%;
    width: 100%;
    max-height: 380px !important;

    >img {
      height: auto;
      width: 100%;
      object-fit: cover;
      display: block;
    }
  }

  .swiper_btn_sec {
    position: absolute;
    width: 90%;
    left: 50%;
    top: 50%;
    z-index: 5;
    transform: translate(-50%, -50%);
    display: flex;
    justify-content: space-between;
    pointer-events: none;

    .swiper_btn {
      height: auto;
      width: 10px;
      cursor: pointer;
      pointer-events: all;

      >img {
        height: auto;
        width: 100%;
        display: block;
        object-fit: contain;
      }

      &.prev {
        transform: scaleX(-1);
      }

      @media #{$media-700} {
        width: 10px;
      }
    }
  }
}

:global(body.rtl) .img_left_block .swiper_btn_sec .swiper_btn.prev {
  transform: scaleX(1);
}

.txt_right_block {
  width: 50%;
  padding-left: 6%;

  p {
    max-width: 480px;
  }

  @media #{$media-700} {
    width: 100%;
    padding-left: 0;
    padding-right: 0;
  }
}

.align_center {
  align-items: center;
}

.season_product_title {
  h3 {
    color: #fff !important;
  }

  p {
    color: #fff;
  }

  @media #{$media-700} {
    text-align: center;
  }
}

.season_image_section {
  width: 100%;
}

.mobile_version_swiper {
  aspect-ratio: unset !important;
}

.mobile_version {
  width: 100%;
  position: relative;
  min-height: 250px;

  &.mobile_version_new {
    @media (max-width: 500px) {
      height: 250px;
      overflow: hidden;

      img {
        width: auto !important;
        height: 100% !important;
      }
    }
  }

  .season_image {
    width: 100%;
    // height: auto;
    height: 100%;

    >img {
      height: auto;
      width: 100%;
      object-fit: cover;
      display: block;
    }
  }

  .text {
    position: absolute;
    bottom: 0;
    z-index: 2;
    width: 100%;
    padding: 20px 5%;
    background-image: linear-gradient(to top, rgb(0, 0, 0), rgba(0, 0, 0, 0));

    h3 {
      color: white;
      @include rem(25);

      @media #{$media-500} {
        @include rem(20);
      }
    }
  }
}

.season_image_ul_new {
  margin: 0;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  column-gap: 20px;

  li {
    list-style: none;
    height: 493px;
    flex: 1 1 20%; // Default width as 16.6%
    position: relative;
    background-position: center center !important;
    background-size: auto 100% !important;
    // transition: flex 0.6s ease-in-out, background-size 0.6s ease-in-out;
    transition: flex 0.3s ease-in-out, background-size 0.5s ease-in-out;
    cursor: pointer;

    .text {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      padding-top: 30px;
      padding-bottom: 30px;
      background-image: linear-gradient(to top,
          rgba(0, 0, 0, 0.89) 10%,
          rgba(0, 128, 0, 0));

      h3 {
        @include rem(20);
        font-weight: 400;
        color: #fff;
        text-align: left;
        padding: 0 30px;
        margin-bottom: 0;
      }
    }

    &:hover {
      flex: 1 1 30%;
      background-size: auto 120% !important;
    }
  }

  &.second {
    li {
      height: 400px;

      @media (max-width:768px) {
        height: 300px;

      }
    }
  }

  &:hover li:not(:hover) {
    flex: 1 1 calc((100% - 30%) / 4); // Adjust width for remaining items
  }
}

.season_image_new {
  li:hover .hover-txt {
    height: 50px;
    transition-delay: 6s;
    transition: height 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.conter_wrap_outer {
  margin-left: auto;
  max-width: 230px;

  h3 {
    color: #fff;
    @include rem(20);
    font-weight: 400;
    margin-bottom: 20px;

    @media #{$media-700} {
      margin-bottom: 10px;
    }
  }

  .conter_wrap {
    width: 100%;

    column-gap: 10px;
    align-items: baseline;

    .conter_main {
      text-align: center;

      .conter_title {
        width: 50px;
        height: 50px;
        background-image: url(../public/images/counter-bg.png);
        background-repeat: no-repeat;
        background-size: cover;
        display: flex;
        justify-content: center;
        align-items: center;
        @include rem(30);
        font-weight: 400;
        color: #6758b6;
      }

      span {
        font-size: 10px;
        color: #fff;
      }
    }
  }

  @media #{$media-700} {
    width: 100%;
    max-width: 100%;
    margin-top: 25px;
    margin-bottom: 30px;
  }
}

.season_image_item_mob {
  aspect-ratio: 285 / 400;
  width: 75%;
}

.season_image_card {
  cursor: pointer;
  position: relative;
  max-height: 400px;

  .text {
    position: absolute;
    width: 100%;

    left: 50%;
    transform: translateX(-50%);
    bottom: 0px;
    color: #fff;
    padding: 30px 10%;

    h3 {
      font-family: var(--aller_lt);
      @include rem(20);
      font-weight: 300;
    }

    @media #{$media-768} {
      padding: 15px 10%;
    }
  }
}