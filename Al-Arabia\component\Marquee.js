import React, { useRef, useEffect } from "react";
import { gsap } from "gsap";
import styles from "@/styles/marquee.module.scss";
import parse from "html-react-parser";

const Marquee = ({
	content = ["This is a marquee"], // Accepts an array of text or image URLs
	speed = 5000, // Speed of scrolling
	direction = "left", // Direction: "left" or "rtl"
	className = "",
	font_size20 = false,
	font_weight = false,
	bg_black = false,
	bgColor, textColor
}) => {
	const marqueeRef = useRef(null);
	const marqueeContentRef = useRef(null);
	const animationRef = useRef(null);

	useEffect(() => {
		const marquee = marqueeRef.current;
		const content = marqueeContentRef.current;

		// Get content width
		const contentWidth = content.scrollWidth;
		const parentWidth = marquee.offsetWidth;

		// Calculate the minimum number of copies needed
		const minCopies = Math.ceil(parentWidth / contentWidth) + 1;

		// Duplicate content dynamically for smooth looping
		for (let i = 0; i < minCopies; i++) {
			content.innerHTML += content.innerHTML;
		}

		// Reset GSAP animation
		gsap.set(content, { x: direction === "left" ? 0 : -contentWidth });

		// Apply animation based on direction
		animationRef.current = gsap.to(content, {
			x: direction === "left" ? -contentWidth : contentWidth, // <-- FIXED
			duration: speed / 1000,
			repeat: -1,
			ease: "none",
			modifiers: {
				x: (x) =>
					direction === "left"
						? `${parseFloat(x) % contentWidth}px`
						: `${(parseFloat(x) % contentWidth) - contentWidth}px`, // <-- FIXED
			},
		});

		return () => {
			animationRef.current.kill();
		};
	}, [direction, speed]);

	// Pause on hover
	const handleMouseEnter = () => animationRef.current.pause();
	const handleMouseLeave = () => animationRef.current.play();

	return (
		<div
			className={`${styles.marqueeWrapper} ${className}   ${direction === "rtl" ? styles.rtl : ""
				}  ${bg_black == true && styles.black_bg
				}`}
			ref={marqueeRef}
			onMouseEnter={handleMouseEnter}
			onMouseLeave={handleMouseLeave}
			style={{backgroundColor:bgColor}}

		>
			<div
				className={`${styles.marqueeContent} ${font_size20 ? styles.font_20 : ""
					} ${font_weight ? styles.font_weight : ""}`}
				ref={marqueeContentRef}
			>
				{content.map((item, index) =>
					typeof item === "string" && item.startsWith("http") ? (
						<img
							key={index}
							src={item}
							alt={`Marquee item ${index}`}
							className={styles.marqueeImage}
						/>
					) : (
						<p key={index} className={styles.marqueeText} style={{color:textColor}}>
							{parse(item)}
						</p>
					)
				)}
			</div>
		</div>
		// <></>
	);
};

export default Marquee;
