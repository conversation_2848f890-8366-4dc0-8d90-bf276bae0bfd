@import "variable", "base", "mixin";

.careers_title_block_main {
    width: 100%;
}
.mob_hide{
    @media (max-width:700px){
        display: none;
    }
}


.career_ul {
    margin: 0;
    padding: 0;
    padding-bottom: 3px;

    >li {
        list-style: none;
        border-bottom: solid 1px #9c9c9c;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        position: relative;
        display: flex;
        flex-wrap: wrap;
        align-items: center;

        .career_list_left {
            width: calc(100% - 170px);
            transition: all 0.3s ease;

            @media #{$media-700} {
                margin-bottom: 20px;
                width: 100%;
            }
        }

        .career_list_right {
            width: auto;
            transition: all 0.3s ease;
        }

        &::after {
            content: "";
            display: block;
            // height: 100%;
            height: calc(100% + 2px);

            background: #6758b6;
            position: absolute;
            left: -2%;
            right: -2%;
            opacity: 0;
            transition: all 0.4s ease-out 0s;
            -moz-transition: all 0.4s ease-out 0s;
            -webkit-transition: all 0.4s ease-out 0s;
            -o-transition: all 0.4s ease-out 0s;
            z-index: -1;
        }

        &:hover {
            &::after {
                opacity: 1;

                // height: calc(100% + 2px);
            }

            * {
                color: #fff !important;
            }

            .career_list_right {
                transform: translateX(-10px);

                @media #{$media-1024} {

                    transform: unset;

                }
            }

            .career_list_left {

                transform: translateX(10px);

                @media #{$media-1024} {

                    transform: unset;

                }
            }

            img {
                filter: brightness(0) invert(1);
            }

            .buttion {
                background: #fff;
                color: #6758b6 !important;

                &:hover {
                    background: #e0daff;
                }
            }

            .career_list_left {
                li {
                    border-color: #fff;
                }
            }
        }
    }
}

.career_list_left {
    h5 {
        font-size: 17px;
        color: #3b3064;
        font-weight: unset;
        display: flex;
        flex-wrap: wrap;

        ul {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            margin: 0 10px;

            li {
                gap: 7px;
                color: #6758b6;
                padding: 0 20px;
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                font-size: 14px;
                border-right: solid 1px rgba(59, 48, 100, 0.2);
            }

            & :last-child {
                border: none;
            }

            @media #{$media-500} {
                margin: 0 -20px;
                width: 100%;
                margin-top: 10px;
            }
        }
    }
}

.bot_left_block {
    width: 40%;
    margin: 2px 0;

    ul {
        display: flex;
        align-items: center;
        li{
            width: 90%;
        }

    }

    h3 {
        color: #3b3064;
        @include rem(26);
        // padding: 0 25px;
        padding-right: 3%;

        br {
            display: none;

            @media #{$media-700} {
                display: none;
            }
        }

        // @media #{$media-700} {
        //     padding: 0 15px;
        // }
    }

    @media #{$media-1366} {
        margin: 0;
    }

    @media #{$media-700} {
        width: 100%;
        margin-bottom: 15px;
    }
}

.bot_right_block {
    width: 60%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    ul {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 10px;
        // margin-left: -2%;
        // margin-right: -2%;

        li {
            list-style: none;
            width: auto;
            // padding-left: 2%;
            // padding-right: 2%;

            p {
                font-family: var(--aller_lt);

                font-size: 17px;

                @media #{$media-1366} {
                    font-size: 16px;

                }

                @media #{$media-1024} {
                    font-size: 15px;

                }

                @media #{$media-700} {
                    font-size: 14px;

                }
            }

            h3 {
                @include rem(26);
                color: #3b3064;
                margin-bottom: 10px;

                @media #{$media-700} {
                    @include rem(30);
                }
            }

            @media #{$media-700} {
                width: 45%;
                padding-bottom: 10px;
            }
        }
    }

    @media #{$media-700} {
        width: 100%;
    }
}