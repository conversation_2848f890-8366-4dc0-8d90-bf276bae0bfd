// Install required dependencies first:
// npm install leaflet react-leaflet

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import L from "leaflet";
import { useEffect } from "react";

// Fix for missing marker icons in Leaflet
const fixLeafletIcons = () => {
  delete L.Icon.Default.prototype._getIconUrl;
  L.Icon.Default.mergeOptions({
    iconRetinaUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",
    iconUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",
    shadowUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png",
  });
};

const SnazzyMap = ({ center = [51.505, -0.09], zoom = 13, markers = [] }) => {
  useEffect(() => {
    fixLeafletIcons();
  }, []);

  return (
    <div style={{ height: "500px", width: "100%" }}>
      <MapContainer center={center} zoom={zoom} scrollWheelZoom={false} style={{ height: "100%", width: "100%" }}>
        {/* Snazzy Tiles */}
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        />

        {/* Dynamic Markers */}
        {markers.map((marker, index) => (
          <Marker key={index} position={marker.position}>
            <Popup>
              <strong>{marker.title}</strong>
              <br />
              {marker.description}
            </Popup>
          </Marker>
        ))}
      </MapContainer>
    </div>
  );
};

export default SnazzyMap;

// Usage Example
// import SnazzyMap from './components/SnazzyMap';
// <SnazzyMap
//   center={[40.7128, -74.0060]}
//   zoom={12}
//   markers={[
//     { position: [40.7128, -74.0060], title: "New York", description: "The Big Apple" },
//     { position: [40.73061, -73.935242], title: "Brooklyn", description: "Cool neighborhood" }
//   ]}
// />
