@import "variable", "base", "mixin";

.mapContainer {
  position: relative;
  // height: 793px;
  height: 660px;

  .map_right_block {
    background: #3b3064;
    width: 325px;
    position: absolute;
    right: 0%;
    top: 100px;
    z-index: 700;
    // border: solid 1px #c2bce2;
    padding: 1.5%;

    @media screen and (max-width: 1440px) {
      right: 9%;
      width: 23%;
    }

    @media screen and (max-width: 1024px) {
      max-width: 30%;
    }

    @media screen and (max-width: 820px) {
      max-width: 30%;
      width: 100%;
    }

    // @media screen and (max-width:700px) {
    @media screen and (max-width: 820px) {
      // position: unset;
      max-width: 100%;
      width: 90%;
      right: 50%;
      top: unset;
      bottom: 30px;
      transform: translateX(50%);

      padding: 6%;
    }
  }

  h5 {
    color: #fff;
    font-weight: 400;
    @include rem(26);
    margin-bottom: 20px;
    text-transform: none;

    @media screen and (max-width: 700px) {
      @include rem(23);

    }


  }

  .tab_ul {
    display: flex;
    flex-wrap: wrap;

    li {
      list-style: none;
      padding: 0 4%;
      color: #fff;
      @include rem(15);
      cursor: pointer;
      padding-bottom: 6px;
      text-transform: uppercase;
      opacity: 0.7;

      &.active {
        border-bottom: solid 1px #fff;
        // font-family: var(--aller_bd);
        opacity: 1;
        font-weight: 600;
      }

      @media #{$media-700} {
        margin: 0 4%;
        padding: 0;
        @include rem(17);
      }
    }

    @media #{$media-700} {
      margin-left: -4%;
      margin-right: -4%;
    }
  }

  .locationList {
    margin: 15px -3% 0px -12%;
    // max-height: 426px;
    max-height: 335px;
    overflow: auto;

    li {
      @include rem(16);
      cursor: pointer;
      list-style: none;
      color: #fff;
      padding-top: 15px;
      padding-bottom: 15px;
      border-bottom: solid 1px rgba($color: #ffffff, $alpha: 0.15);
      margin-left: 12%;
      margin-right: 12%;
      width: 76%;



      @media #{$media-820} {
        padding-top: 10px;
        padding-bottom: 10px;

        @include rem(17);

        width: fit-content;
        margin: 0;
        border: 1px solid rgba(167, 167, 167, 0.486);
        border-radius: 50px;
        padding: 6px 15px;

        &:hover {

          background-color: rgba(167, 167, 167, 0.13);

        }
      }
    }

    // @media #{$media-700} {
    @media #{$media-820} {
      overflow: hidden;
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin: 0;
      margin-top: 20px;
    }

    @media #{$media-700} {
      // max-height: 150px;
      max-height: 70px;
      overflow-y: auto;
      gap: 7px;

    }

    &::-webkit-scrollbar {
      background-color: transparent;
      transform: translateX(-200px) !important;
      width: 10px;

      @media #{$media-700} {
        width: 5px;
      }
    }

    &::-webkit-scrollbar-track {
      // margin-bottom: 200px !important;
      margin-bottom: 10px !important;

      background: url(../public/images/drager_line.svg) repeat-y center center;

      border-radius: 12px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #c2bce2;
      border: 1px solid #c2bce2;
      border-radius: 16px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: #dfdafa;
    }

    &::-webkit-scrollbar-button {
      display: none;
    }
  }

  // @media screen and (max-width:700px) {
  @media screen and (max-width: 820px) {
    height: 80dvh;
  }
}




.map_txt_block {
  position: absolute;
  left: 0%;
  z-index: 800;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  max-width: 30%;
  pointer-events: none;

  h3 {
    @include rem(30);
    font-family: var(--aller_lt);
    color: #fff;
    font-weight: 700;
    // line-height: 1.875rem;
    line-height: 125%;

    @media screen and (max-width: 820px) {
      // color: #3b3064;
    }
  }

  span {
    pointer-events: all;
  }

  @media screen and (max-width: 1440px) {
    left: 9%;
  }

  @media screen and (max-width: 1024px) {
    max-width: 30%;
  }

  // @media screen and (max-width:700px) {
  @media screen and (max-width: 820px) {
    // position: unset;
    width: 100%;
    max-width: unset;
    padding-left: 6%;
    padding-right: 6%;
    height: unset;
    left: 0%;
    top: 30px;
    padding-top: 35px;
    padding-bottom: 35px;
  }
}

.map_block_03 {
  height: 100%;
  width: 100%;
}

.map_block_04 {
  height: 100%;

  // @media screen and (max-width:700px) {
  @media screen and (max-width: 820px) {
    // height: 500px;
  }
}

.map_wrap {
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  position: absolute;
  width: 100%;
  left: 0;
  top: 0;
}

.map_absolute {
  position: absolute;
  width: 100%;
  height: 100%;

  // @media screen and (max-width:700px) {
  @media screen and (max-width: 820px) {
    // position: unset;
  }
}

.map_wrap_02 {
  max-width: 1200px;
  height: 100%;
  margin-left: auto;
  margin-right: auto;
}

@media screen and (max-width: 820px) {
  :global(body.rtl) .mapContainer .locationList {
    margin: 0 !important;
    margin-top: 20px !important;
  }

}

.product_pin_logo {
  height: 90px;
  width: 90px;

  >img {
    // height: 100%;
    width: 100%;
    display: block;
    object-fit: contain;
  }

  @media #{$media-1366} {
    height: 70px;
    width: 70px;
  }

  @media #{$media-1024} {
    height: 50px;
    width: 50px;
  }


}

.textBtn_zoomBtn_hide {
  @media #{$media-820} {

    display: none;
  }
}