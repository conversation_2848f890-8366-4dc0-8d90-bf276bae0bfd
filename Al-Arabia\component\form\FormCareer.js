import React, { useState, useEffect } from "react";
import comon from "@/styles/comon.module.scss";
import rtl from "@/styles/rtl.module.scss";
import Box from '@mui/material/Box';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import { useRouter } from "next/router";
import SelectDropDown from "@/component/SelectDropDown";
import contactF from "@/styles/contactForm.module.scss";
const FormCareer = ({ jobTitle, listName }) => {

  const { locale } = useRouter();
  const langCode = locale === 'ar' ? 'ar' : 'en';

  const [dropdown, setDropdown] = useState('1 week');

  const handleChange1 = (event) => {
    setDropdown(event.target.value);
  };

  const [fileName, setFileName] = useState("");
  const [formData1, setFormData] = useState({
    firstname: '',
    lastname: '',
    emailaddress: '',
    phonenumber: '',
    nationality: '',
    gender: '',
    age: '',
    major: '',
    working: '',
    lastjob: '',
    experience: '',
    jobtitle: jobTitle || '',
    resume: null,
  });
  const [validationErrors, setValidationErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [responseMessage, setResponseMessage] = useState('');
  const [sresponseMessage, setSresponseMessage] = useState('');
  const [fieldErrors, setFieldErrors] = useState({});


  const OptionData = [
    ...(jobTitle === "general"
      ? [{ value: locale === "ar" ? "لا شيء" : "None", label: locale === "ar" ? "لا شيء" : "None" }]
      : []),
    ...listName.map((item) => ({
      value: item.title.rendered,
      label: item.title.rendered,
    })),
  ];


  const handleDropdownChange = (value) => {
    setFormData((prev) => ({
      ...prev,
      jobtitle: value,
    }));
  };
  const handleFileChange = (event) => {
    const file = event.target.files[0];
    const filePath = event.target.value;
    //const fileName = filePath.replace(/.*(\/|\\)/, '');
    const extractedFileName = filePath.split(/(\\|\/)/g).pop();

    const maxFileSize = 2 * 1024 * 1024; // 2MB
    const supportedFileTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

    if (file) {
      if (!supportedFileTypes.includes(file.type)) {
        // console.log("tess")
        setValidationErrors({
          ...validationErrors,
          resume: langCode === 'ar'
            ? 'نوع الملف غير مدعوم. يرجى تحميل ملف PDF أو مستند Word.'
            : 'Unsupported file type. Please upload a PDF or Word document.'
        });
        setFileName('');
        return;
      }

      if (file.size > maxFileSize) {
        setValidationErrors({
          ...validationErrors,
          resume: langCode === 'ar'
            ? 'يتجاوز حجم الملف 2 ميجابايت.'
            : 'File size exceeds 2MB.'
        });
        setFileName('');
        return;
      }

      setFormData({ ...formData1, resume: file });
      setFileName(extractedFileName);
      setValidationErrors({ ...validationErrors, resume: '' });
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData1, [name]: value });
    setResponseMessage('');
    setValidationErrors((prevErrors) => ({
      ...prevErrors,
      [name]: '',
    }));

    if (name === "phonenumber") {
      const numericValue = value.replace(/\D/g, ""); // Remove non-numeric characters
      setFormData({ ...formData1, [name]: numericValue });
    } else {
      // For other fields, just update the state normally
      setFormData({ ...formData1, [name]: value });
    }
    if (name === "firstname") {
      //const alphabeticValue = value.replace(/[^a-zA-Z]/g, "");
      const alphabeticValue = value.replace(/[^a-zA-Z\s]/g, "");
      setFormData({ ...formData1, [name]: alphabeticValue });
    }
    else {
      setFormData({ ...formData1, [name]: value });
    }

    setResponseMessage('');
    setValidationErrors((prevErrors) => ({
      ...prevErrors,
      [name]: '',
    }));
    setFieldErrors({ ...fieldErrors, [name]: '' });
  };

  const formId = langCode === 'ar' ? 3396 : 923;
  const unitTag = langCode === 'ar' ? 'wpcf7-f3396-o1' : 'wpcf7-f923-o1';

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setResponseMessage('');
    setFieldErrors({}); // Reset field errors on submit

    const formData = new FormData();
    formData.append('firstname', formData1.firstname);
    formData.append('lastname', formData1.lastname);
    formData.append('phonenumber', formData1.phonenumber);
    formData.append('emailaddress', formData1.emailaddress);
    formData.append('nationality', formData1.nationality);
    formData.append('gender', formData1.gender);
    formData.append('age', formData1.age);
    formData.append('major', formData1.major);
    formData.append('working', formData1.working);
    formData.append('lastjob', formData1.lastjob);
    formData.append('experience', formData1.experience);
    formData.append('post', formData1.jobtitle);
    formData.append('resume', formData1.resume);
    formData.append('_wpcf7_unit_tag', unitTag);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_CONTACT_API_URL}/${formId}/feedback`, {
        method: 'POST',
        body: formData,
        redirect: 'follow',
      });

      const result = await response.json();
      setIsSubmitting(false);

      if (response.ok) {
        if (result.status === "validation_failed") {
          const fieldErrors = result.invalid_fields.reduce((acc, fieldError) => {
            acc[fieldError.field] = fieldError.message;
            return acc;
          }, {});
          setFieldErrors(fieldErrors);
          setTimeout(() => {
            setResponseMessage('');
            setFieldErrors({});
          }, 3000);
        } else if (result.status === "mail_sent") {
          setResponseMessage('');
          setSresponseMessage(result.message);
          setTimeout(() => {
            setSresponseMessage('');
          }, 3000);
          setFormData({
            firstname: '', lastname: '', phonenumber: '', emailaddress: '', jobtitle: '', resume: null,
            nationality: '', gender: '', age: '', major: '', working: '', lastjob: '', experience: ''

          });
          setFileName('');
        } else {
          setResponseMessage('An unexpected error occurred. Please try again.');
        }
      } else {
        setResponseMessage(result.message || langCode === 'ar' ? 'حدث خطأ ما. يرجى المحاولة مرة أخرى.' : 'Something went wrong. Please try again.');
      }
    } catch (error) {
      setIsSubmitting(false);
      setResponseMessage(langCode === 'ar' ? 'حدث خطأ ما. يرجى المحاولة مرة أخرى.' : 'Something went wrong. Please try again.');
    }
  };


  const Major = [
    { value: "Lorem", label: "Lorem" },
    { value: "Lorem1", label: "Lorem1" },
  ];
  const Nationality = [
    { value: "Saudi Arabia", label: "Saudi Arabia" },
    { value: "Afghanistan", label: "Afghanistan" },
    { value: "Albania", label: "Albania" },
    { value: "Algeria", label: "Algeria" },
    { value: "Argentina", label: "Argentina" },
    { value: "Armenia", label: "Armenia" },
    { value: "Australia", label: "Australia" },
    { value: "Austria", label: "Austria" },
    { value: "Azerbaijan", label: "Azerbaijan" },
    { value: "Bahrain", label: "Bahrain" },
    { value: "Bangladesh", label: "Bangladesh" },
    { value: "Belarus", label: "Belarus" },
    { value: "Belgium", label: "Belgium" },
    { value: "Belize", label: "Belize" },
    { value: "Bhutan", label: "Bhutan" },
    { value: "Bolivia", label: "Bolivia" },
    { value: "Bosnia and Herzegovina", label: "Bosnia and Herzegovina" },
    { value: "Botswana", label: "Botswana" },
    { value: "Brazil", label: "Brazil" },
    { value: "Brunei", label: "Brunei" },
    { value: "Bulgaria", label: "Bulgaria" },
    { value: "Cambodia", label: "Cambodia" },
    { value: "Cameroon", label: "Cameroon" },
    { value: "Canada", label: "Canada" },
    { value: "Caribbean", label: "Caribbean" },
    { value: "Chile", label: "Chile" },
    { value: "China", label: "China" },
    { value: "Colombia", label: "Colombia" },
    { value: "Congo (DRC)", label: "Congo (DRC)" },
    { value: "Costa Rica", label: "Costa Rica" },
    { value: "Côte d’Ivoire", label: "Côte d’Ivoire" },
    { value: "Croatia", label: "Croatia" },
    { value: "Cuba", label: "Cuba" },
    { value: "Czechia", label: "Czechia" },
    { value: "Denmark", label: "Denmark" },
    { value: "Djibouti", label: "Djibouti" },
    { value: "Dominican Republic", label: "Dominican Republic" },
    { value: "Ecuador", label: "Ecuador" },
    { value: "Egypt", label: "Egypt" },
    { value: "El Salvador", label: "El Salvador" },
    { value: "Eritrea", label: "Eritrea" },
    { value: "Estonia", label: "Estonia" },
    { value: "Ethiopia", label: "Ethiopia" },
    { value: "Faroe Islands", label: "Faroe Islands" },
    { value: "Finland", label: "Finland" },
    { value: "France", label: "France" },
    { value: "Georgia", label: "Georgia" },
    { value: "Germany", label: "Germany" },
    { value: "Greece", label: "Greece" },
    { value: "Greenland", label: "Greenland" },
    { value: "Guatemala", label: "Guatemala" },
    { value: "Haiti", label: "Haiti" },
    { value: "Honduras", label: "Honduras" },
    { value: "Hong Kong SAR", label: "Hong Kong SAR" },
    { value: "Hungary", label: "Hungary" },
    { value: "Iceland", label: "Iceland" },
    { value: "India", label: "India" },
    { value: "Indonesia", label: "Indonesia" },
    { value: "Iran", label: "Iran" },
    { value: "Iraq", label: "Iraq" },
    { value: "Ireland", label: "Ireland" },
    { value: "Israel", label: "Israel" },
    { value: "Italy", label: "Italy" },
    { value: "Jamaica", label: "Jamaica" },
    { value: "Japan", label: "Japan" },
    { value: "Jordan", label: "Jordan" },
    { value: "Kazakhstan", label: "Kazakhstan" },
    { value: "Kenya", label: "Kenya" },
    { value: "Korea", label: "Korea" },
    { value: "Kuwait", label: "Kuwait" },
    { value: "Kyrgyzstan", label: "Kyrgyzstan" },
    { value: "Laos", label: "Laos" },
    { value: "Latin America", label: "Latin America" },
    { value: "Latvia", label: "Latvia" },
    { value: "Lebanon", label: "Lebanon" },
    { value: "Libya", label: "Libya" },
    { value: "Liechtenstein", label: "Liechtenstein" },
    { value: "Lithuania", label: "Lithuania" },
    { value: "Luxembourg", label: "Luxembourg" },
    { value: "Macao SAR", label: "Macao SAR" },
    { value: "Macedonia, FYRO", label: "Macedonia, FYRO" },
    { value: "Malaysia", label: "Malaysia" },
    { value: "Maldives", label: "Maldives" },
    { value: "Mali", label: "Mali" },
    { value: "Malta", label: "Malta" },
    { value: "Mexico", label: "Mexico" },
    { value: "Moldova", label: "Moldova" },
    { value: "Monaco", label: "Monaco" },
    { value: "Mongolia", label: "Mongolia" },
    { value: "Montenegro", label: "Montenegro" },
    { value: "Morocco", label: "Morocco" },
    { value: "Myanmar", label: "Myanmar" },
    { value: "Nepal", label: "Nepal" },
    { value: "Netherlands", label: "Netherlands" },
    { value: "New Zealand", label: "New Zealand" },
    { value: "Nicaragua", label: "Nicaragua" },
    { value: "Nigeria", label: "Nigeria" },
    { value: "Norway", label: "Norway" },
    { value: "Oman", label: "Oman" },
    { value: "Pakistan", label: "Pakistan" },
    { value: "Panama", label: "Panama" },
    { value: "Paraguay", label: "Paraguay" },
    { value: "Peru", label: "Peru" },
    { value: "Philippines", label: "Philippines" },
    { value: "Poland", label: "Poland" },
    { value: "Portugal", label: "Portugal" },
    { value: "Puerto Rico", label: "Puerto Rico" },
    { value: "Qatar", label: "Qatar" },
    { value: "Réunion", label: "Réunion" },
    { value: "Romania", label: "Romania" },
    { value: "Russia", label: "Russia" },
    { value: "Rwanda", label: "Rwanda" },
    { value: "Senegal", label: "Senegal" },
    { value: "Serbia", label: "Serbia" },
    { value: "Singapore", label: "Singapore" },
    { value: "Slovakia", label: "Slovakia" },
    { value: "Slovenia", label: "Slovenia" },
    { value: "Somalia", label: "Somalia" },
    { value: "South Africa", label: "South Africa" },
    { value: "Spain", label: "Spain" },
    { value: "Sri Lanka", label: "Sri Lanka" },
    { value: "Sweden", label: "Sweden" },
    { value: "Switzerland", label: "Switzerland" },
    { value: "Syria", label: "Syria" },
    { value: "Taiwan", label: "Taiwan" },
    { value: "Tajikistan", label: "Tajikistan" },
    { value: "Thailand", label: "Thailand" },
    { value: "Trinidad and Tobago", label: "Trinidad and Tobago" },
    { value: "Tunisia", label: "Tunisia" },
    { value: "Türkiye", label: "Türkiye" },
    { value: "Turkmenistan", label: "Turkmenistan" },
    { value: "Ukraine", label: "Ukraine" },
    { value: "United Arab Emirates", label: "United Arab Emirates" },
    { value: "United Kingdom", label: "United Kingdom" },
    { value: "United States", label: "United States" },
    { value: "Uruguay", label: "Uruguay" },
    { value: "Uzbekistan", label: "Uzbekistan" },
    { value: "Venezuela", label: "Venezuela" },
    { value: "Vietnam", label: "Vietnam" },
    { value: "World", label: "World" },
    { value: "Yemen", label: "Yemen" },
    { value: "Zimbabwe", label: "Zimbabwe" }
  ];

  const Gender = locale === 'ar'
    ? [
      { value: "سعودي", label: "سعودي" },
      { value: "أخرى", label: "أخرى" }
    ]
    : [
      { value: "Male", label: "Male" },
      { value: "Female", label: "Female" }
    ];

  const isWorking = locale === 'ar'
    ? [
      { value: "نعم", label: "نعم" },
      { value: "لا", label: "لا" }
    ]
    : [
      { value: "Yes", label: "Yes" },
      { value: "No", label: "No" }
    ];

  const Experience = [
    { value: "0", label: "0" },
    { value: "1", label: "1" },
    { value: "2", label: "2" },
    { value: "3", label: "3" },
    { value: "4", label: "4" },
    { value: "5", label: "5" },
    { value: "6", label: "6" },
    { value: "7", label: "7" },
    { value: "8", label: "8" },
    { value: "9", label: "9" },
    { value: "10+", label: "10+" }
  ];

  const handleChangeDropdown = ({ name, value }) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };




  return (
    <>
      <form action="#" onSubmit={handleSubmit} noValidate>
        <div className={`${contactF.ul_wrap}`}>
          <ul className={`${contactF.contact_form_main_ul} ${rtl.contact_form_main_ul}`}>
            <li className={`${contactF.form_li}`}>
              <input
                className={`${contactF.input_fld} ${rtl.input_fld} ${contactF.pop_up_input_fld}`}
                type="text"
                placeholder={langCode === 'ar' ? 'الاسم الأول' : 'First Name'}
                value={formData1.firstname}
                onChange={handleChange}
                name="firstname"
                required
              />
              {fieldErrors.firstname && (
                <p
                  className={`${contactF.error_msg} ${rtl.error_msg}`}
                  style={{
                    fontSize: "14px",
                    color: "red",
                    marginTop: "5px",
                    paddingLeft: "10px",
                    display: "block",
                  }}
                >
                  {fieldErrors.firstname}
                </p>
              )}
            </li>

            <li className={`${contactF.form_li}`}>
              <input
                className={`${contactF.input_fld} ${rtl.input_fld} ${contactF.pop_up_input_fld}`}
                type="text"
                placeholder={langCode === 'ar' ? 'اسم العائلة' : 'Last Name'}
                value={formData1.lastname}
                onChange={handleChange}
                name="lastname"
                required
              />
              {fieldErrors.lastname && (
                <p
                  className={`${contactF.error_msg} ${rtl.error_msg}`}

                  style={{
                    fontSize: "14px",
                    color: "red",
                    marginTop: "5px",
                    paddingLeft: "10px",
                    display: "block",
                  }}
                >
                  {fieldErrors.lastname}
                </p>
              )}
            </li>

            <li className={`${contactF.form_li}`}>
              <input
                className={`${contactF.input_fld} ${rtl.input_fld} ${contactF.pop_up_input_fld}`}
                type="text"
                placeholder={locale == "ar" ? "البريد الإلكتروني" : "Email"}
                value={formData1.emailaddress}
                onChange={handleChange}
                name="emailaddress"
                required
              />
              {fieldErrors.emailaddress && (
                <p
                  className={`${contactF.error_msg} ${rtl.error_msg}`}

                  style={{
                    fontSize: "14px",
                    color: "red",
                    marginTop: "5px",
                    paddingLeft: "10px",
                    display: "block",
                  }}
                >
                  {fieldErrors.emailaddress}
                </p>
              )}
            </li>

            <li className={`${contactF.form_li}`}>
              <input
                className={`${contactF.input_fld} ${rtl.input_fld} ${contactF.pop_up_input_fld}`}
                type="tel"
                placeholder={locale == "ar" ? "رقم الهاتف" : "Phone"}
                value={formData1.phonenumber}
                onChange={(e) => {
                  const onlyNums = e.target.value.replace(/[^0-9]/g, '');
                  setFormData((prev) => ({
                    ...prev,
                    phonenumber: onlyNums,
                  }));
                }}
                name="phonenumber"
                required
                pattern="[0-9]*"
                style={{
                  direction: locale === 'ar' ? 'rtl' : '',
                }}
              />
              {fieldErrors.phonenumber && (
                <p
                  className={`${contactF.error_msg} ${rtl.error_msg}`}

                  style={{
                    fontSize: "14px",
                    color: "red",
                    marginTop: "5px",
                    paddingLeft: "10px",
                    display: "block",
                  }}
                >
                  {fieldErrors.phonenumber}
                </p>
              )}
            </li>

            <li className={`${contactF.form_li} ${contactF.pop_up_input_fld} custom_dropdown career_dropdown`}>
              <label>{locale == "ar" ? "الجنسية" : "Nationality"}</label>
              <SelectDropDown
                options={Nationality}
                name="nationality"
                value={formData1.nationality}
                onChange={(val) =>
                  setFormData((prev) => ({
                    ...prev,
                    nationality: val,
                  }))
                }
              />
              {fieldErrors.nationality && (
                <p
                  className={`${contactF.error_msg} ${rtl.error_msg}`}

                  style={{
                    fontSize: "14px",
                    color: "red",
                    marginTop: "5px",
                    paddingLeft: "10px",
                    display: "block",
                  }}
                >
                  {fieldErrors.nationality}
                </p>
              )}
            </li>

            <li className={`${contactF.form_li} ${contactF.pop_up_input_fld} custom_dropdown career_dropdown`}>
              <label>{locale == "ar" ? "الجنس" : "Gender"}</label>
              <SelectDropDown
                options={Gender}
                value={formData1.gender}
                onChange={(val) =>
                  setFormData((prev) => ({
                    ...prev,
                    gender: val,
                  }))
                }
              />
              {fieldErrors.gender && (
                <p
                  className={`${contactF.error_msg} ${rtl.error_msg}`}

                  style={{
                    fontSize: "14px",
                    color: "red",
                    marginTop: "5px",
                    paddingLeft: "10px",
                    display: "block",
                  }}
                >
                  {fieldErrors.gender}
                </p>
              )}
            </li>

            <li className={`${contactF.form_li}`}>
              <input
                className={`${contactF.input_fld} ${rtl.input_fld} ${contactF.pop_up_input_fld} ${contactF.pop_up_input_fld}`}
                type="number"
                placeholder={locale == "ar" ? "العمر" : "Age"}
                onChange={(e) => {
                  const onlyNums = e.target.value.replace(/[^0-9]/g, '');
                  setFormData((prev) => ({
                    ...prev,
                    age: onlyNums,
                  }));
                }}
                maxlength="2"
              />
              {fieldErrors.age && (
                <p
                  className={`${contactF.error_msg} ${rtl.error_msg}`}

                  style={{
                    fontSize: "14px",
                    color: "red",
                    marginTop: "5px",
                    paddingLeft: "10px",
                    display: "block",
                  }}
                >
                  {fieldErrors.age}
                </p>
              )}
            </li>

            <li className={`${contactF.form_li}`}>
              {/* <SelectDropDown
              options={Major}
              value={formData1.major}
              onChange={(val) =>
                setFormData((prev) => ({
                  ...prev,
                  major: val,
                }))
              }
            /> */}
              <input
                className={`${contactF.input_fld} ${rtl.input_fld} ${contactF.pop_up_input_fld} ${contactF.pop_up_input_fld}`}
                type="text"
                placeholder={locale == "ar" ? "التخصص " : "Major"}
                value={formData1.major}
                onChange={handleChange}
                name="major"
                required
              />
              {fieldErrors.major && (
                <p
                  className={`${contactF.error_msg} ${rtl.error_msg}`}

                  style={{
                    fontSize: "14px",
                    color: "red",
                    marginTop: "5px",
                    paddingLeft: "10px",
                    display: "block",
                  }}
                >
                  {fieldErrors.major}
                </p>
              )}
            </li>

            <li className={`${contactF.form_li} ${contactF.pop_up_input_fld} custom_dropdown career_dropdown`}>
              <label>{locale == "ar" ? 'ما المنصب الذي تتقدم إليه؟' : 'What position are you applying for'}</label>
              <SelectDropDown
                options={OptionData}
                value={formData1.jobtitle}
                onChange={handleDropdownChange}
              />
              {fieldErrors.post && (
                <p
                  className={`${contactF.error_msg} ${rtl.error_msg}`}

                  style={{
                    fontSize: "14px",
                    color: "red",
                    marginTop: "5px",
                    paddingLeft: "10px",
                    display: "block",
                  }}
                >
                  {fieldErrors.post}
                </p>
              )}
            </li>

            <li className={`${contactF.form_li} ${contactF.pop_up_input_fld} custom_dropdown career_dropdown`}>
              <label>{locale == "ar" ? "هل تعمل حاليا؟ " : "Are you Working"}</label>
              <SelectDropDown
                options={isWorking}
                value={formData1.working}
                onChange={(val) =>
                  setFormData((prev) => ({
                    ...prev,
                    working: val,
                  }))
                }
              />
              {fieldErrors.working && (
                <p
                  className={`${contactF.error_msg} ${rtl.error_msg}`}

                  style={{
                    fontSize: "14px",
                    color: "red",
                    marginTop: "5px",
                    paddingLeft: "10px",
                    display: "block",
                  }}
                >
                  {fieldErrors.working}
                </p>
              )}
            </li>

            <li className={`${contactF.form_li}`}>
              <input
                className={`${contactF.input_fld} ${rtl.input_fld} ${contactF.pop_up_input_fld} ${contactF.pop_up_input_fld}`}
                type="text"
                placeholder={locale == "ar" ? "المسمى الوظيفي الأخير " : "Last job Title"}
                name="lastjob"
                value={formData1.lastjob}
                onChange={handleChange}
              />
              {fieldErrors.lastjob && (
                <p
                  className={`${contactF.error_msg} ${rtl.error_msg}`}

                  style={{
                    fontSize: "14px",
                    color: "red",
                    marginTop: "5px",
                    paddingLeft: "10px",
                    display: "block",
                  }}
                >
                  {fieldErrors.lastjob}
                </p>
              )}
            </li>

            <li className={`${contactF.form_li} ${contactF.pop_up_input_fld} custom_dropdown career_dropdown`}>
              <label>{locale == "ar" ? "سنوات الخبرة" : "Years of experience"}</label>
              <SelectDropDown
                options={Experience}
                value={formData1.experience}
                onChange={(val) =>
                  setFormData((prev) => ({
                    ...prev,
                    experience: val,
                  }))
                }
              />
              {fieldErrors.experience && (
                <p
                  className={`${contactF.error_msg} ${rtl.error_msg}`}

                  style={{
                    fontSize: "14px",
                    color: "red",
                    marginTop: "5px",
                    paddingLeft: "10px",
                    display: "block",
                  }}
                >
                  {fieldErrors.experience}
                </p>
              )}
            </li>
          </ul>
        </div>

        <div className={`${comon.w_100} ${comon.pt_15} ${contactF.popup_btn_sec}`}>
          <div className={`${contactF.upload_sec} ${rtl.arb_font}`}>
            {fileName ? fileName : locale === "ar" ? "قم بتحميل سيرتك الذاتية" : "Upload your resume"}
            <label className={rtl.arb_font} htmlFor="inputUpload">
              <input type="file" id="inputUpload"  hidden onChange={handleFileChange} name="resume" />
              {locale == 'ar' ? 'رفع الملفات' : 'Upload'}
            </label>
          </div>

          <input
            type="submit"
            className={`${comon.buttion} ${comon.but_h_02} ${contactF.contact_but} ${comon.but_fill}`}
            value={locale == 'ar' ? 'إرسال' : 'Submit'}
          />
        </div>

        {fieldErrors.resume && (
          <p
            className={`${contactF.error_msg} ${contactF.resume_error_msg} ${rtl.error_msg}`}

            style={{
              fontSize: "14px",
              color: "red",
              marginTop: "5px",
              paddingLeft: "10px",
              display: "block",
            }}
          >
            {fieldErrors.resume}
          </p>
        )}
        {validationErrors.resume && (
          <span
            className={`${contactF.error_msg}  ${contactF.resume_error_msg} ${rtl.error_msg}`}

            style={{
              fontSize: "14px",
              color: "red",
              marginTop: "15px",
              // paddingLeft: "10px",
              display: "block",
            }}
          >
            {validationErrors.resume}
          </span>
        )}
        {responseMessage && (
          <p
            className={`${contactF.form_response_msg} ${rtl.form_response_msg}  `}
            style={{ color: "red", textAlign: "center" }}>
            {responseMessage}
          </p>
        )}
        {sresponseMessage && (
          <p
            className={`${contactF.form_response_msg} ${rtl.form_response_msg} `}
            style={{ color: "green", textAlign: "center" }}>
            {sresponseMessage}
          </p>
        )}
      </form>
    </>


  )
}

export default FormCareer
