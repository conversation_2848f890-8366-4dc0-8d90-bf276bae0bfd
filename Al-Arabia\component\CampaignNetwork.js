import React, { useState, useEffect } from "react";
import campaign from "@/styles/campaign.module.scss";
import comon from "@/styles/comon.module.scss";
import rtl from "@/styles/rtl.module.scss";
import parse from "html-react-parser";

const CampaignNetwork = ({ darkMode = false, pageData, onSelectionChange }) => {
  const [selected, setSelected] = useState({});
  const [disabledState, setDisabledState] = useState({});

  useEffect(() => {
    const updateDisabledState = () => {
      const newDisabledState = {};
      const updatedSelected = { ...selected };
      let hasSelectionChanged = false;

      if (!pageData?.acf?.form_items?.network_list) {
        return;
      }

      const selectedLocationsRaw = selected[1]
        ? Object.keys(selected[1]).filter((key) => selected[1][key])
        : [];

      const selectedLocations = selectedLocationsRaw.map((loc) =>
        loc.toLowerCase().trim()
      );

      pageData.acf.form_items.network_list.forEach((networkGroup, groupIndex) => {
        if (!networkGroup || !Array.isArray(networkGroup.choose_value_or_title)) {
          return;
        }

        networkGroup.choose_value_or_title.forEach((item) => {
          if (item.acf_fc_layout === "value") {
            const itemValue = item.item_value.toLowerCase().trim();
            let isDisabled = false;

            if (item.disabling_rules && Array.isArray(item.disabling_rules)) {
              isDisabled = item.disabling_rules.some((rule) => {
                if (!rule || !rule.disabling_condition_type || !rule.rule_type) {
                  return false;
                }

                if (rule.disabling_condition_type === "locations_selected") {
                  const requiredLocations = rule.required_locations
                    .split(",")
                    .map((loc) => loc.toLowerCase().trim())
                    .filter(loc => loc);

                  switch (rule.rule_type) {
                    case "disable_if_any_selected":
                      return requiredLocations.some((loc) => selectedLocations.includes(loc));
                    case "disable_if_none_selected":
                      return requiredLocations.every((loc) => !selectedLocations.includes(loc));
                    case "disable_if_all_selected":
                      return requiredLocations.every((loc) => selectedLocations.includes(loc));
                    default:
                      return false;
                  }
                }
                return false;
              });
            }

            newDisabledState[groupIndex] = {
              ...newDisabledState[groupIndex],
              [item.item_value]: isDisabled,
            };

            if (isDisabled && updatedSelected[groupIndex]?.[item.item_value]) {
              updatedSelected[groupIndex] = {
                ...updatedSelected[groupIndex],
                [item.item_value]: false,
              };
              hasSelectionChanged = true;
            }
          }
        });
      });

      setDisabledState(newDisabledState);
      if (hasSelectionChanged) {
        setSelected(updatedSelected);
      }
    };

    updateDisabledState();
  }, [selected, pageData]);

  const handleChange = (groupId, id, value) => {
    setSelected((prev) => {
      const newSelected = {
        ...prev,
        [groupId]: {
          ...prev[groupId],
          [id]: value,
        },
      };

      const currentGroup = pageData.acf.form_items.network_list[groupId];
      if (currentGroup) {
        const groupItems = currentGroup.choose_value_or_title;

        // Find the title that this 'id' (item_value) belongs to
        let parentTitleIndex = -1;
        for (let i = 0; i < groupItems.length; i++) {
          if (groupItems[i].acf_fc_layout === "title") {
            parentTitleIndex = i;
          } else if (groupItems[i].item_value === id) {
            // Found the current item, break and use the last found title index
            break;
          }
        }

        if (parentTitleIndex !== -1) {
          const parentTitleItem = groupItems[parentTitleIndex];
          let valuesUnderParentTitle = [];
          for (let i = parentTitleIndex + 1; i < groupItems.length; i++) {
            if (groupItems[i].acf_fc_layout === "title") {
              break; // Stop if another title is encountered
            }
            if (groupItems[i].acf_fc_layout === "value") {
              valuesUnderParentTitle.push(groupItems[i].item_value);
            }
          }

          // Check if any of the values directly under this parent title are selected
          const anyDirectChildValueSelected = valuesUnderParentTitle.some(
            (val) => newSelected[groupId]?.[val]
          );

          // Update the parent title's selection based on its direct children
          newSelected[groupId] = {
            ...newSelected[groupId],
            [parentTitleItem.item_title]: anyDirectChildValueSelected,
          };
        } else {
            // If the item doesn't have a specific parent title (like Digital/Static)
            // We need to check if it's the first group (index 0) where the title acts as "select all"
            if (groupId === 0) {
                const titleItem = groupItems.find(item => item.acf_fc_layout === "title");
                if (titleItem) {
                    const allValuesSelected = groupItems.every(item =>
                        item.acf_fc_layout === "value" ? newSelected[groupId]?.[item.item_value] : true
                    );
                    newSelected[groupId] = {
                        ...newSelected[groupId],
                        [titleItem.item_title]: allValuesSelected,
                    };
                }
            }
        }
      }

      const selectedValues = Object.entries(newSelected[groupId])
        .filter(([, checked]) => checked)
        .map(([key]) => key);

      onSelectionChange(groupId, selectedValues);

      return newSelected;
    });
  };

  const handleSelectGroup = (groupId, titleIndex, value) => {
    setSelected((prev) => {
      const group = pageData.acf.form_items.network_list[groupId];
      const items = group.choose_value_or_title;

      const updatedSelected = { ...(prev[groupId] || {}) }; // Use prev[groupId] here

      const currentTitle = items[titleIndex];
      if (currentTitle.acf_fc_layout === "title") {
        updatedSelected[currentTitle.item_title] = value;
      }

      for (let i = titleIndex + 1; i < items.length; i++) {
        const nextItem = items[i];
        if (nextItem.acf_fc_layout === "title") break;

        const isDisabled = disabledState?.[groupId]?.[nextItem.item_value] || false;
        if (!isDisabled) {
          updatedSelected[nextItem.item_value] = value;
        } else if (value) {
          updatedSelected[nextItem.item_value] = false;
        }
      }

      const selectedValues = Object.entries(updatedSelected)
        .filter(([, checked]) => checked)
        .map(([key]) => key);

      onSelectionChange(groupId, selectedValues);

      return {
        ...prev,
        [groupId]: updatedSelected,
      };
    });
  };

  return (
    <>
      <div
        data-aos="fade-in"
        data-aos-duration="1000"
        className={`${comon.title_30} ${comon.mb_40}`}
      >
        <h3>{parse(pageData.acf.section1)}</h3>
      </div>

      <div
        className={`${campaign.select_network_block} ${campaign.first_element} ${
          darkMode ? campaign.dark_mode : ""
        } ${campaign.block_style_01}`}
      >
        <ul className={`${campaign.list_ul} ${rtl.campaign_list_ul} ${rtl.special_label} ${campaign.special_label}`}>
          {/* Render the first group (index 0) */}
          {pageData.acf.form_items.network_list[0]?.choose_value_or_title.map((subItem, index) => {
            const isDisabled = disabledState?.[0]?.[subItem.item_value] || false;

            if (subItem.acf_fc_layout === "title") {
              return (
                <li key={index} style={{ minWidth: '125px' }}>
                  <label className={` ${campaign.sub_label}`}>
                    <input
                      className={campaign.label_checkbox}
                      type="checkbox"
                      id={`group0-title${index}`}
                      name="group0"
                      value={subItem.item_title}
                      hidden
                      onChange={(e) =>
                        handleSelectGroup(0, index, e.target.checked)
                      }
                      checked={selected[0]?.[subItem.item_title] || false}
                    />
                    <label
                      htmlFor={`group0-title${index}`}
                      className={`${campaign.label_checkbox_input}`}
                    ></label>
                    {subItem.item_title}
                  </label>
                </li>
              );
            } else {
              return (
                <li key={`value-${index}`}>
                  <label
                    style={isDisabled ? { opacity: '0.5', cursor: 'not-allowed' } : {}}
                  >
                    <input
                      className={campaign.label_checkbox}
                      type="checkbox"
                      id={`group0-item${index}`}
                      name="group0"
                      value={subItem.item_value}
                      hidden
                      checked={selected[0]?.[subItem.item_value] || false}
                      onChange={(e) =>
                        handleChange(0, subItem.item_value, e.target.checked)
                      }
                      disabled={isDisabled}
                    />
                    <label
                      htmlFor={`group0-item${index}`}
                      className={campaign.label_checkbox_input}
                      style={isDisabled ? { cursor: 'not-allowed' } : {}}
                    ></label>
                    {subItem.item_value}
                  </label>
                </li>
              );
            }
          })}
        </ul>
      </div>

      <ul className={`${comon.w_100} ${comon.d_flex_wrap} ${comon.justify_space_bet}`}>
        {/* Render groups 1 and 2 (Location and Format) */}
        {pageData.acf.form_items.network_list.slice(0, 2).map((item, groupIndex) => {

          if (groupIndex === 0) {
            return null
          }

          const hasTitleInGroup = item.choose_value_or_title.some(
            (subItem) => subItem.acf_fc_layout === "title"
          );

          return (
            <li
              key={groupIndex}
              className={`${campaign.select_network_block} ${
                darkMode ? campaign.dark_mode : ""
              } ${campaign.block_style_01}`}
            >
              <span className={campaign.title_lable}>{item.type}</span>
              <ul className={campaign.list_ul}>
                {item.choose_value_or_title.map((subItem, index) => {
                  const isDisabled = disabledState?.[groupIndex]?.[subItem.item_value] || false;

                  if (subItem.acf_fc_layout === "title") {
                    return (
                      <React.Fragment key={`title-${groupIndex}-${index}`}>
                        {index !== 0 && (
                          <span style={{ display: 'block', width: '100%' }}> </span>
                        )}
                        <li>
                          <label>
                            <input
                              className={campaign.label_checkbox}
                              type="checkbox"
                              id={`group${groupIndex}-title${index}`}
                              name={`group${groupIndex}`}
                              value={subItem.item_title}
                              hidden
                              onChange={(e) =>
                                handleSelectGroup(groupIndex, index, e.target.checked)
                              }
                              checked={selected[groupIndex]?.[subItem.item_title] || false}
                            />
                            <label
                              htmlFor={`group${groupIndex}-title${index}`}
                              className={campaign.label_checkbox_input}
                            ></label>
                            {subItem.item_title}
                          </label>
                        </li>
                        <span style={{ display: 'block', width: '100%' }}> </span>
                      </React.Fragment>
                    );
                  } else {
                    return (
                      <li key={`value-${groupIndex}-${index}`}>
                        <label
                          className={`${hasTitleInGroup ? campaign.sub_label : ''}`}
                          style={isDisabled ? { opacity: '0.5', cursor: 'not-allowed' } : {}}
                        >
                          <input
                            className={campaign.label_checkbox}
                            type="checkbox"
                            id={`group${groupIndex}-item${index}`}
                            name={`group${groupIndex}`}
                            value={subItem.item_value}
                            hidden
                            checked={selected[groupIndex]?.[subItem.item_value] || false}
                            onChange={(e) =>
                              handleChange(groupIndex, subItem.item_value, e.target.checked)
                            }
                            disabled={isDisabled}
                          />
                          <label
                            htmlFor={`group${groupIndex}-item${index}`}
                            className={campaign.label_checkbox_input}
                            style={isDisabled ? { cursor: 'not-allowed' } : {}}
                          ></label>
                          {subItem.item_value}
                        </label>
                      </li>
                    );
                  }
                })}
              </ul>
            </li>
          );
        })}
        <div className={campaign.campaign_wrap_block}>

          {pageData.acf.form_items.network_list.map((item, groupIndex) => {

            if (groupIndex === 0 || groupIndex === 1) {
              return null
            }

            const hasTitleInGroup = item.choose_value_or_title.some(
              (subItem) => subItem.acf_fc_layout === "title"
            );

            return (
              <li
                key={groupIndex}
                className={`${campaign.select_network_block} ${
                  darkMode ? campaign.dark_mode : ""
                } ${campaign.block_style_01}`}
              >
                <span className={campaign.title_lable}>{item.type}</span>
                <ul className={campaign.list_ul}>
                  {item.choose_value_or_title.map((subItem, index) => {
                    const isDisabled = disabledState?.[groupIndex]?.[subItem.item_value] || false;

                    if (subItem.acf_fc_layout === "title") {
                      return (
                        <React.Fragment key={`title-${groupIndex}-${index}`}>
                          {index !== 0 && (
                            <span style={{ display: 'block', width: '100%' }}> </span>
                          )}
                          <li>
                            <label>
                              <input
                                className={campaign.label_checkbox}
                                type="checkbox"
                                id={`group${groupIndex}-title${index}`}
                                name={`group${groupIndex}`}
                                value={subItem.item_title}
                                hidden
                                onChange={(e) =>
                                  handleSelectGroup(groupIndex, index, e.target.checked)
                                }
                                checked={selected[groupIndex]?.[subItem.item_title] || false}
                              />
                              <label
                                htmlFor={`group${groupIndex}-title${index}`}
                                className={campaign.label_checkbox_input}
                              ></label>
                              {subItem.item_title}
                            </label>
                          </li>
                          <span style={{ display: 'block', width: '100%' }}> </span>
                        </React.Fragment>
                      );
                    } else {
                      return (
                        <li key={`value-${groupIndex}-${index}`}>
                          <label
                            className={`${hasTitleInGroup ? campaign.sub_label : ''}`}
                            style={isDisabled ? { opacity: '0.5', cursor: 'not-allowed' } : {}}
                          >
                            <input
                              className={campaign.label_checkbox}
                              type="checkbox"
                              id={`group${groupIndex}-item${index}`}
                              name={`group${groupIndex}`}
                              value={subItem.item_value}
                              hidden
                              checked={selected[groupIndex]?.[subItem.item_value] || false}
                              onChange={(e) =>
                                handleChange(groupIndex, subItem.item_value, e.target.checked)
                              }
                              disabled={isDisabled}
                            />
                            <label
                              htmlFor={`group${groupIndex}-item${index}`}
                              className={campaign.label_checkbox_input}
                              style={isDisabled ? { cursor: 'not-allowed' } : {}}
                            ></label>
                            {subItem.item_value}
                          </label>
                        </li>
                      );
                    }
                  })}
                </ul>
              </li>
            );
          })}
        </div>
      </ul>
    </>
  );
};

export default CampaignNetwork;