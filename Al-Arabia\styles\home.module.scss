@import "variable", "base", "mixin";

.home_txt_01 {
  width: 42.5%;

  h1 {
    color: #fff;
    @include rem(26);
    font-weight: unset;
    font-family: var(--aller_bd);
    // line-height: 1.1rem;
    padding-right: 10px;

    span {
      font-family: var(--aller_lt);
      font-weight: unset;
      line-height: 130%;
      // @include rem(17);
      font-size: 17px;
      margin-top: 13px;
      display: inline-block;

      @media #{$media-1366} {
        font-size: 16px;
      }

      @media #{$media-1024} {
        font-size: 15px;
      }

      @media #{$media-820} {
        br {
          display: none;
        }
      }

      @media #{$media-700} {
        font-size: 14px;
      }
    }

    @media #{$media-1024} {
      @include rem(30);
    }

    @media #{$media-700} {
      @include rem(25);
    }
  }

  @media #{$media-820} {
    width: 100%;
    text-align: center;
  }
}

:global(body.rtl) {
  .home_txt_01 {
    h1 {
      padding-right: unset;
      padding-left: 10px;
    }
  }
}

.home_txt_02 {
  width: 57%;

  @media #{$media-820} {
    width: 100%;
    margin-top: 25px;
    margin-bottom: 6px;
  }

  @media #{$media-820} {
    margin-top: 0;
  }
}

.home_txt_counder {
  display: flex;
  flex-wrap: wrap;
  margin-left: -8%;
  margin-right: -8%;

  li {
    position: relative;
    padding-left: 7%;
    padding-right: 7%;
    width: 33%;

    h2 {
      color: #fff;
      @include rem(26);
      margin-bottom: 8px;
      font-family: var(--aller_rg), var(--arbfonts);

      @media #{$media-1024} {
        @include rem(30);
      }

      @media #{$media-700} {
        @include rem(25);
      }
    }

    p {
      font-size: 17px;
      color: #fff;
      font-weight: 200;
      font-family: var(--aller_lt);
      line-height: 1.3rem;

      @media #{$media-1024} {
        line-height: 110%;
        font-size: 15px;
      }
    }

    &::after {
      content: "";
      display: block;
      width: 1px;
      // height: 120px;
      height: 130px;
      background: #fff;
      position: absolute;
      right: 0;
      // top: -20px;
      top: -20px;
      -moz-transform: rotate(28deg);
      -webkit-transform: rotate(28deg);
      -o-transform: rotate(28deg);
      -ms-transform: rotate(28deg);
      transform: rotate(28deg);

      @media #{$media-1024} {
        height: 100%;
        top: -1%;
      }

      @media #{$media-700} {
        height: 130px;
        top: -15px;
        display: none;
      }
    }

    @media #{$media-700} {
      width: 100%;
      text-align: center;
      border-top: solid 1px #7f6fcf;
      padding-top: 15px;
      margin-top: 15px;
    }
  }

  &> :last-child {
    &::after {
      display: none;
    }
  }

  @media #{$media-700} {
    margin-left: 0;
    margin-right: 0;
  }
}

.corporate_block {
  background: rgba(103, 88, 182, 0.9);
  padding: 4% 5%;
  width: 37%;

  h3 {
    font-family: var(--aller_rg);
    @include rem(30);
    color: #fff;
    font-weight: 400;
    font-weight: unset;

    @media #{$media-700} {
      font-size: 1.75rem;
      font-family: "Aller", sans-serif;
    }

    br {
      @media #{$media-700} {
        display: none;
      }
    }
  }

  @media #{$media-700} {
    width: 100%;
  }
}

.slider_block {
  width: 45% !important;

  @media #{$media-700} {
    width: 75% !important;
  }
}

.slider_block_txt {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 100;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  padding: 5%;
  background-image: linear-gradient(to top,
      rgba(0, 0, 0, 0.801) 10%,
      rgba(0, 128, 0, 0) 40%);

  h4 {
    font-family: var(--aller_lt);
    @include rem(20);
    color: #fff;
    font-weight: 400;
  }
}

.bottom_row_corporate {
  border-top: solid 1px #fff;
  padding-top: 15px;

  p {
    font-size: 14px;
    font-family: var(--aller_lt);
    color: #fff;
  }

  h6 {
    font-size: 15px;
    color: #fff;
    font-weight: 300;
    margin-top: 12px;
    line-height: 26px;
    font-family: var(--aller_lt);
    text-transform: none;

    @media #{$media-700} {
      line-height: 18px;
      font-size: 14px;
      margin-top: 5px;
    }
  }
}

.insight_left_block {
  width: 32%;

  @media #{$media-700} {
    width: 100%;
  }

  @media #{$media-min-1366} {
    margin-top: -10px;
  }
}

.insight_right_block {
  width: 62%;

  @media #{$media-700} {
    width: 100%;
  }
}

.news_list_ul {
  margin: 0;
  padding: 0;

  li {
    padding-left: 0%;
    padding-right: 0%;
    list-style: none;
    width: 100%;
    max-width: 692px;
    // border-bottom: solid 1px #9C9C9C;
    padding-top: 30px;
    padding-bottom: 29px;
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    .news_date {
      font-size: 15px;

      margin-bottom: 20px;
      min-height: 18px;
      display: inline-flex;
      // color: #3B3064;
      color: #6758b6;
      font-style: italic;
      transition: all 0.5s ease;

      @media #{$media-1024} {
        min-height: unset;
      }

      @media #{$media-1000} {
        @include rem(20);
      }

      @media #{$media-700} {
        margin-bottom: 5px;
        font-size: 12px;
      }
    }

    p {
      transition: all 0.5s ease;
      width: 100%;

      // @include rem(20);
      @include rem(17);
      color: #3b3064;

      @media #{$media-1000} {
        @include rem(22);
      }

      @media #{$media-700} {
        @include rem(23);
        line-height: 2rem;
        width: 95%;
      }
    }

    a {
      margin-top: 20px;
      color: #ffffff;
      font-size: 16px;
      font-weight: 400;
      opacity: 0;

      @media #{$media-1280} {
        font-size: 15px;
        margin-top: 15px;
      }

      @media #{$media-1024} {
        opacity: 1;
        color: #3b3064;
      }

      @media #{$media-820} {
        font-size: 14px;
        margin-top: 10px;
      }
    }

    &::after {
      display: block;

      // margin-top: 30px;
      content: "";
      height: 100%;
      background: #6758b6;
      position: absolute;
      z-index: -1;
      // left: -4%;
      // right: -4%;
      opacity: 0;

      left: -6%;
      right: -7%;

      transition: all 0.4s ease-out 0s;
      -moz-transition: all 0.4s ease-out 0s;
      -webkit-transition: all 0.4s ease-out 0s;
      -o-transition: all 0.4s ease-out 0s;

      @media #{$media-1280} {
        left: -4%;
        right: -4%;
      }

      @media #{$media-700} {
        margin-top: 0;
        left: -3%;
        right: -3%;
      }

      @media #{$media-500} {
        left: 0;
        right: 0;
      }
    }

    &::before {
      display: block;
      position: absolute;

      width: 100%;
      content: "";
      height: 1px;
      background: #9c9c9c;
      z-index: 1;

      left: 0;
      bottom: 0;

      transition: all 0.4s ease-out 0s;
      -moz-transition: all 0.4s ease-out 0s;
      -webkit-transition: all 0.4s ease-out 0s;
      -o-transition: all 0.4s ease-out 0s;
    }

    &:hover {

      p,
      a,
      .news_date {
        color: #fff;
        opacity: 1;
        transform: translateX(10px) !important;
      }

      &::after {
        opacity: 1;
        // height: 101%;

        // @media #{$media-1280} {
        //   height: 104%;
        // }
      }

      &::before {
        bottom: 20%;
        opacity: 0;
      }

      .news_date {
        // font-size: 80%;

        @media #{$media-1024} {
          @include rem(20);
        }
      }
    }

    &:first-child {
      padding-top: 0px;

      &::before {
        bottom: -2px;

        @media #{$media-1440} {
          bottom: 1px;
        }
      }

      &:after {
        height: 118%;

        @media #{$media-700} {
          height: 100%;
        }
      }

      &::after {
        // transform: translateY(10px);

        @media #{$media-1280} {
          transform: unset;
        }
      }
    }

    &:nth-child(2):hover {
      &::after {
        height: 99%;

        @media #{$media-1440} {
          height: 104%;
          bottom: -3px;
        }
      }
    }

    &:last-child {
      @media #{$media-min-1366} {
        min-height: 185px;
      }

      &::before {
        bottom: 7px;

        @media #{$media-1440} {
          bottom: 10px;
        }

        @media #{$media-1280} {
          bottom: 0;
        }
      }

      &::after {
        transform: translateY(-5px);

        @media #{$media-1440} {
          transform: translateY(-15px);
        }

        @media #{$media-1280} {
          transform: unset;
        }
      }
    }

    @media #{$media-700} {
      padding-top: 15px !important;
      padding-bottom: 15px;
    }

    @media #{$media-min-1366} {
      min-height: 154px;
    }
  }
}

.scroll_block {
  max-width: 378px;
  margin-left: auto;
  margin-right: auto;
  position: relative;

  &::after {
    @media #{$media-1024} {
      display: block;
      content: "";
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 100;
      top: 0;
      left: 0;
    }
  }

  @media #{$media-500} {
    max-width: 200px;
  }
}

.custom_scrollbar {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  height: 14px;

  border-radius: 4px;

  &::after {
    content: "";
    display: block;
    height: 2px;
    width: 100%;
    background: rgba(0, 0, 0, 0.3);
    position: absolute;
  }

  @media #{$media-700} {
    height: 9px;
  }
}

.custom_scrollbar .swiper-scrollbar-drag {
  background: #f5a802;
  border-radius: 4px;
}

// -----------news and insights fixed height hover effect -------

.news_insight_fixed_height_block {
  .news_list_ul {
    margin-top: -20px;

    li {
      min-height: unset !important;
      height: 175px;
      min-height: 175px;
      padding-top: 20px;
      padding-bottom: 20px;
      align-items: flex-start;
      justify-content: flex-start;
      flex-direction: column;
      gap: 10px;

      .news_date {
        margin-bottom: 5px;
      }

      a {
        margin-top: 0;
      }

      p {
        min-height: 55px;
        max-height: 55px;

        @media #{$media-768} {
          min-height: 25px;
          max-height: 25px;
        }

        @media #{$media-700} {
          min-height: unset;
          max-height: unset;
        }
      }

      &:last-child {
        &::after {
          transform: translateY(0px) !important;
        }
      }

      &::after {
        height: calc(100% + 1px) !important;
        top: -1px !important;
      }

      &::before {
        bottom: 0px !important;
      }

      @media #{$media-1280} {
        height: 165px;
      }

      @media #{$media-1024} {
        height: 154px;
        gap: 3px;
      }

      @media #{$media-768} {
        height: 122px;
      }
    }
  }

  .insight_left_block {
    position: relative;

    .latest_insight_img_sec {
      margin: 0 !important;
      max-height: 350px;
      position: absolute !important;
      bottom: 0;

      @media #{$media-1280} {
        max-height: 330px;
      }

      @media #{$media-700} {
        position: relative !important;
        margin-top: 20px !important;
        margin-bottom: 20px !important;
        max-height: 250px;
      }
    }

    @media #{$media-768} {
      width: 34%;
    }

    @media #{$media-700} {
      width: 100%;
    }
  }
}