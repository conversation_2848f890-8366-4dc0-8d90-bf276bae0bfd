@import "variable", "base", "mixin";

.marqueeWrapper {
  overflow: hidden;
  white-space: nowrap;
  position: relative;
  width: 100%;
  background: #3b3064;
  padding-top: 13px;
  padding-bottom: 13px;
  direction: ltr;

  &.black_bg {
    background-color: black;
  }

  &.rtl {
    direction: rtl;
  }

  p {
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
    @include rem(18);
    // @include rem(20);
    // font-weight: 600;
    font-family: var(--allerRg);

    @media #{$media-700} {
      @include rem(15);
      padding-top: 0px;
      padding-bottom: 0px;
    }

    @media #{$media-500} {
      @include rem(16);

    }
  }

  @media #{$media-700} {
    padding-top: 5px;
    padding-bottom: 5px;
  }

  @media #{$media-500} {
    padding-top: 8px;
    padding-bottom: 8px;
  }
}

.marqueeContent {
  display: inline-flex;
  white-space: nowrap;
  align-items: center;

  &.font_weight {
    p {
      font-weight: 400
    }
  }

  b {
    font-size: 15px;

    @media #{$media-1280} {
      font-size: 13px;
    }

    @media #{$media-700} {
      font-size: 11px;
    }
  }

  &.font_20 {
    p {

      font-size: 20px;

      @media #{$media-1440} {
        font-size: 18px;
      }

      @media #{$media-1280} {
        font-size: 16px;
      }

      @media #{$media-500} {
        font-size: 14px;
      }
    }
  }

  p {
    @media #{$media-500} {
      font-size: 12px;
    }
  }
}

.head_marqueeWrapper {
  overflow: hidden;
  white-space: nowrap;
  position: relative;
  width: 100%;
  background: #6758b6;
  padding-top: 13px;
  padding-bottom: 13px;

  &.head_marqueeWrapper_overlay {
    z-index: 500;
  }

  p {
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
    @include rem(16);
    font-weight: 400;
    font-family: var(--aller_lt);

    @media #{$media-700} {
      @include rem(15);
    }

    @media #{$media-500} {
      font-size: 14px;
    }

    &.specialChar {
      position: relative;
      color: #03d784;
      margin-left: 15px;

      &::after {
        content: "";
        border-top: 5px solid #03d784;
        border-left: 5px solid #03d784;
        border-right: 5px solid rgba(255, 0, 0, 0);
        border-bottom: 5px solid rgba(255, 0, 0, 0);
        top: 55%;
        transform: translateY(-20%) rotate(45deg);
        left: 0;
        position: absolute;
      }
    }
  }
}

:global(body.rtl) .marqueeWrapper {
  // direction: rtl !important;
}