import React, {useState, useEffect} from "react";
import Link from "next/link";
import comon from "@/styles/comon.module.scss";
import banner from "@/styles/banner.module.scss";
import { useRouter } from "next/router";
import Image from "next/image";
const BannerHome = ({
  title,
  details,
  extraClass = "",
  showLink = true,
  linkHref = "#",
  linkText = "Start Your Campaign",
  isPaddingReduce = false,
  darkBackground = false,
  isButtonPopup = false,
  popupFunction
}) => {
  const route = useRouter();

  // console.log("router", route.asPath);

  const [isMobile, setIsMobile] = useState(false);
	 useEffect(() => {
      const checkScreenSize = () => {
        setIsMobile(window.innerWidth <= 767); 
      };  
      checkScreenSize();
      window.addEventListener("resize", checkScreenSize);  
      return () => {
        window.removeEventListener("resize", checkScreenSize);
      };
    }, []);

if(!details){
  return null;
}
  return (
    <section
      className={` ${banner.inner_banner_main} ${banner.mob_banner}  ${isPaddingReduce == true ? banner.padding_banner : ""
        } ${banner[extraClass] || ""}`}
    >
      <div
        className={`${banner.inner_banner_video} ${darkBackground == true ? banner.black_gradient : ""
          }  `}
      >
        {details.video && !details.banner_image_ ? (
        <video
          autoPlay
          muted
          loop
          playsInline={true}
          width="100%"
          height="auto"
        >
          {isMobile ? (
            <source src={details.video_mobile || details.video.url } type="video/mp4" />
          ): (
              <source src={details.video.url} type="video/mp4" />
          )}
          
        </video>
        ):(
          <Image
              src={details.banner_image}
              height={30}
              width={30}
              style={{width:'100%', height:'100%'}}
              alt=""
              quality={100}
          />
        )}
      </div>

      <div className={banner.wrap}>
        <div className={`${comon.d_flex_wrap} ${comon.justify_center}`}>
          <h1>{title}</h1>
          {showLink && isButtonPopup == false && (
            <Link
              className={`${comon.buttion}  ${isPaddingReduce == true ? banner.btn_padding_device : ""
                } ${banner.banner_link} ${comon.but_white} ${comon.mt_20}`}
              href={linkHref}
            >
              {linkText}
            </Link>
          )}
          {showLink && isButtonPopup == true && (
            <span
              className={`${comon.buttion}  ${isPaddingReduce == true ? banner.btn_padding_device : ""
                } ${banner.banner_link} ${comon.but_white} ${comon.mt_20}`}
              onClick={popupFunction}
            >
              {linkText}
            </span>
          )}
        </div>
      </div>
    </section>
  );
};

export default BannerHome;