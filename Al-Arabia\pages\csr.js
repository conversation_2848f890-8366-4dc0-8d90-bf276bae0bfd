import React, { useState, useEffect, useRef } from "react";
import Head from "next/head";
import Link from "next/link";
// ---buttion-import--start
import But<PERSON> from "@/component/button/Buttons";

import Marquee from "@/component/Marquee";
// ---buttion-import--end

import Image from "next/image";
import InnerBanner from "@/component/InnerBanner";
import Parallax from "@/component/Paralax";
import Map from "@/component/contactmap";

import comon from "@/styles/comon.module.scss";
import csr from "@/styles/csr.module.scss";
import buttion from "@/styles/buttion.module.scss";
import DynamicImage from "@/component/DynamicImage";

import HeadMarquee from "@/component/HeadMarquee";
import SliderSection from "@/component/SliderSection";
import ImageBlock from "@/component/ImageBlock";

import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, EffectFade, Scrollbar } from "swiper/modules";
import "swiper/css";
import "swiper/css/scrollbar";
import "swiper/css/effect-fade";
import ProjectPartners from "@/component/ProjectPartners";
import Box from "@mui/material/Box";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
// import Select from "@mui/material/Select";
import AwardStack from "@/component/AwardStack";
import AwardStackSlide from "@/component/AwardStackSlide";
import Modal from "@mui/material/Modal";
import rtl from "@/styles/rtl.module.scss";
import contactF from "@/styles/contactForm.module.scss";
import { Select } from "@mui/material";
import SelectDropDown from "@/component/SelectDropDown";
import { fetchPageById } from '@/lib/api/pageApi';
import parse from 'html-react-parser';
import { useRouter } from "next/router";
import OurInitiatives from "@/component/form/OurInitiatives";
export default function Csr({ pageData }) {
  const { locale } = useRouter();
  // ==========AOS SECTION==========
  // const [year, setYear] = useState("Select Year");
  // const [month, setMonth] = useState("Select Month");
  const [filteredCampaigns, setFilteredCampaigns] = useState([]);

  const campaignList = pageData.acf.campaign_list;

  // const [year, setYear] = useState(() => {
  //   return campaignList && campaignList.length > 0 ? campaignList[0].year : new Date().getFullYear();
  // });

  // const [month, setMonth] = useState(() => {
  //   return campaignList && campaignList.length > 0 ? campaignList[0].month.value.toLowerCase() : "january";
  // });



  const [year, setYear] = useState("2025");
  const [month, setMonth] = useState("Select Month");
  const [age, setAge] = useState("2025");
  const [dropdown, setDropdown] = useState("1 week");


  const uniqueYears = Array.from(new Set(campaignList.map(item => item.year)));
  const sortedYears = uniqueYears.sort((a, b) => b - a);
  const YearData = [
    // {
    //   value: 'Select Year',
    //   label: locale === 'ar' ? 'اختر السنة' : 'Select Year',
    //   disabled: true
    // },
    ...sortedYears.map(year => ({
      value: year,
      label: year
    }))
  ];

  // const MonthData = Array.from(
  //   new Set(campaignList.map(item => item.month.value))
  // ).map(month => ({
  //   value: month,
  //   label: month
  // }));

  // useEffect(() => {
  //   console.log("tttdfsgd", MonthData);
  //   console.log("tttdfsgd", YearData);
  // }, [YearData, MonthData]);


  const handleChange = (event) => {
    setAge(event.target.value);
  };
  const monthChnage = (event) => {
    setMonth(event.target.value);
  };
  const [open, setOpen] = useState(false);
  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  // filter
  useEffect(() => {
    handleFilter();
  }, [year, month]);
  // useEffect(() => {
  //   console.log("dshhhhhhhhh", month)
  // }, [year, month]);

  const [disableMonth, setDisableMonth] = useState(false);

  const handleFilter = () => {
    if (campaignList) {
      let filtered = campaignList;

      //console.log('hhhhhhhhhhhhhhhhhh', filtered)
      if (year !== "Select Year") {
        filtered = filtered.filter(
          (campaign) => Number(campaign.year) === Number(year)
        );
        const hasMonthData = filtered.some(campaign => campaign.month && campaign.month.value);
        setDisableMonth(!hasMonthData);

        if (month !== "Select Month" && month) {
          filtered = filtered.filter(
            (campaign) =>
              campaign.month.value && campaign.month.value.toLowerCase() == month.toLowerCase()
          );
        }
      } else {
        // year not selected, don't filter anything
        filtered = campaignList;
      }

      setFilteredCampaigns(filtered);
    }
  };





  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: currentYear - 2024 + 1 }, (_, i) => 2024 + i);

  const MonthData = [
    {
      label: locale === "ar" ? "اختر الشهر" : "Select Month",
      value: "Select Month",
      disabled: true,
    },
    ...(locale === "ar"
      ? [
        { label: "يناير", value: "january" },
        { label: "فبراير", value: "february" },
        { label: "مارس", value: "march" },
        { label: "أبريل", value: "april" },
        { label: "مايو", value: "may" },
        { label: "يونيو", value: "june" },
        { label: "يوليو", value: "july" },
        { label: "أغسطس", value: "august" },
        { label: "سبتمبر", value: "september" },
        { label: "أكتوبر", value: "october" },
        { label: "نوفمبر", value: "november" },
        { label: "ديسمبر", value: "december" }
      ]
      : [
        { label: "January", value: "january" },
        { label: "February", value: "february" },
        { label: "March", value: "march" },
        { label: "April", value: "april" },
        { label: "May", value: "may" },
        { label: "June", value: "june" },
        { label: "July", value: "july" },
        { label: "August", value: "august" },
        { label: "September", value: "september" },
        { label: "October", value: "october" },
        { label: "November", value: "november" },
        { label: "December", value: "december" }
      ])
  ];





  const IsPosiiton = [
    { value: "Lorem", label: "Lorem" },
    { value: "Lorem1", label: "Lorem1" },
  ];
  const Major = [
    { value: "Lorem", label: "Lorem" },
    { value: "Lorem1", label: "Lorem1" },
  ];
  const Nationality = [
    { value: "UAE", label: "UAE" },
    { value: "Dubai", label: "Dubai" },
  ];
  const Gender = [
    { value: "Male", label: "Male" },
    { value: "Female", label: "Female" },
  ];
  const isWorking = [
    { value: "Yes", label: "Yes" },
    { value: "No", label: "No" },
  ];

  return (
    <>

      <HeadMarquee />
      <InnerBanner title={pageData.acf.banner_details.title} details={pageData.acf.banner_details}
        linkHref={pageData.acf.banner_details.button.url}
        linkText={pageData.acf.banner_details.button.title}
        extraClass="padding_bottom_banner"
        isPaddingReduce={true}
        showLink={true}
        isButtonPopup={true}
        popupFunction={handleOpen}
      />

      <section className={`${comon.pt_60} ${comon.pb_30} ${comon.p_relative}  ${comon.z_10} `}>
        <div className={`${comon.wrap}`}>
          <div className={`${csr.csr_fltr_block}`}>
            <ul
              className={`${csr.csr_fltr_block_ul} ${csr.new_dropdown} custom_dropdown `}
            >
              <li className={`${csr.csr_select} base_font`}>
                <SelectDropDown
                  options={YearData}
                  value={year}
                  onChange={(val) => {
                    setYear(val);
                    if (val === "2023") {
                      setMonth("Select Month");
                    }
                  }}
                  classN={"base_font"}
                />
              </li>
              
              <li className={csr.csr_select}>

                <SelectDropDown
                  options={MonthData}
                  value={month}
                  onChange={(val) => setMonth(val)}
                  disabled={disableMonth}
                />
              </li>
              <li>
                <button
                  className={`${comon.buttion} ${comon.but_fill}`}
                  onClick={(e) => {
                    e.preventDefault();
                    handleFilter();
                  }}
                >
                  {pageData.acf.form_button_text}
                </button>
              </li>
            </ul>
          </div>
        </div>
      </section >

      <ImageBlock list={filteredCampaigns} />

      <AwardStackSlide data={pageData.acf.slider} />
      {/* <AwardStack /> */}

      <ProjectPartners images={pageData.acf.partners_list} slideView={6} />

      <div>
        <Modal
          open={open}
          onClose={handleClose}
          aria-labelledby="modal-modal-title"
          aria-describedby="modal-modal-description"
          data-lenis-prevent="true"
        >
          <Box
            // sx={style}
            className={`${contactF.popup_container} ${rtl.popup_container}`}
          >
            <button className={`${contactF.popup_close} ${rtl.popup_close}`} onClick={handleClose}>
              <Image
                src={"/images/close_btn.svg"}
                height={30}
                width={30}
                alt=" "
              />
            </button>
            <div className={`${contactF.popup_section} ${rtl.popup_section}`}>
              <div className={`${contactF.head_sec} ${rtl.head_sec}`}>
                <h3>{pageData.acf.popup.form_title && parse(pageData.acf.popup.form_title)}</h3>
                <p>
                  {pageData.acf.popup.description && parse(pageData.acf.popup.description)}
                </p>
              </div>

              <OurInitiatives />
            </div>
          </Box>
        </Modal>
      </div>
    </>
  );
}



export async function getStaticProps({ locale }) {
  const langCode = locale === "ar" ? "ar" : "en";
  //const langCode = "en"
  try {
    const pageData = await fetchPageById("csr", langCode);
    return {
      props: {
        pageData,
      },
      revalidate: 10,
    };
  } catch (error) {
    console.error('Error fetching data:', error);
    return {
      props: {
        pageData: null,
      },
    };
  }
}