@import "variable", "base";

.header {
  width: 100%;
  padding-top: 20px;
  padding-bottom: 20px;
  position: absolute;
  width: 100%;
  left: 0;
  top: 50px;
  z-index: 100;
}

.marqui {
  background: #5e45ff;
}

.marquee {
  padding-top: 15px;
  padding-bottom: 15px;
  background: #6758b6;
  color: #fff;
  font-size: 16px;
  white-space: nowrap;
  font-weight: 400;
  line-height: 20px;
}

.marquee_item {
  border-right: solid 1px #fff !important;
  padding-left: 15px;
  padding-right: 15px;

  p {
    color: $white;

    span {
      font-family: var(--segoeUiSemibold);
    }
  }
}

.about_hero__marquee_row {
  display: flex;
  position: relative;
  white-space: nowrap;

  .marq_block {}
}

.logo_block {
  width: 11.9%;
}

.menu_block {
  width: 80%;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-items: center;

  .menu_ul {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -20px;
    padding-left: 5%;
    padding-right: 5%;

    li {
      padding: 0% 20px;

      a {
        color: #fff;

        &:hover {
          color: #c1b0ff;
        }
      }
    }
  }

  .lang_switch {
    background: #6758b6;
    display: flex;
    padding: 5px 10px;
    color: #fff;
    align-items: center;

    &:hover {
      background: #5647a0;
    }
  }
}

.header_main {
  position: absolute;
  top: 35px;
  width: 100%;
  left: 0;
  padding-top: 65px;
  padding-bottom: 30px;
  z-index: 900;
  transition: all 0.3s ease;

  // &.sticky {
  //   position: fixed !important;
  //   top: 0 !important;
  //   left: 0;
  //   width: 100%;
  //   padding: 0;
  // }

  @media #{$media-700} {
    padding: 5px 0 0 0;
  }
}

.menu_block_02 {
  max-width: 733px;
  width: 90%;
  margin-left: auto;
  margin-right: auto;
  background: #514887e6;
  border-radius: 17px;
  border: 1px solid #6758b6;
  transition: all 0.3s ease;

  // &.active {
  //   border-radius: 0px;
  //   width: 100%;

  // }

  &.sticky {
    max-width: 100%;
    background: #ececec;
    width: 100%;
    border-radius: 0px;
    border: 1px solid #ececec;

    @media #{$media-700} {
      width: 100%;
    }
  }

  @media #{$media-700} {
    display: flex;
    padding: 10px 3%;
    justify-content: space-between;
  }
}

.menu_ul_block {
  display: flex;
  justify-content: center;
  gap:10%;

  >li {
    display: flex;
    align-items: center;
    position: relative;

    >a {
      display: block;
      font-family: var(--aller_lt);
      padding: 23px 0;
      color: white;

      font-size: 17px;
      font-weight: 400;

      // &.dropdown_link {
      //   padding: 24px 0;
      //   font-family: var(--aller_lt);
      // }

      @media #{$media-820} {
        font-size: 15px;
      }

      >img {
        @media #{$media-700} {
          display: none;
        }
      }
    }

    &:hover a {
      color: #b6a7ff;
    }

    .dropdown_arrow {
      height: 100%;
      width: 15px;
      display: flex;
      margin: 0 10px;
      align-items: center;

      @media #{$media-820} {
        width: 12px;
        position: relative;
        display: flex;
        margin: 0 10px;
      }

      img {
        transition: all 0.3s ease;
        display: block;
        object-fit: contain;
        height: auto;
        width: 100%;
      }

      &.active {
        img {
          @media #{$media-700} {

            transform: scaleY(-1) !important;

          }
        }
      }
    }

    .dropdown_menu {
      position: absolute;
      top: calc(100% - 5px);
      left: 50%;
      min-width: 250px;
      transform: translateX(-50%);
      background: #6758b6cc;
      backdrop-filter: blur(20px);
      padding: 20px;
      display: flex;
      flex-direction: column;
      gap: 10px;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease-in;

      &::after {
        position: absolute;
        content: "";
        left: 50%;
        top: -6px;
        transform: translateX(-50%) rotate(45deg);
        border-top: 6px solid #6758b6cc;
        border-left: 6px solid #6758b6cc;
        border-right: 6px solid transparent;
        border-bottom: 6px solid transparent;
      }

      li {
        a {
          font-size: 15px;
          font-weight: 400;
          color: white;
          padding: 10px 15px;
          display: block;
          width: 100%;
          border: 1px solid #9d8fda;
          transition: all 0.3s ease-in;

          &:hover {
            background-color: #7765d1cc;
          }

          @media #{$media-820} {
            font-size: 14px;
          }

          @media #{$media-700} {
            text-align: center;
            padding: 7px 15px;
          }
        }
      }

      @media #{$media-700} {
        opacity: 0;
        visibility: hidden;
        display: none;
        transform: translateX(0%);
        position: unset;

      }

      &.submenuActive {
        @media #{$media-700} {


          display: flex !important;

          position: unset;
          opacity: 1 !important;
          visibility: visible !important;
          min-width: 100%;
          transform: unset;
          padding: 20px;
        }
      }


      @keyframes dropdownAnimation {
        from {
          transform: translateX(-50%) translateY(20px);
        }

        to {
          transform: translateX(-50%) translateY(0px);
        }
      }



      @media #{$media-820} {
        padding: 15px;
      }


    }

    &:hover .dropdown_arrow img {
      transform: scaleY(-1);

      @media #{$media-700} {

        transform: unset;


      }
    }

    &:hover .dropdown_menu {
      opacity: 1;
      visibility: visible;
      animation: dropdownAnimation 0.3s linear;

      @media #{$media-700} {
        opacity: 0;
        visibility: hidden;
        animation: unset;
      }
    }

    @media #{$media-700} {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      flex-wrap: wrap;
      width: 100%;
    }
  }

  &.sticky {
    li {
      a {
        color: #3b3064;

        @media #{$media-700} {
          color: white;
        }
      }
    }
  }

  @media #{$media-700} {
    position: absolute;
    align-items: center;
    flex-direction: column;
    gap: 0px;
    top: 100%;
    right: 0;
    width: 100%;
    visibility: hidden;
    opacity: 0;
    background: #565391;
            // height: 100vh;
  }

  &.active {
    visibility: visible;
    opacity: 1;
  }


  &.sticky{
    gap: 4%;
    li{
      a{
        font-weight: 600;
      }
    }
  }
}

.language_switch {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  background: rgba(103, 88, 182, 1);
  padding: 5px 15px;
  opacity: 0;
  color: white;
  visibility: hidden;
  transition: all 0.3s ease;

  &.sticky {
    opacity: 1;
    visibility: visible;

    @media #{$media-700} {
      color: rgba(103, 88, 182, 1);
    }
  }

  @media #{$media-700} {
    position: unset;
    right: 100px;
    background: rgba(104, 88, 182, 0);
    font-size: 18px;
    opacity: 1;
    padding: 5px 25px;

    visibility: visible;
    transform: unset;
    margin-left: auto;
  }
}

.sticky .dropdown_arrow>img {
  filter: invert(1);

  @media #{$media-700} {
    filter: invert(0);
  }
}

.mob_logo {
  display: none;

  @media #{$media-700} {
    display: block;
  }
}

.hamburger {
  display: none;
  align-items: center;
  justify-content: center;
  height: auto;
  width: 30px;

  span {
    height: 2px;
    width: 30px;
    background-color: rgb(255, 255, 255);
    position: relative;
    transition: all 0.3s ease;

    &::before {
      position: absolute;
      content: "";
      height: 2px;
      width: 100%;
      bottom: -10px;
      background-color: rgb(255, 255, 255);
      transition: all 0.3s ease;
    }

    &::after {
      position: absolute;
      content: "";
      height: 2px;
      width: 100%;
      top: -10px;
      background-color: rgb(255, 255, 255);
      transition: all 0.3s ease;
    }
  }

  &.active {
    span {
      background-color: transparent;

      &::before {
        bottom: 0%;
        transform: rotate(-45deg);
        background-color: #ffffff;
      }

      &::after {
        background-color: #ffffff;
        top: 0px;
        transform: rotate(45deg);
      }
    }
  }

  &.sticky {
    span {
      background: rgba(103, 88, 182, 1);

      &::before {
        background: rgba(103, 88, 182, 1);
      }

      &::after {
        background: rgba(103, 88, 182, 1);
      }
    }

    &.active {
      span {
        background-color: transparent;
      }
    }
  }

  @media #{$media-700} {
    display: flex;
  }
}