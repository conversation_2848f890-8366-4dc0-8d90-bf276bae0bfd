import React, { useState, useEffect } from "react";
import comon from "@/styles/comon.module.scss";
import home from "@/styles/home.module.scss";
import buttion from "@/styles/buttion.module.scss";
import DynamicImage from "@/component/DynamicImage";
import parse from 'html-react-parser';
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/scrollbar";
import {
	Scrollbar as SwiperScrollbar,
	Autoplay,
	Navigation,
} from "swiper/modules";
import Image from "next/image";
import { useRouter } from "next/router";
import Link from "next/link";

const SliderSection = ({
	images,
	title,
	paragraph,
	description,
	padding_top0 = false,
	PageLink = false,
	bgColor, textColor
}) => {
	const [activeIndex, setActiveIndex] = useState(0);
	// Array of images corresponding to each `li`
	console.log(images);

	const router = useRouter();

	return (
		<div style={{backgroundColor:bgColor}}>
			<section
				className={`${padding_top0 == true ? comon.pt_0 : comon.pt_65} ${comon.pb_65
					}  ${comon.overflow_hide} swipper_home`}
			>
				<div
					className={`${comon.title_30}  ${comon.mb_30}  ${comon.text_center}   `}
				>
					{title && (
						<h3
						// data-aos="fade-in"
						// data-aos-delay="600"
						// data-aos-duration="1000"
						style={{color:textColor}}
						>
							{title}
						</h3>
					)}

					{paragraph && (
						<p
							// data-aos="fade-in"
							// data-aos-delay="600"
							// data-aos-duration="1000"
							className={comon.mt_15}
						>
							{paragraph}
						</p>
					)}
				</div>
				<Swiper
					// data-aos="fade-in"
					// data-aos-delay="600"
					// data-aos-duration="1000"
					initialSlide={1}
					scrollbar={{
						el: ".custom-scrollbar", // Reference to your custom scrollbar div
						draggable: true, // Makes the scrollbar draggable
					}}
					speed={1300}
					autoplay={{
						delay: 3000, // Delay between slides in milliseconds
						disableOnInteraction: false, // Keep autoplay running after user interaction
					}}
					// loop={true}
					slidesPerView="auto"
					navigation={{
						prevEl: '.prev_btn',
						nextEl: '.next_btn'
					}}
					spaceBetween={0}
					centeredSlides={true}
					modules={[SwiperScrollbar, Autoplay, Navigation]}
					className="mySwiper swiper_slides_sec "
					dir={router.locale == "ar" || router.locale == "rtl" ? "rtl" : "ltr"}
				>
					{/* {images.map((data, index) => ( */}
					{images.concat(images, images, images).map((data, index) => (
						<SwiperSlide className={`${home.slider_block}`}>

							{PageLink == false ? <>
								<div className={`${home.slider_block_txt}`}>
									{data.description && <h4>{data.description}</h4>}
								</div>
								<Image
									src={data.img}
									fill
									style={{ objectFit: "cover" }}
									alt=""
									quality={100}
								/>
								<Image
									className={`${comon.w_100} ${comon.holder}  ${comon.slider_img}`}
									src={data.img}
									width={672}
									height={447}
									alt="button image"
									style={{
										height: "auto",
										width: "100%",
										display: "block",
										aspectRatio: "1/1",
									}}
									quality={100}
								/>
							</>
								:
								<>

									<Link href={data.link}>
										<div className={`${home.slider_block_txt}`}>
											{data.description && <h4>{data.description}</h4>}
										</div>
										<Image
											src={data.img}
											fill
											style={{ objectFit: "cover" }}
											alt=""
											quality={100}
										/>
										<Image
											className={`${comon.w_100} ${comon.holder}  ${comon.slider_img}`}
											src={data.img}
											width={672}
											height={447}
											alt="button image"
											style={{
												height: "auto",
												width: "100%",
												display: "block",
												aspectRatio: "1/1",
											}}
											quality={100}
										/>
									</Link>
								</>
							}
						</SwiperSlide>
					))}


					<div className="thumb_swiper_slide_btn top_adjusted">
						<div className="prev_btn swiper_btn ">
							<Image
								// src="/images/next_icon1.svg"
								src="/images/next_icon2.svg"

								height={30}
								width={30}
								alt="next"
								quality={100}
							/>
						</div>
						<div className="next_btn swiper_btn">
							<Image
								// src="/images/next_icon1.svg"
								src="/images/next_icon2.svg"

								height={30}
								width={30}
								alt="prev"
								quality={100}
							/>
						</div>
					</div>

				</Swiper>

				<div className={`${home.scroll_block} ${home.mt_40}`}>
					<div className={`${home.custom_scrollbar} custom-scrollbar`}></div>
				</div>
			</section>
		</div>
	);
};

export default SliderSection;
