import * as https from 'https';
import * as XLSX from 'xlsx';

export default function handler(req, res) {
  const fileUrl = 'https://acscoutdoor-my.sharepoint.com/personal/h_alowaidh_al-arabia_com/_layouts/15/download.aspx?share=ERbGc3cqlHFKm4LzM6qxGGkB7ZRyiraqhsV9NMbP6_ehhw';

  https.get(fileUrl, (fileRes) => {
    const chunks = [];

    fileRes.on('data', (chunk) => {
      chunks.push(chunk);
    });

    fileRes.on('end', () => {
      try {
        const buffer = Buffer.concat(chunks);
        const workbook = XLSX.read(buffer, { type: 'buffer' });

        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        res.status(200).json({ data: jsonData });
      } catch (error) {
        console.error('Error parsing Excel file:', error.message);
        res.status(500).json({ error: 'Failed to parse Excel file', detail: error.message });
      }
    });

  }).on('error', (err) => {
    console.error('HTTPS error:', err.message);
    res.status(500).json({ error: 'Failed to fetch file', detail: err.message });
  });
}
