import React, { useState, useEffect } from "react";
import campaign from "@/styles/campaign.module.scss";
import comon from "@/styles/comon.module.scss";
import Link from "next/link";
import MultiDatePicker from "@/component/MultiDatePicker";
import contactF from "@/styles/contactForm.module.scss";
import Image from "next/image";
import RangeCalendar from "./Calendar";
import Modal from "@mui/material/Modal";
import Box from '@mui/material/Box';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import SelectDropDown from "./SelectDropDown";
import { useRouter } from "next/router";
const CampaignDates = ({ onNext, onDataChange, darkMode = false, pageData }) => {
  const [dropdown, setDropdown] = useState("");
  const [selectedDateRange, setSelectedDateRange] = useState({
    startDate: new Date(),
    endDate: new Date(),
  });
  const [activeSelector, setActiveSelector] = useState(null);
const {locale} = useRouter();
  // drop down change
  const handleDropdownChange = (value) => {
    // setSelectedDateRange(null);    
    setActiveSelector("calendar");
    setDropdown(value);
    onDataChange({ dropdown: value, selectedDateRange1: "" });
  };
  

  // select change
  const handleDateChange = (newRange) => {
    setDropdown('')
    setActiveSelector("dropdown");
    setSelectedDateRange(newRange);
    onDataChange({ dropdown:"", selectedDateRange1: newRange });
  };
  const OptionData = pageData.acf.durations?.map(duration => ({
    value: duration.item,
    label: duration.item
  }));

  const changeFn = () => {
    //setDropdown('')
    setActiveSelector("calender");
    onDataChange({ dropdown: dropdown, selectedDateRange1: "" });
    //alert("test")
  };
  const DateClick=() => {
    setDropdown('')
    setActiveSelector("dropdown");
    onDataChange({ dropdown:"", selectedDateRange1: selectedDateRange });
  }
  // useEffect(() => {
  //   console.log("yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy", selectedDateRange)
  // }, [selectedDateRange])

  return (
    <>
      <div
        className={`${comon.title_30}`}
        data-aos="fade-in"
        data-aos-duration="1000"
      >
        <h3>{pageData.acf.title2}</h3>
      </div>

      <div
        className={`${comon.w_100} ${campaign.select_network_container} ${comon.justify_space_bet} ${comon.d_flex_wrap} ${comon.pt_20} ${comon.mb_40}`}
      >
        <div
          className={`${campaign.select_network_block} ${campaign.width_620} ${
            darkMode ? contactF.dark_mode : ""
          }`}
        >
          <ul className={`${contactF.contact_form_main_ul} ${comon.max_0} ${activeSelector === "calendar" ? "calendar-disabled" : ""}`}
            onClick={() => {
              DateClick()
            }}
          >
             <li
              data-aos="fade-in"
              data-aos-duration="1000"
              className={`${contactF.form_li}`}
              
            >
              <input
                className={`${contactF.input_fld}`}
                type="text"
                placeholder={pageData.acf.start_date}
                value={selectedDateRange?.startDate ? new Date(selectedDateRange.startDate).toDateString() : ''}

                readOnly
              />
            </li>
             <li
              data-aos="fade-in"
              data-aos-duration="1000"
              className={`${contactF.form_li}`}
            >
              <input
                className={`${contactF.input_fld}`}
                type="text"
                placeholder={pageData.acf.end_date}
                value={selectedDateRange?.endDate ? new Date(selectedDateRange.endDate).toDateString() : ''}
                readOnly
              />
            </li>
          </ul>

          <div
            data-aos="fade-in"
            data-aos-duration="1000"
            className={`${comon.w_100} ${comon.mb_30} ${comon.mt_10}`}
          >
         <p>{locale === 'ar' ? 'أو' : 'Or'}</p>

          </div>

          <div
            // data-aos="fade-in"
            // data-aos-duration="1000"
            className={`${comon.w_100} ${activeSelector === "dropdown" ? "calendar-disabled" : ""}`}
          >
            <p>{pageData.acf.duration_title}</p>
          </div>

          <div className={`${comon.w_100} ${activeSelector === "dropdown" ? "calendar-disabled" : ""}`}
           
          >
            <ul className={`${contactF.contact_form_main_ul} ${comon.max_0}`}>
              <li
                data-aos="fade-in"
                data-aos-duration="1000"
                className={`${contactF.form_li} ${comon.w_100} ${
                  darkMode ? "black_color" : ""
                } custom_dropdown career_dropdown `}
                onMouseDown={() => {
                  changeFn()
                }}
              >
                <Box style={{ width: "100%" }}>
                  <FormControl fullWidth>
                    {/* <Select
                      id="duration-select"
                      value={dropdown}
                      onChange={handleDropdownChange}
                    >
                      <MenuItem value="" disabled>
                        Select a duration
                      </MenuItem>
                      {pageData.acf.durations.map((duration, index) => (
                        <MenuItem value={duration.item} key={index}>
                          {duration.item}
                        </MenuItem>
                      ))}
                    </Select> */}
                    <SelectDropDown options={OptionData} DarkTheme={darkMode == true ? true : ''} 
                    value={dropdown}
                    onChange={handleDropdownChange}
                    classN="base_font"
                    />
                  </FormControl>
                </Box>
              </li>
            </ul>
          </div>

          <div className={`${comon.w_100} ${comon.mt_10} `}>
            <p>{pageData.acf.selected_days_title}</p>
          </div>

          <div  className={`${comon.title_30} ${comon.mt_20}`}>
            {dropdown ? (
              <h3>              
              {dropdown}
            </h3>
            ):(
              <h3>              
                <span className={locale === 'ar' ? 'base_font' : ''}>{`${Math.round(
                  (new Date(selectedDateRange.endDate) -
                    new Date(selectedDateRange.startDate)) /
                    (1000 * 60 * 60 * 24)
                ) + 1}`}</span> {locale=='ar' ? 'أيام' :'Days'}
              </h3>
            )}
            
          </div>
        </div>

        <div className={`${campaign.select_network_block} ${activeSelector === "calendar" ? "calendar-disabled" : ""}`}>
          <div
            data-aos="fade-in"
            data-aos-duration="1000"
            className={`${campaign.date_picker_block} cl`}
          >
            <RangeCalendar darkMode={darkMode} onDateChange={handleDateChange} 
             />
          </div>
        </div>
      </div>

      <style jsx>{`
      .calendar-disabled {
        // pointer-events: none;
        opacity: 0.5;
        transition: opacity 0.3s ease;
      }
      .cl .rdrDayNumber span {
          font-family: Aller, sans-serif !important;
      }
    `}</style>
    </>
  );
};

export default CampaignDates;


