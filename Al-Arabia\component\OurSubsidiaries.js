import Image from "next/image";
import Link from "next/link";
import comon from "@/styles/comon.module.scss";
import about from "@/styles/about.module.scss";

const OurSubsidiaries = () => {
  return (
    <section className={`${comon.w_100} ${about.px_about}    ${comon.pt_0} ${comon.pb_20}`}>
      <div
        className={`${comon.title_30} ${comon.justify_space_bet} ${comon.d_flex_wrap} ${comon.flex_center}`}
      >
        <h3>Our Subsidiaries</h3>
        <Link className={`${comon.link}`} href="#">
          Learn More
        </Link>
      </div>

      <ul className={`${about.subsidiaries_block}`}>
        <li>
          <div className={`${comon.w_100}`}>
            <h3>Expertise Advertising</h3>
            <p>
              All entities focus on delivering comprehensive advertising and media solutions, experience in the outdoor advertising sector.
            </p>
          </div>
        </li>

        {["subsidiaries_ic_01.svg", "subsidiaries_ic_02.svg", "subsidiaries_ic_01.svg", "subsidiaries_ic_03.svg", "subsidiaries_ic_04.svg", "subsidiaries_ic_05.svg"].map((src, index) => (
          <li
            key={index}
            className={`${comon.logo_block} ${comon.flex_center} ${comon.justify_center} ${comon.d_flex_wrap}`}
            style={{ backgroundColor: index % 2 === 0 ? "#6758b6" : "transparent", aspectRatio: "326/260" }}
          >
            <div
              className={`${comon.p_relative} ${comon.w_100}`}
              style={{ aspectRatio: "135/60", maxWidth: "150px" }}
            >
              <Image
                src={`/images/${src}`}
                fill
                style={{ objectFit: "contain" }}
                alt="Subsidiary Logo"
                quality={100}
              />
            </div>
          </li>
        ))}

        <li>
          <div className={`${comon.w_100}`}>
            <h3>Diverse Services</h3>
            <p>
              Offering a wide range of services including consulting, digital advertising, to cater to various client needs.
            </p>
          </div>
        </li>
      </ul>

      <div
        className={`${comon.pt_100} ${comon.pb_100} custom_next_4`}
        style={{
          backgroundImage: 'url("/images/c-social-bg.jpg")',
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
        <div className={`${comon.wrap}`}>
          <div className={`${comon.w_100} ${comon.d_flex_wrap}`}>
            <div className={`${about.sustainability_block} ${comon.ml_auto}`}>
              <h3>Sustainability</h3>
              <p>Enhancing the environment and building smart cities are our main pillars</p>
              <p>
                AlArabia has never stopped innovating since it has been founded, and has started with street furniture, live-touch screens, bus shelters, self-cleaning toilets, etc. The story is not only about developing something new and original, but it’s also about taking what’s already there and making it much better. That’s what we do best.
              </p>
              <Link className={`${comon.buttion} ${comon.but_white} ${comon.mt_20}`} href="#">
                Learn More
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default OurSubsidiaries;
