@import "variable", "base", "mixin";

.airplane_wrap {
    top: -500px;
    // top: 0px;
    position: absolute;
    width: 80%;
    height: auto;
    z-index: 1;
    // right: 0;
    right: -25px;
    pointer-events: none;

    >svg {
        width: 100%;

        @media #{$media-600} {
            // height: 100%;
        }
    }

    @media #{$media-1599} {
        top: -50%;
    }

    @media #{$media-1440} {
        top: -60%;
    }

    @media #{$media-1366} {
        top: -68%;
    }

    @media #{$media-1024} {
        top: -80%;
    }

    @media #{$media-768} {
        top: -90%;
    }

    @media #{$media-600} {
        top: -75%;
        width: 100%;
    }

    @media #{$media-500} {
        // top:-92%;
        top: -86%;
    }

    @media #{$media-430} {
        top: -90%;
    }

    @media #{$media-400} {
        top: -95%;
    }
}


.airplane_wrap_new {
    top: 10px;
    position: absolute;
    width: 80%;
    height: auto;
    z-index: 1;
    // right: 0;
    right: -20px;

    pointer-events: none;



    .airplane_svg {
        width: 100%;
        height: 100%;


    }

    .plane_img {
        width: 120px;
        height: auto;

        @media #{$media-1024} {
            width: 180px;

        }

        @media #{$media-500} {
            width: 220px;

        }

    }

    @media #{$media-1599} {
        top: 10px;


    }



    @media #{$media-1024} {
        top: 80px;

    }

    @media #{$media-500} {
        // top: 40px;
        top: 0px;

    }

    
}