@import "variable", "base", "mixin";

@font-face {
    font-family: "aller_lt";
    src: url("../public/fonts/aller_lt-webfont.woff");
}

@font-face {
    font-family: "aller_rg";
    src: url("../public/fonts/aller_rg-webfont.woff");
}

@font-face {
    font-family: "aller_bd";
    src: url("../public/fonts/aller_bd-webfont.woff");
}

@font-face {
    font-family: "arbfontsBase";
    src: url("../public/fonts/Cairo-Regular.woff");
}

@font-face {
    font-family: "arbfonts";
    src: url("../public/fonts/arbfonts-ge_thameen_book.woff");
}

.season_image_item_mob {
    aspect-ratio: 285 / 400;
    width: 75%;
}


.arabic_format {
    gap: 5px;
    display: flex;
    align-items: center;
    flex-wrap: wrap-reverse;

}

.season_image_card {
    &.cursor_pointer {
        cursor: pointer;
    }

    position: relative;
    max-height: 400px;
    transition: all 0.8s ease !important;
    // background-color: aliceblue;
    overflow: hidden !important;

    .season_image {
        height: 100% !important;
        pointer-events: none;

        >img {
            height: 100% !important;
            width: auto !important;
            aspect-ratio: 619/400 !important;
            object-position: left;
            object-fit: contain !important;
            display: block;

            @media #{$media-1024} {
                aspect-ratio: unset !important;
                object-position: center !important;
                object-fit: cover !important;
                width: 100% !important;
            }
        }
    }

    .text {
        position: absolute;
        width: 100%;

        left: 50%;
        transform: translateX(-50%);
        bottom: 0px;
        color: #fff;
        padding: 30px 10%;

        h3 {
            font-family: var(--aller_lt);
            @include rem(20);
            font-weight: 300;
        }

        &.text_overlay {
            background-image: linear-gradient(to top,
                    rgb(0, 0, 0) 20%,
                    rgba(0, 128, 0, 0));
        }

        @media #{$media-768} {
            padding: 15px 10%;
        }
    }

    &.extra_expand {
        @media screen and (min-width: 1025px) {
            &:hover {
                // width: 33% !important;
                width: 50% !important;
            }
        }

        @media screen and (max-width: 768px) {
            max-height: 250px !important;
        }

        @media screen and (max-width: 500px) {
            max-height: 150px !important;
        }
    }

    @media screen and (min-width: 1025px) {
        &:hover {
            width: 33% !important;
        }
    }

    @media screen and (min-width: 1440px) {
        &:hover {
            width: 40% !important;
        }
    }

    @media screen and (min-width: 1600px) {
        &:hover {
            width: 30% !important;
        }
    }
}

.map_section {
    height: 79dvh;
    width: 100%;
    position: relative;

    &::after {
        position: absolute;
        pointer-events: none;
        content: "";
        background-image: linear-gradient(to top, #282828c0, rgba(0, 128, 0, 0));
        height: 100%;
        width: 100%;
        left: 0;
        top: 0;
    }

    .map_content {
        position: absolute;
        top: 10%;
        right: 5%;
        height: 80%;
        width: 100%;
        max-width: 446px;
        z-index: 2;
        // pointer-events: none;
        overflow: auto;

        .content_img {
            height: auto;
            width: 100%;
            visibility: hidden;

            >img {
                height: auto;
                width: 100%;
                object-fit: contain;
                display: block;

                @media #{$media-600} {
                    height: 100%;
                    object-fit: cover;

                    width: 100%;
                }
            }

            @media #{$media-1024} {
                width: 30%;
                display: none;
            }

            @media #{$media-600} {
                height: 100px;

                width: 100%;
            }
        }

        .content_text {
            margin-top: 20px;

            span {
                color: #ffffff;
                font-size: 18px;
                margin-bottom: 5px;
                display: block;
                font-family: "allerlt";

                @media #{$media-1024} {
                    font-size: 15px;
                }
            }

            h4 {
                margin-bottom: 10px;

                font-family: "allerlt";

                color: #ffffff;
                // font-size: 30px;
                @include rem(30);
                font-weight: 700;
                display: block;

                @media #{$media-1024} {
                    @include rem(27);

                    margin-bottom: 5px;
                }
            }

            p {
                color: #ffffff;
                font-family: "allerlt";

                font-size: 15px;
                display: block;

                font-weight: 400;

                @media #{$media-1024} {
                    font-size: 14px;
                    line-height: 140%;
                }
            }

            @media #{$media-1024} {
                // width: 65%;
                width: 100%;
            }

            @media #{$media-600} {
                width: 100%;
            }
        }

        // @media #{$media-1024} {

        //     padding: 25px;
        //     height: auto;
        //     top: unset;
        //     bottom: 0;
        //     right: 0;
        //     width: 60%;
        //     max-width: unset;
        // }

        @media #{$media-1024} {
            padding: 25px;
            height: auto;
            top: unset;
            bottom: 0;
            right: 0;
            width: 100%;
            max-width: unset;

            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 20px;
        }

        @media #{$media-600} {
            display: unset;
        }
    }
}

:global(body.rtl) {
    .content_text {
        span {
            font-family: "arbfonts" !important;
        }

        h4 {
            font-family: "arbfonts" !important;
        }

        p {
            font-family: "arbfonts" !important;
        }
    }
}