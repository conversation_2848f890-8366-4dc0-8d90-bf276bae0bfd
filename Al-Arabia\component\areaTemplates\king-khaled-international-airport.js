import HeadMarquee from "@/component/HeadMarquee";
import React, { useState, useEffect } from "react";
import InnerBanner from "@/component/InnerBanner";
import comon from "@/styles/comon.module.scss";
import about from "@/styles/about.module.scss";
import CountUp from "@/component/CountUp";
import boulevard from "@/styles/boulevard.module.scss";
import rtl from "@/styles/rtl.module.scss";
import Link from "next/link";
import Image from "next/image";
import SliderBannerText from "@/component/SliderBannerText";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/scrollbar";
import { Autoplay, Navigation } from "swiper/modules";
import Head from "next/head";
import { useRouter } from 'next/router';
import parse from 'html-react-parser';

const KingKhaledInternationalAirport = ({ pageData }) => {
	const [tabSelect, setTabSelect] = useState();

	const scrollToSection = (sectionId) => {
		setTabSelect(sectionId);
		const section = document.getElementById(sectionId);
		if (section) {
			section.scrollIntoView({ behavior: "smooth" });
		}
	};

	const [isMobile, setIsMobile] = useState(false);

	useEffect(() => {
		const handleResize = () => {
			setIsMobile(window.innerWidth < 768);
		};

		handleResize(); // Check on mount
		window.addEventListener("resize", handleResize);

		return () => window.removeEventListener("resize", handleResize);
	}, []);

	const [isClient, setIsClient] = useState(false);
	const { locale } = useRouter();
	const [airportList, setAirportList] = useState(null);
	useEffect(() => {
		const parentId = pageData.id;
		const fetchOptions = async () => {
			try {
				const res = await fetch(
					`${process.env.NEXT_PUBLIC_API_BASE_URL}/areas?acf_format=standard&per_page=100&lang=${locale}&_embed&parent=${parentId}&orderby=date&order=asc`
				);
				const data = await res.json();
				setAirportList(data);
			} catch (error) {
				console.error("Error fetching options:", error);
			}
		};

		fetchOptions();
	}, [locale]);

	return (
		<div>
			{/* <Head>
				<title>King Khaled International Airport</title>
				<meta name="description" content="Generated by create next app" />
			</Head> */}
			<HeadMarquee />
			{pageData.acf.banner_details.description_field ? (

				<SliderBannerText
					title={pageData.acf.banner_details.title}
					paragraph={pageData.acf.banner_details.description_}
					details={pageData.acf.banner_details}
					className={'width_870'}
					bgColor={true}
					extra_height_class={true}
					full_height_banner={true}
				/>
			) : (
				<InnerBanner showLink={false} title={pageData.acf.banner_details.title} details={pageData.acf.banner_details} />
			)}
			<div className={`${comon.wrap} ${comon.mb_30} ${comon.mt_35}  `}>
				<ul className={`${comon.mb_30} ${comon.detail_breadcrumb} ${comon.detail_breadcrumb_padding}  ${rtl.detail_breadcrumb} `}>
					<li>
						<Link href={"/"}>
							<div className={` ${comon.bread_icon}  `}>
								<Image
									src={"/images/breadcumb_home.svg"}
									height={30}
									width={30}
									alt=""
								/>
							</div>
						</Link>
					</li>
					{pageData.parent_title && (
						<li>
							<Link href={`${pageData.parent_slug}`}>{pageData.parent_title}</Link>
						</li>
					)}
					<li>
						<Link
							href={"/king-khaled-international-airport"}
							className={` ${comon.active}`}
						>
							{pageData.title.rendered}
						</Link>
					</li>
				</ul>
			</div>

			<section className={`${comon.mt_35} ${comon.mb_60}`}>
				<div className={`${comon.wrap}`}>
					<div className={`${comon.w_100} ${comon.d_flex_wrap}`}>
						<ul className={`${about.two_cl_ul} ${about.kkia_ul_sec}`}>
							<li
								data-aos="fade-up"
								data-aos-duration="1000"
								className={`${comon.w_50} ${about.mission_vission_block}`}
							>
								<>
									{parse(pageData.acf.description.description_left)}
								</>
							</li>
							<li
								data-aos="fade-up"
								data-aos-duration="1000"
								className={`${comon.w_50} ${about.mission_vission_block}`}
							>
								<>
									{parse(pageData.acf.description.description_right)}
								</>
							</li>
						</ul>
					</div>
				</div>
			</section>

			<CountUp counterData={pageData.acf.counter} />

			<section className={`${comon.pt_60} ${comon.pb_60}`}>
				<div
					className={`${comon.wrap}`}
					data-aos="fade-up"
					data-aos-duration="1000"
				>
					{!isMobile && (
						<div className={`${boulevard.season_image_section}`}>
							<ul
								className={`${boulevard.season_image_ul_new} ${boulevard.second}`}
							>
								{airportList?.map((season, index) => {
									console.log(season);
									return (
										<li key={index} className={`${boulevard.season_image_item}`}>
											{season.slug && (
												<Link href={`${season.slug}`}>
													<div className={`${boulevard.season_image}`}>
														<Image
															src={season._embedded["wp:featuredmedia"][0].source_url}
															fill
															style={{ objectFit: "cover" }}
															quality={100}
														/>
													</div>

													<div className={`${boulevard.text}`}>
														<h3>{season.title.rendered}</h3>
													</div>
												</Link>
											)}
										</li>
									)
								})}
							</ul>
						</div>
					)}

					{isMobile && (
						<Swiper
							spaceBetween={15}
							slidesPerView={"auto"}
							pagination={{
								clickable: true,
							}}
							autoplay={{
								delay: 5000,
								disableOnInteraction: false,
							}}
							loop={true}
							speed={2000}
							className="mySwiper"
							modules={[Autoplay]}
						>
							{airportList?.map((season, index) => {
								console.log(season);
								return (
									<SwiperSlide
										modules={[Autoplay, Navigation]}
										key={index}
										className={`${boulevard.season_image_item}  ${boulevard.mobile_version_swiper} ${boulevard.season_image_item_mob}`}

									>
										{/* <div className={`${boulevard.season_image}`}>
										<Image
											src={season.image}
											fill
											style={{ objectFit: "cover" }}
										/>
									</div> */}
										{/* <div className={`${boulevard.text}`}>
										<h3>{season.title}</h3>
									</div> */}


										<Link href={`${season.slug}`} >
											<div className={`  ${boulevard.mobile_version} ${boulevard.mobile_version_new}`}>
												<div className={`${boulevard.season_image}  `}>
													<Image
														src={season._embedded["wp:featuredmedia"][0].source_url}
														height={500}
														width={600}
														alt="'"
														quality={100}
													/>
												</div>

												<div className={`${boulevard.text}`}>
													<h3>{season.title.rendered}</h3>
												</div>
											</div>
										</Link>

									</SwiperSlide>
								)
							})}
						</Swiper>
					)}
				</div>
			</section>
		</div>
	);
};

export default KingKhaledInternationalAirport;
