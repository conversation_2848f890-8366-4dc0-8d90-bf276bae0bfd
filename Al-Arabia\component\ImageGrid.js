"use client";
import React, { useState } from "react";
import style from "@/styles/ImageGrid.module.scss";
import comon from "@/styles/comon.module.scss";
import Link from "next/link";
import Image from "next/image";
import { Navigation, FreeMode, Thumbs } from "swiper/modules";
import { Box, Modal } from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/pagination";

const ImageGrid = ({ images }) => {
  const chunkArray = (arr, size) =>
    Array.from({ length: Math.ceil(arr.length / size) }, (_, i) =>
      arr.slice(i * size, i * size + size)
    );

  const layoutClasses = [
    "grid_sec1",
    "grid_sec2",
    "grid_sec3",
    "grid_sec4",
    "grid_sec5",
    "grid_sec6",
    "grid_sec7",
    "grid_sec8",
    "grid_sec9",
    "grid_sec10",
  ];
  const [activeIndex, setActiveIndex] = useState(0);

  const imageChunks = chunkArray(images, 10);

  const [open, setOpen] = useState(false);
  const [thumbsSwiper, setThumbsSwiper] = useState(null);
  const [imageSlides, setImageSlides] = useState([]);

  // Handle modal open and close
  const handleOpen = (index) => {
    setActiveIndex(index);
    setOpen(true);
  };


  const handleClose = () => {
    setThumbsSwiper(null);
    setOpen(false);
  };
  return (
    <section>
      {imageChunks.map((chunk, groupIndex) => {
        let x = 1;
        return (
          <div className={`${style.image_grid_layout}`} key={groupIndex}>
            {chunk.map((src, index) => {
              const content = (
                <div
                  className={`grid_sec${x} ${style.image_sec}`}
                  key={index}
                  onClick={() => handleOpen(groupIndex * 10 + index)}
                >
                  <Image
                    src={src}
                    height={2400}
                    width={2500}
                    alt={`Image ${index + 1}`}
                    quality={100}
                  />
                </div>
              );
              x++;
              if (x > 9) x = 1;
              return content;
            })}
          </div>
        );
      })}


      <div>
        <Modal
          className="ModalBlockSlideSection"
          open={open}
          data-lenis-prevent="true"
          onClose={() => {
            setThumbsSwiper(false);
            setOpen(false);
          }}
        >
          <Box
            className={`${comon.popup_container}  ${comon.swiper_popup_container} ${comon.min_height}`}
          >  <div
            onClick={() => {
              setThumbsSwiper(false);
              setOpen(false);
            }}
            className={`${comon.swiper_image} ${comon.swiper_popup_image} ${comon.swiper_image_contain} ${comon.swiper_image_cover}`}>
              <Image src={images[activeIndex]} alt={`Slide  `}
                quality={100}
                height={2400}
                width={2500}

              />
              {/* <button
                className={comon.popup_close}
                onClick={() => {
                  setThumbsSwiper(false);
                  setOpen(false);
                }}
              >
                <Image
                  src="/images/close_btn.svg"
                  height={30}
                  width={30}
                  alt="Close"
                />
              </button> */}
            </div>

            {/* <Swiper
              initialSlide={activeIndex}
              spaceBetween={10}
              onSwiper={setThumbsSwiper}
              // navigation
              navigation={{
                prevEl: '.prev_btn',
                nextEl: '.next_btn'
              }} thumbs={{
                swiper:
                  thumbsSwiper && !thumbsSwiper.destroyed ? thumbsSwiper : null,
              }}
              modules={[FreeMode, Navigation, Thumbs]}
              className="mySwiper2 ImageBlockSlide"
            >
              {images.map((data, index) => (
                <SwiperSlide key={index}>
                  <div className={comon.swiper_image}>
                    <img src={data} alt={`Slide ${index + 1}`} />
                  </div>
                </SwiperSlide>
              ))}
              <div className="thumb_swiper_slide_btn">
                <div className="prev_btn swiper_btn ">
                  <Image
                    src="/images/next_icon1.svg"
                    height={30}
                    width={30}
                    alt="next"
                    quality={100}
                  />
                </div>
                <div className="next_btn swiper_btn">
                  <Image
                    src="/images/next_icon1.svg"
                    height={30}
                    width={30}
                    alt="prev"
                    quality={100}
                  />
                </div>
              </div>
            </Swiper> */}

            {/* -----thumbs------ */}
            {/* <Swiper
              onSwiper={setThumbsSwiper}
              spaceBetween={10}
              slidesPerView={3}
              freeMode={true}
              watchSlidesProgress={true}
              modules={[FreeMode, Navigation, Thumbs]}
              className="mySwiper ImageBlockSlideThumbs"
              breakpoints={{
                500: {
                  slidesPerView: 4,
                },
              }}
            >
              {images.map((data, index) => (
                <SwiperSlide key={index}>
                  <div className={`${comon.swiper_image_thumbs} ${comon.border}`}>
                    <img src={data} alt={`Thumbnail ${index + 1}`} />
                  </div>
                </SwiperSlide>
              ))}
            </Swiper> */}
          </Box>
        </Modal>
      </div>
    </section>
  );
};

export default ImageGrid;
