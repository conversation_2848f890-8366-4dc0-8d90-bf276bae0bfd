@import "variable", "base", "mixin";

.card_section {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100dvh;
    position: relative;
    pointer-events: none;

    // @media #{$media-700} {
    //     // padding: 10%;
    //     // height: calc(100dvh - 50px);
    //     // padding: 120px 0;
    // }
}

.card_container {
    overflow: hidden;
}

.gift_block {
    max-width: 522px;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    background: #e2dfed;
    position: relative;
    padding: 45px 2.3%;
    text-align: center;
    z-index: 100;
    // z-index: 2;
    pointer-events: all;
    overscroll-behavior: contain;


    .gift_content {
        overflow-y: auto;
        width: 100%;
        padding-right: 10px;
        height: 445px;


        h6 {
            color: #504785;
            font-weight: 500;
            font-size: 16px;
            margin-bottom: 6px;

            @media #{$media-820} {
                font-size: 16px;
            }
        }

        h5 {
            color: #3b3064;
            @include rem(22);
            font-weight: 500;

            @media #{$media-820} {
                font-size: 18px;
            }
        }

        &::-webkit-scrollbar {
            // background-color: #3700ff00;
            width: 11px;

            @media #{$media-700} {
                width: 8px;
            }
        }

        &::-webkit-scrollbar {
            // background-color: #3700ff00;
            width: 11px;

            @media #{$media-700} {
                width: 8px;
            }
        }

        &::-webkit-scrollbar-track {
            border: 1px solid #00000000;
            margin-top: 100px;
            margin-bottom: 30px;
            background: linear-gradient(90deg,
                    rgba(206, 38, 38, 0) 47%,
                    #838383 48%,
                    #838383 52%,
                    rgba(255, 255, 255, 0) 53%);
            border-radius: 12px;
            width: 100%;
            position: relative;
        }

        &::-webkit-scrollbar-thumb {
            background-color: #c9c5d8;

            border: 1px solid #c9c5d8;
            border-radius: 16px;
        }

        &::-webkit-scrollbar-thumb:hover {
            border: 1px solid #514491;
            background-color: #514491;
        }

        &::-webkit-scrollbar-button {
            display: none;
        }


        @media #{$media-820} {
            height: 400px;
            padding: 15px;
        }

        @media #{$media-700} {
            height: 100%;
            max-height: 70dvh;
        }

        @media #{$media-500} {
            // height: 350px;
            padding: 10px 15px;
        }
    }



    @media #{$media-820} {
        padding-top: 20px;
        padding-bottom: 20px;
    }

    @media #{$media-500} {
        padding-top: 15px;
        padding-bottom: 15px;
    }
}

:global(body.rtl) .gift_block .gift_content {
    padding-right: 0;
    padding-left: 10px;
}

.gift_txt_block {
    max-width: 375px;
    margin-left: auto;
    margin-right: auto;

    p {
        line-height: 150%;
        @include rem(15);
        font-size: 15px;
        color: #3b3064;

        @media #{$media-820} {
            @include rem(21);
            line-height: 2rem;
        }

        @media #{$media-700} {
            @include rem(19);
            line-height: 1.6rem;
        }
    }
}

.swiper_btn_section {
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    pointer-events: none;
    z-index: 1;

    .swiper_btn {
        width: 48%;
        height: 100%;
        pointer-events: all;
        cursor: pointer;
        // background-color: rgba(145, 91, 199, 0.678);
    }
}