import React, { useEffect, useState } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";
import { MotionPathPlugin } from "gsap/dist/MotionPathPlugin";
import styles from "@/styles/AirAnimPageNew.module.scss";
import rtl from "@/styles/rtl.module.scss";
import Image from "next/image";

gsap.registerPlugin(ScrollTrigger, MotionPathPlugin);





const AirAnimPageNew = () => {
    useEffect(() => {
        const initPathAnimation = () => {
            const path = document.querySelector("#motionPath");
            if (!path) return;

            const pathLength = path.getTotalLength();
            gsap.set('.arrow', { rotate: "+=270" });

            let motionPathConfig = {
                path: "#motionPath",
                align: "#motionPath",
                alignOrigin: [0.5, 0.5],
                autoRotate: [true, 215],
            };

            let startTrigger = "top 60%";
            let endTrigger = "bottom -100%";

            if (window.matchMedia("(min-width: 1441px)").matches) {
                startTrigger = "top 60%";
                endTrigger = "bottom -100%";
            } else if (window.matchMedia("(min-width: 1281px) and (max-width: 1440px)").matches) {
                startTrigger = "top 70%";
                endTrigger = "bottom -50%";
            } else if (window.matchMedia("(min-width: 1025px) and (max-width: 1280px)").matches) {
                startTrigger = "top 70%";
                endTrigger = "bottom -10%";
            } else if (window.matchMedia("(min-width: 821px) and (max-width: 1024px)").matches) {
                startTrigger = "top 60%";
                endTrigger = "bottom 50%";
            } else if (window.matchMedia("(min-width: 601px) and (max-width: 820px)").matches) {
                startTrigger = "top 75%";
                endTrigger = "bottom 60%";
            } else if (window.matchMedia("(min-width: 450px) and (max-width: 600px)").matches) {
                startTrigger = "top 45%";
                endTrigger = "bottom 60%";
            } else if (window.matchMedia("(min-width: 350px) and (max-width: 449px)").matches) {
                startTrigger = "top 50%";
                endTrigger = "bottom 60%";
            }

            // Show path always, but animate draw
            gsap.set(path, {
                strokeDasharray: pathLength - 55,
                strokeDashoffset: pathLength,
            });

            gsap.timeline({
                scrollTrigger: {
                    trigger: ".plane-trigger",
                    start: startTrigger,
                    end: endTrigger,
                    scrub: 1,
                    markers: false,
                },
            })
                .to(".arrow", {
                    ease: "none",
                    motionPath: motionPathConfig,
                    onStart: () => {
                        gsap.set('.arrow', { rotate: "+=270" });
                    },
                    onUpdate: () => {
                        gsap.set('.arrow', { rotate: "+=270" });
                    },
                })
                .to("#motionPath", {
                    strokeDashoffset: 0,
                    ease: "none",
                }, 0);
        };

        if (document.readyState === 'complete') {
            // If page already loaded
            setTimeout(initPathAnimation, 50);
        } else {
            // If not yet loaded
            window.addEventListener("load", () => setTimeout(initPathAnimation, 50));
        }
    }, []);



    const [viewBox, setViewBox] = useState(null);

    useEffect(() => {
        const getViewBox = () => {
            const width = window.innerWidth;
            if (width > 1281) {
                return "0 0 1700 3058"; // Desktop
            } else if (width >= 1025) {
                return "0 0 1700 3500"; // Tablet
            }
            else if (width >= 950) {
                return "0 0 1700 4500"; // Tablet
            }
            else if (width >= 768) {
                return "0 0 1700 5000"; // Tablet
            }
            else if (width >= 500) {
                return "0 0 1700 6000"; // Tablet
            }

            else {
                return "0 0 1700 6000"; // Mobile
            }
        };

        // Set initial viewBox
        setViewBox(getViewBox());

        // Update on resize
        const handleResize = () => {
            setViewBox(getViewBox());
        };

        window.addEventListener('resize', handleResize);

        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, []);



    return (

        <div className={`${styles.airplane_wrap_new} ${rtl.airplane_wrap_new} plane-trigger`}>
            <svg
                // viewBox="0 0 1700 3058"
                viewBox={viewBox}

                xmlns="http://www.w3.org/2000/svg" className={`${styles.airplane_svg}  `}>
                <path
                    id="motionPath"
                    fill="none"
                    stroke="#3B3064"
                    stroke-width="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M1672.17 231.675C1658.17 ************.9 242.675 1558.9 178.675C1487.64 98.6749 1423.69 138.175 1425.52 188.675C1427.35 239.175 1511.85 206.173 1455.67 145.673C1399.48 85.1733 1290.69 40.5169 1170.65 143.132C908 367.634 121 461.632 121 850.132C121 1315.63 1523.63 1362.13 1523.63 1850.63C1523.63 2378.13 267.629 2028.13 294.129 2598.13C325.954 3282.67 1504.61 2562.41 1532.11 3177.41C1560.61 3814.85 229.654 3482.41 275.106 3985.41C320.106 4483.41 1586.11 4393.41 1985.61 4110.91"
                />
                <image
                    className={`${styles.plane_img} arrow`}
                    href="/images/air_img1.svg"
                    x="0"
                    y="0"
                    width="100"
                    height="60"
                    quality={100}
                />
            </svg>
        </div>

    );
};

export default AirAnimPageNew;
