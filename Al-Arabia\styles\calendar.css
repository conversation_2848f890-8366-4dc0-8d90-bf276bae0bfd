.MuiDateCalendar-root {
  background: #fff;
}

/* .MuiTypography-root{
    width: 56px !important;
    height: 60px !important;
}

.css-4k4mmf-MuiButtonBase-root-MuiPickersDay-root{

    width: 56px !important;
    height: 56px !important;
} */

.css-1rl1vrc-MuiDayCalendar-header {
  border-bottom: solid 1px #000 !important;
}

.rdrSelected,
.rdrInRange,
.rdrStartEdge,
.rdrEndEdge,
.rdrDay,
.rdrDayWeekend,
.rdrDayEndOfWeek,
.rdrStartEdge,
.rdrEndEdge {
  border-radius: 0 !important;
  color: #6758b6 !important;
}

.dark_theme .rdrSelected,
.dark_theme .rdrInRange,
.dark_theme .rdrStartEdge,
.dark_theme .rdrEndEdge,
.dark_theme .rdrDay,
.dark_theme .rdrDayWeekend,
.dark_theme .rdrDayEndOfWeek,
.dark_theme .rdrStartEdge,
.dark_theme .rdrEndEdge {
  color: #ffffff !important;
}

.rdrDayDisabled {
  background-color: transparent !important;
}

.dark_theme .rdrDay:not(.rdrDayPassive) .rdrInRange~.rdrDayNumber span,
.dark_theme .rdrDay:not(.rdrDayPassive) .rdrStartEdge~.rdrDayNumber span,
.dark_theme .rdrDay:not(.rdrDayPassive) .rdrEndEdge~.rdrDayNumber span,
.dark_theme .rdrDay:not(.rdrDayPassive) .rdrSelected~.rdrDayNumber span {
  color: rgb(0 0 0 / 85%) !important;
}

.dark_theme .rdrWeekDay {
  color: rgb(255 255 255) !important;
}

.rdrDayStartPreview,
.rdrDayInPreview,
.rdrDayEndPreview {
  border-radius: 0 !important;
  background-color: hsla(250, 39%, 53%, 0.1) !important;

  border: 1px solid #6758b6 !important;
}

.dark_theme .rdrDayStartPreview,
.dark_theme .rdrDayInPreview,
.dark_theme .rdrDayEndPreview {
  border: 1px solid #ffffff !important;
}

.rdrMonthAndYearPickers select {
  padding: 10px 0px !important;
  font-size: 18px !important;
  /* color: #6758b6 !important; */
  color: #3b3064 !important;

  font-weight: 600 !important;

  background: url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 9 6' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg fill='%230E242F' fill-opacity='0.37'%3E%3Cpath d='M1 1l4 sdfdsf4 4-4'/%3sdfdsfE%3C/g%3E%3C/svg%3E") no-repeat !important;
}

.rdrWeekDay {
  line-height: 2.2rem !important;
  font-weight: 800 !important;
  color: #3b3064 !important;
  font-family: "Aller", sans-serif;

}

.rdrMonth .rdrWeekDays {
  border-bottom: 1px solid rgb(228, 228, 228) !important;
}

.dark_theme .rdrMonthAndYearPickers select {
  color: #ffffff !important;
}

.rdrMonthAndYearPickers select::-ms-expand {
  display: none;
}

.rdrNextPrevButton {
  background: #ffffff !important;
}

.dark_theme .rdrNextPrevButton {
  background: #242424 !important;
}

.rdrDayToday .rdrDayNumber span:after {
  position: unset !important;
}

.rdrCalendarWrapper {
  font-size: 16px !important;
}

.dark_theme .rdrCalendarWrapper {
  background: #242424 !important;
  border: 1px solid #606060 !important;
}

.dark_theme .rdrDayNumber span {
  color: #ffffff !important;
}

.dark_theme .rdrDayDisabled .rdrDayNumber span {
  color: #929292 !important;
}

.rdrDayPassive .rdrDayNumber span {
  color: #d5dce0 !important;
}

.rdrDateRangePickerWrapper .rdrDayNumber span {
  color: #3b3064 !important;
  font-family: 'Aller' !important;
}

.dark_theme .rdrDateRangePickerWrapper .rdrDayNumber span {
  color: #ffffff !important;
}


.dark_theme .rdrDateRangePickerWrapper .rdrDayDisabled span {
  color: #d5dce0 !important;
}

.rdrDay:not(.rdrDayPassive) .rdrInRange~.rdrDayNumber span,
.rdrDay:not(.rdrDayPassive) .rdrStartEdge~.rdrDayNumber span,
.rdrDay:not(.rdrDayPassive) .rdrEndEdge~.rdrDayNumber span,
.rdrDay:not(.rdrDayPassive) .rdrSelected~.rdrDayNumber span {
  color: rgba(255, 255, 255, 0.85) !important;
}

.rdrDayHovered .rdrNumber {
  border: 0 !important;
}

.rdrDefinedRangesWrapper {
  display: none;
}

.rdrNextPrevButton i {
  display: none !important;
}

.rdrNextPrevButton {
  width: 10px !important;
  background-image: url(../public/images/calendar-next.svg) !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  background-size: contain !important;
}

.rdrPprevButton {
  transform: scaleX(-1) !important;
}

.rdrMonthAndYearWrapper {
  width: 95% !important;
  margin: auto !important;
  height: unset !important;
  padding: 5px 0;
}

.rdrMonthPicker,
.rdrYearPicker {
  margin: 0 !important;
}

.rdrMonth {
  width: 536px !important;
  padding: 0 10px 10px 10px !important;
}

@media screen and (max-width: 1440px) {
  .rdrMonth {
    width: 27.667em !important;
    padding: 0 10px 10px 10px !important;
  }
}

.rdrDayDisabled span {
  color: #d5dce0 !important;
}

.rdrDayPassive {
  visibility: hidden !important;
  pointer-events: none !important;
}

.rdrDay {
  height: 48px !important;
}

.rdrDay.rdrDayHovered span {
  width: 100% !important;
}

.rdrSelected,
.rdrInRange,
.rdrStartEdge,
.rdrEndEdge {
  top: 3px !important;
  bottom: 3px !important;
}

@media screen and (max-width: 950px) {
  .rdrCalendarWrapper {
    font-size: 13px !important;
  }

  .rdrDay {
    height: 40px !important;
  }
}

@media screen and (max-width: 820px) {
  .rdrNextPrevButton {
    width: 8px !important;
  }

  .rdrMonth {
    width: 25.667em !important;
    padding: 0 10px 10px 10px !important;
  }
}

@media screen and (max-width: 400px) {

  .rdrMonth {
    width: 23em !important;
    padding: 0 10px 10px 10px !important;
  }

  .rdrDay {
    height: 35px !important;
  }
}

.rtl .rdrMonthAndYearWrapper {
  flex-direction: row-reverse;
}