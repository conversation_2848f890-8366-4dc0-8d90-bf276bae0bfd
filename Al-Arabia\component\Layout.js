import React, { useEffect, useState } from "react";
import Footer from "@/component/Footer";
import localFont from "next/font/local";
import rtl from "@/styles/rtl.module.scss";
import { useRouter } from "next/router";
import comon from "@/styles/comon.module.scss";
import AOS from "aos";
import "aos/dist/aos.css";
import HeaderCenterNew from "./HeaderCenterNew";


// -------- Font Implementation Start --------
const allerIt = localFont({
  src: "../public/fonts/aller_lt-webfont.woff",
  variable: "--aller_lt",
});


const arbfonts = localFont({
  src: "../public/fonts/arbfonts-ge_thameen_book.woff",
  variable: "--arbfonts",
});


const arbfontsbld = localFont({
  src: "../public/fonts/arbfonts-ge_thameen_demibold.woff",
  variable: "--arbfontsbld",
});
const arbfontsBase = localFont({
  // src: "../public/fonts/JFFlat-Regular.woff",
  src: "../public/fonts/Cairo-Regular.woff",
  variable: "--arbfontsBase",
});




const allerRg = localFont({
  src: "../public/fonts/aller_rg-webfont.woff",
  variable: "--aller_rg",
});

const allerDisplay = localFont({
  src: "../public/fonts/allerDisplay_Std_Rg.woff",
  variable: "--allerDisplay",
});

const allerBd = localFont({
  src: "../public/fonts/aller_bd-webfont.woff",
  variable: "--aller_bd",
});

const allerBdit = localFont({
  src: "../public/fonts/aller_std_bdIt.woff",
  variable: "--aller_bdit",
});

const allerStdIt = localFont({
  src: "../public/fonts/aller_std_It.woff",
  variable: "--aller_it",
});
// -------- Font Implementation End --------

const Layout = (props) => {
  const [language, setLanguage] = useState("");
  const router = useRouter();
  const { pathname, asPath } = router;
  const yoast_head = props?.pageData?.yoast_head;

  useEffect(() => {
    if (router.locale === "ar") {
      setLanguage("ar");
      document.body.classList.add("rtl", rtl.rtl);
      document.documentElement.setAttribute("dir", "rtl");
      document.documentElement.setAttribute("lang", "ar");
    } else {
      setLanguage("en");
      document.body.classList.remove("rtl", rtl.rtl);
      document.documentElement.setAttribute("dir", "ltr");
      document.documentElement.setAttribute("lang", "en");
    }
  }, [router]);

  // 🚀 Dark Mode Detection (Chrome Fix)
  useEffect(() => {
    const darkModeQuery = window.matchMedia("(prefers-color-scheme: dark)");

    const updateTheme = () => {
      if (darkModeQuery.matches) {
        document.body.classList.add("dark-mode");
      } else {
        document.body.classList.remove("dark-mode");
      }
    };

    updateTheme(); // ✅ Ensure it runs on initial load

    // ✅ Use both addEventListener & addListener (for compatibility)
    if (darkModeQuery.addEventListener) {
      darkModeQuery.addEventListener("change", updateTheme);
    } else if (darkModeQuery.addListener) {
      darkModeQuery.addListener(updateTheme); // Fallback for older browsers
    }

    return () => {
      if (darkModeQuery.removeEventListener) {
        darkModeQuery.removeEventListener("change", updateTheme);
      } else if (darkModeQuery.removeListener) {
        darkModeQuery.removeListener(updateTheme);
      }
    };
  }, []);

  // 🚀 Initialize Locomotive Scroll
  useEffect(() => {
    (async () => {
      const LocomotiveScroll = (await import("locomotive-scroll")).default;
      new LocomotiveScroll({
        el: document.querySelector("#main-element"),
        smooth: true,
      });
    })();
  }, []);




  useEffect(() => {
    AOS.init({
      easing: "ease-out",
      duration: 1000,
    });
  }, []);

  let scrollRef = 0;
  useEffect(() => {

    window.addEventListener("resize", handleScrollOrResize);
    window.addEventListener("scroll", handleScrollOrResize);

    function handleScrollOrResize() {
      if (scrollRef <= 10) {
        scrollRef++;
      } else {
        AOS.refresh();
      }
    }
  }, []);

  // prevent download
  useEffect(() => {
    const preventImageRightClick = (e) => {
      if (e.target.tagName === "IMG") {
        e.preventDefault();
      }
    };

    document.addEventListener("contextmenu", preventImageRightClick);

    return () => {
      document.removeEventListener("contextmenu", preventImageRightClick);
    };
  }, []);

  useEffect(() => {
    const disableRightClickAndShortcuts = (e) => {
      // Disable right-click anywhere
      e.preventDefault();
    };

    const disableInspectShortcuts = (e) => {
      // Block F12, Ctrl+Shift+I/J/C, and Ctrl+U
      if (
        e.key === "F12" ||
        (e.ctrlKey && e.shiftKey && (e.key === "I" || e.key === "J" || e.key === "C")) ||
        (e.ctrlKey && e.key === "U")
      ) {
        e.preventDefault();
      }
    };

    document.addEventListener("contextmenu", disableRightClickAndShortcuts);
    document.addEventListener("keydown", disableInspectShortcuts);

    return () => {
      document.removeEventListener("contextmenu", disableRightClickAndShortcuts);
      document.removeEventListener("keydown", disableInspectShortcuts);
    };
  }, []);


  return (
    <React.Fragment>
      <main
        id="main-element"
        className={`${comon.main} main ${allerIt.variable} ${allerRg.variable} ${allerDisplay.variable} ${allerBd.variable} ${arbfonts.variable} ${arbfontsbld.variable}  ${arbfontsBase.variable}     ${allerBdit.variable} ${allerStdIt.variable}`}
      >
        <HeaderCenterNew />
        {props.children}
        <Footer />
      </main>
    </React.Fragment>
  );
};

export default Layout;
