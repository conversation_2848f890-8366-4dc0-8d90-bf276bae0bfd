'use client';

import { useEffect, useState } from 'react';

export default function MapPage() {
  const [structuredData, setStructuredData] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchData() {
      try {
        const excelResponse = await fetch('/api/readExcel');
        if (!excelResponse.ok) {
          throw new Error(`HTTP error! status: ${excelResponse.status} from /api/readExcel`);
        }
        const excelJson = await excelResponse.json();
        const excelRows = excelJson.data;

        if (!excelRows || excelRows.length === 0) {
          setStructuredData({});
          setLoading(false);
          return;
        }

        const newStructuredData = {};

        const kmlFetchPromises = excelRows.map(async (row) => {
          const productName = row['Product'];
          const country = row['Country'];
          const city = row['City'];
          let locationLink = row['Location'];

          if (!locationLink || typeof locationLink !== 'string') {
            console.warn('Skipping row due to missing or invalid Location link:', row);
            return;
          }

          try {
            const urlObj = new URL(locationLink);
            const mid = urlObj.searchParams.get('mid');
            if (mid) {
              locationLink = `https://www.google.com/maps/d/kml?mid=${mid}`;
            } else {
              console.warn('Could not extract valid mid from:', locationLink);
              return; 
            }
          } catch (e) {
            console.warn('Invalid URL:', locationLink, e);
            return;
          }

          if (!locationLink || !productName || !country || !city) {
            console.warn('Skipping row due to missing data after URL cleanup:', row);
            return;
          }

          try {
            const kmlResponse = await fetch(`/api/locations?kmlUrl=${encodeURIComponent(locationLink)}`);
            if (!kmlResponse.ok) {
              let errorDetails = {};
              try {
                errorDetails = await kmlResponse.json();
              } catch (jsonParseError) {
                errorDetails.detail = kmlResponse.statusText;
              }
              console.error(`Failed to fetch KML for ${locationLink}: ${kmlResponse.status} - ${errorDetails.detail || kmlResponse.statusText}`);
              return;
            }
            const kmlJson = await kmlResponse.json();
            const coordinates = kmlJson.coordinates;

            if (!newStructuredData[productName]) {
              newStructuredData[productName] = {};
            }
            if (!newStructuredData[productName][country]) {
              newStructuredData[productName][country] = {};
            }
            if (!newStructuredData[productName][country][city]) {
              newStructuredData[productName][country][city] = [];
            }

            newStructuredData[productName][country][city].push(...coordinates);

          } catch (kmlErr) {
            console.error(`Error processing KML for ${locationLink}:`, kmlErr);
          }
        });

        await Promise.allSettled(kmlFetchPromises);
        setStructuredData(newStructuredData);

      } catch (err) {
        console.error('Overall data fetching error:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  useEffect(() => {
    console.log("Structured Data:", structuredData);
  }, [structuredData]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <p className="text-lg text-gray-700">Loading map data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-red-100 text-red-800">
        <p className="text-lg">Error: {error}</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8 font-sans">
      <h1 className="text-4xl font-bold text-center text-gray-800 mb-10">Location Inventory</h1>
      {Object.keys(structuredData).length === 0 ? (
        <p className="text-center text-gray-600 text-xl">No location data available.</p>
      ) : (
        <div className="max-w-4xl mx-auto bg-white shadow-lg rounded-xl p-6">
          {Object.entries(structuredData).map(([productName, countries]) => (
            <div key={productName} className="mb-8 p-4 border border-gray-200 rounded-lg bg-gray-50">
              <h2 className="text-3xl font-semibold text-blue-700 mb-4">{productName}</h2>
              {Object.entries(countries).map(([countryName, cities]) => (
                <div key={countryName} className="ml-4 mb-6 p-3 border-l-4 border-blue-300">
                  <h3 className="text-2xl font-medium text-gray-700 mb-3">{countryName}</h3>
                  {Object.entries(cities).map(([cityName, coordsArray]) => (
                    <div key={cityName} className="ml-4 mb-4 p-2 bg-white rounded-md shadow-sm">
                      <h4 className="text-xl font-normal text-gray-600 mb-2">{cityName}</h4>
                      <ul className="list-disc list-inside text-gray-500">
                        {coordsArray.length > 0 ? (
                          coordsArray.map((coord, index) => (
                            <li key={index} className="mb-1">
                              Latitude: {coord.latitude.toFixed(6)}, Longitude: {coord.longitude.toFixed(6)}
                            </li>
                          ))
                        ) : (
                          <li>No coordinates found for this city.</li>
                        )}
                      </ul>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
