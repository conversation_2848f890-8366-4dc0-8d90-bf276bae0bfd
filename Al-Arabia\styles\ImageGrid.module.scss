@import "variable", "base", "mixin";

.image_grid_layout {
    display: grid;
    padding-left: 5px;
    flex-wrap: wrap;
    grid-template-columns: repeat(5, 1fr);
    // grid-template-rows: repeat(4, 1fr);
    grid-auto-rows: 350px;
    margin-bottom: 5px;
    // auto-rows: minmax(100px, auto); /* Let rows auto-size */

    gap: 5px;
    width: 100%;

    a,
    .image_sec {
        width: 100%;
        cursor: pointer;
        overflow: hidden;

        >img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.3s ease;
        }


        &:hover img {
            transform: scale(1.05);
        }
    }

    :global {
        .grid_sec1 {
            grid-column: span 1;
        }

        .grid_sec2 {
            // grid-column-start: 2;
            // grid-row-start: 1;
            // grid-row-end: 3;
            grid-row: span 2;

        }

        .grid_sec3 {
            // grid-column: span 2;
            // grid-column-start: 3;
            // grid-column-end: 5;
            grid-column: span 2;

        }

        .grid_sec4 {

            grid-column: span 1;
        }

        .grid_sec5 {
            grid-column: span 1;

        }

        .grid_sec6 {
            grid-column: span 1;

        }

        .grid_sec7 {
            grid-column: span 2;

        }

        .grid_sec8 {
            // grid-column: span 3;
            // grid-row-start: 4;
            grid-column: span 3;

        }

        .grid_sec9 {
            // grid-column-start: 4;
            // grid-row-start: 4;
            grid-column: span 1;

        }

        .grid_sec10 {
            // grid-column-start: 5;
            // grid-row-start: 4;
            grid-column: span 1;

        }
    }

    @media #{$media-1600} {
        grid-auto-rows: 250px;

    }
    @media #{$media-1024} {
        grid-auto-rows: 180px;

    }
    @media #{$media-820} {
        grid-auto-rows: 150px;

    }

    @media #{$media-700} {
        width: 100%;
        display: flex;
        padding-left: 3px;
        padding-right: 3px;
    }
}

.image_grid_popup {

    position: relative;

    .btn {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);

        height: auto;
        width: 25px;
        z-index: 1;
        background-color: rgb(245, 245, 245);
        padding: 5px;
        cursor: pointer;

        &.prev {
            left: 1px;
        }

        &.next {
            right: 1px;
        }

        img {
            height: auto;
            width: 100%;
            object-fit: contain;
            display: block;
        }


        @media #{$media-700} {
            background-color: rgba(245, 245, 245, 0.733);
            width: 20px;

        }
    }
}