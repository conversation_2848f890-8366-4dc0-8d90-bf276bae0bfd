import React, { useEffect, useState } from "react";
import style from "@/styles/ReactSelect.module.scss";
import rtl from "@/styles/rtl.module.scss";
import Select from "react-select";
import { useRouter } from "next/router";
const SelectDropDown = ({ options = [], DarkTheme = false, onChange, value, disabled = false, classN }) => {
    const [selectOption, setSelectOption] = useState(null);
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        setIsMounted(true);
        if (options.length > 0) {
            setSelectOption(options);
        }
    }, [options]);

    const {locale} = useRouter()

    if (!isMounted) return null;
    return (
        <div className={`${style.custom_select} ${rtl.custom_select} ${DarkTheme == true ? style.dark_select : ''}`}
            data-lenis-prevent={true}
        >
            <Select
                value={options.find(opt => String(opt.value) === String(value)) || null}

                onChange={(selectedOption) => onChange(selectedOption.value)}
                options={options} data-lenis-prevent={true}
                classNamePrefix="selectes"

                isDisabled={disabled}
                className={classN}
                placeholder={locale=='ar' ? 'اختر':'Select'}
            />
        </div>
        
    );
};

export default SelectDropDown;
