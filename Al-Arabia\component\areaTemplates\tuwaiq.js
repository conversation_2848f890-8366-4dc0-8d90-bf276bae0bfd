import React from "react";
import { useState, useRef } from "react";
import Link from "next/link";
import Image from "next/image";
import HeadMarquee from "@/component/HeadMarquee";
import comon from "@/styles/comon.module.scss";
import style from "@/styles/altanfeethi.module.scss";
import SliderSectionDark from "@/component/SliderSectionDark";
import InnerBanner from "@/component/InnerBanner";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper/modules";
import rtl from "@/styles/rtl.module.scss";
import "swiper/css";
import "swiper/css/navigation";
import SwiperHover from "@/component/SwiperHover";
import ContactSection from "@/component/ContactSection";
import StartCampaign from "@/component/StartCampaign";
import HoverSlide from "@/component/HoverSlide";
import Head from "next/head";
import parse from 'html-react-parser';
import SliderBannerText from "@/component/SliderBannerText";


const Tuwaiq = ({ pageData }) => {
	const [currentIndex, setCurrentIndex] = useState(1);
	const swiperRef = useRef(null);
	const [sliderIndex, setSliderIndex] = useState("");

	return (
		<div>
			{/* <Head>
				<title>Tuwaiq</title>
				<meta name="description" content="Generated by create next app" />
			</Head> */}
			<HeadMarquee />
			{pageData.acf.banner_details.description_field ? (

				<SliderBannerText
					title={pageData.acf.banner_details.title}
					paragraph={pageData.acf.banner_details.description_}
					details={pageData.acf.banner_details}
					className={'width_870'}
					bgColor={true}
					extra_height_class={true}
					full_height_banner={true}
				/>
			) : (
				<InnerBanner darkBackground={true} showLink={false} title={pageData.acf.banner_details.title} details={pageData.acf.banner_details} />
			)}

			<section className={`dark_bg ${comon.pb_65}`}>
				<div className={`${comon.wrap}  ${comon.pt_35}`}>
					<ul
						className={`${comon.mb_30} ${comon.detail_breadcrumb} ${comon.breadcrumb_white} `}
					>
						<li>
							<Link href={"/"}>
								<div className={` ${comon.bread_icon}  `}>
									<Image
										src={"/images/breadcumb_home_white.svg"}
										height={30}
										width={30}
										alt=""
									/>
								</div>
							</Link>
						</li>
						{Array.isArray(pageData?._embedded?.up) && pageData._embedded.up[0]?.parent_slug && pageData._embedded.up[0]?.parent_title && (
                            <li>
                                <Link href={pageData._embedded.up[0].parent_slug}>
                                {pageData._embedded.up[0].parent_title}
                                </Link>
                            </li>
                        )}
						{pageData.parent_title && (
							<li>
								<Link href={`${pageData.parent_slug}`}>{pageData.parent_title}</Link>
							</li>
						)}

						<li>
							<Link href={"/access-road"} className={` ${comon.active}`}>
								{pageData.title.rendered}
							</Link>
						</li>
					</ul>
				</div>
				<div
					className={`${comon.wrap} ${comon.d_flex_wrap} ${comon.d_flex_wrap} ${style.tuwqiq_banner_wrap}`}
					data-aos="fade-up"
					data-aos-duration="1000"
				>
					<div className={`${style.tuwqiq_banner_image}`}>
						<Image
							src={pageData.acf.description.main_image}
							height={595}
							width={386}
							alt=""
							quality={100}
						/>
					</div>
					<div
						className={`${style.tuwqiq_banner_content} ${rtl.tuwqiq_banner_content}`}
					>
						<Image
							src={pageData.acf.description.title_image}
							height={36}
							width={197}
							alt=""
							quality={100}
						/>

						<>
							{parse(pageData.acf.description.description_left)}
						</>
					</div>
				</div>
			</section>

			{/* <section
				className={`${comon.pb_60} ${comon.pt_60} ${comon.grey_gradient}`}
			>
				<div
					className={`${comon.wrap} ${comon.d_flex_wrap} ${style.altanfeethi_slider_wrap}`}
					data-aos="fade-up"
					data-aos-duration="1000"
				>
					<div className={`${style.altanfeethi_select}`}>
						<select className={`${comon.select} ${comon.white}`}>
							<option>Access Road</option>
							<option>Access Road</option>
							<option>Access Road</option>
						</select>
					</div>

					<Swiper
						onSwiper={(swiper) => (swiperRef.current = swiper)}
						onSlideChange={(swiper) => setCurrentIndex(swiper.realIndex + 1)}
						loop={true}
						spaceBetween={10}
						className="w-full h-full"
					>
						{slides.map((slide) => (
							<SwiperSlide
								key={slide.id}
								className="flex items-center justify-center"
							>
								<div className={`${comon.d_flex_wrap}`}>
									<div className={`${style.altanfeethi_slider_img}`}>
										<img
											src={slide.mapSrc}
											alt="Map"
											className="w-full h-auto"
										/>
									</div>

									<div className={`${style.altanfeethi_slider_content}`}>
										<div className={`${style.slider_img}`}>
											<img
												src={slide.imageSrc}
												alt="Location"
												className="w-full h-auto mb-4"
											/>
										</div>
										<div className={`${style.slider_content}`}>
											<h4>{slide.subtitle}</h4>
											<h3>{slide.title}</h3>
											<p>{slide.paragraph}</p>
										</div>
									</div>
								</div>
							</SwiperSlide>
						))}
					</Swiper>

					<div className={`${style.nav_count_wrap} ${comon.d_flex_wrap}`}>
						<div className={`${style.nav_count}`}>
							<span className={`${style.count_active}`}>
								{String(currentIndex).padStart(2, "0")}
							</span>
							<span className={`${style.count_main}`}>/ {slides.length}</span>
						</div>
						<div className={`${style.nav_btn}`}>
							<button
								onClick={() => swiperRef.current?.slidePrev()}
								className={`${style.nav_prev} ${style.nav}`}
							>
								<Image
									src="/images/prev_icon1.svg"
									width={1200}
									height={600}
									alt="banner"
								/>
							</button>

							<button
								onClick={() => swiperRef.current?.slideNext()}
								className={`${style.nav_next} ${style.nav}`}
							>
							
								<Image
									src="/images/next_icon1.svg"
									width={1200}
									height={600}
									alt="banner"
								/>
							</button>
						</div>
					</div>
				</div>
			</section> */}
			<SwiperHover />

			<section
				className={`${comon.pb_65} ${comon.pt_65}  ${style.pt_overview_gradient}  dark_bg`}
			>
				<div className={`${comon.wrap} ${comon.d_flex_wrap}`}>
					<div
						className={`${comon.title_30} ${comon.w_100} ${comon.white_color}`}
					>
						<h3>{parse(pageData.acf.other_details.title)}</h3>
					</div>
				</div>

				<div>
					<HoverSlide
						season={pageData.acf.other_details.image_list}
						popUpAction={false}
						PopUpMap={true}
						textOverlay={true}
                        extra_width_arrows={true}
                        black_bg_arrows={true}

					/>
				</div>
			</section>

			<section className={`${comon.pb_60} ${comon.pt_60} dark_bg`}>
				<div className={`${comon.wrap} ${comon.d_flex_wrap}`}>
					<div
						className={`${comon.title_30} ${comon.w_100} ${comon.white_color}`}
						data-aos="fade-up"
						data-aos-duration="800"
					>
						<h3 className={`${comon.text_center}   ${comon.text_capitalize}`}>
							{parse(pageData.acf.gallery_details.gallery_title)}
						</h3>
					</div>
				</div>

				<SliderSectionDark
					images={pageData.acf.gallery_details.gallery.map((item) => ({
						img: item,
						description: ''
					}))}
					popUpAction={true}
					activeSlider={(i) => setSliderIndex(i)}
				/>
			</section>

			<StartCampaign bgColor={"#ECECEC"} darkColor={true} />

			<section
				className={`${comon.pt_60} ${comon.pb_10} ${comon.black_bg} `}
				style={{ backgroundImage: "none" }}
			>
				<ContactSection
					title="Request for Quotation"
					paragraph="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore."
					button="Request a Quote"
					showAutoMargin={false}
					className="white_text"
					classNameSecond="form_white"
					whiteBtn={true}
				/>
			</section>
		</div>
	);
};

export default Tuwaiq;
