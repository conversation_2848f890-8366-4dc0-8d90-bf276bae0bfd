import HeadMarquee from "@/component/HeadMarquee";
import SliderBannerText from "@/component/SliderBannerText";
import SliderSection from "@/component/SliderSection";
import React from "react";
import style from "@/styles/dammamAirport.module.scss";
import InnerBanner from "@/component/InnerBanner";
import parse from 'html-react-parser';
const ComingSoon = ({pageData}) => {
    return (
        <div>
            <HeadMarquee />

                {pageData.acf.banner_details.description_field ? (
                
                <SliderBannerText
                    title={pageData.acf.banner_details.title}
                    paragraph={pageData.acf.banner_details.description_}
                    details={pageData.acf.banner_details}
                    className={"width_870"}
                    bgColor={true}
                    extra_height_class={true}
                    mob_padding_120={true}
                />
                ):(
                <InnerBanner  showLink={false} title={pageData.acf.banner_details.title} details={pageData.acf.banner_details} />
                )}
            <div className={style.coming_soon_page}>
                <div className={style.overlap}>
                    <h4>
                        {parse(pageData.acf.title_cmingsoon)}
                    </h4>
                </div>

                <SliderSection
                    images={pageData.acf.gallery_comingsoon.map((item) => ({
                        img: item,
                        description:''
                      }))}
                     title={pageData.acf.gallery_title}
                />
            </div>
        </div>
    );
};

export default ComingSoon;