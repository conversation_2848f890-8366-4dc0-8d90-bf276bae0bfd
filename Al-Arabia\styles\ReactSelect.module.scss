@import "variable", "base", "mixin";

.custom_select {
    width: 100%;

    input {
        @media #{$media-768} {
            font-size: 16px !important;
        }
    }

    :global {
        .selectes {
            &__control {
                cursor: pointer;
                border: 0 !important;
                box-shadow: none !important;
                border-radius: 0px;
                min-height: 48px;

                background-color: #42363600 !important;

            }

            &__value-container {
                padding-left: 0 !important;
            }

            &__control--is-focused {
                border: 0 !important;
                background-color: #42363600 !important;
            }

            &__indicators {
                border: none;
                position: relative;

                svg {
                    display: none;
                }

                &::before {
                    position: absolute;
                    right: 15px;
                    transform: translateY(-50%);
                    top: 50%;
                    content: "";
                    width: 16px;
                    height: 6px;
                    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="6" viewBox="0 0 10 6" fill="none"><path d="M9 1L5 5L1 1" stroke="%23918F90" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>');
                    background-repeat: no-repeat;
                    background-size: contain;
                    background-position: center;




                }


            }

            :global(body.rtl) &__indicators::before {

                left: 15px;
                transform: translateY(50%);
                right: unset;
            }

            &__indicator-separator {
                display: none;
            }

            &__single-value {
                font-size: 15px;
                // color: #231f20;
                color: #3b3064;
                // font-family: var(--aller_lt);
                // font-family: 'Aller', sans-serif;
                font-weight: 500;

            }

            &__menu {
                border: none;
            }

            &__menu-list {
                padding-top: 0;
                padding-bottom: 0;
                border: none !important;
                outline: none;
                box-shadow: none;
                border-radius: 4px;

                &::-webkit-scrollbar {
                    // background-color: #3700ff00;
                    width: 10px;

                    @media #{$media-700} {
                        width: 8px;
                    }

                }

                &::-webkit-scrollbar {
                    // background-color: #3700ff00;
                    width: 5px;


                }

                &::-webkit-scrollbar-track {
                    border: 1px solid #00000000;
                    // margin-top: 30px;
                    // margin-bottom: 30px;
                    background: linear-gradient(90deg,
                            rgba(206, 38, 38, 0) 47%,
                            #838383 48%,
                            #838383 52%,
                            rgba(255, 255, 255, 0) 53%);
                    // border-radius: 12px;
                    width: 100%;
                    position: relative;
                }

                &::-webkit-scrollbar-thumb {
                    // background-color: #c9c5d8;

                    // border: 1px solid #c9c5d8;
                    border: 1px solid #514491;
                    background-color: #514491;
                    // border-radius: 16px;
                }

                &::-webkit-scrollbar-thumb:hover {
                    border: 1px solid #514491;
                    background-color: #514491;
                }

                &::-webkit-scrollbar-button {
                    display: none;
                }

            }

            &__option {
                color: #4d3e8b;
                font-size: 15px;
                cursor: pointer;

                &--is-focused {
                    background-color: #e0d8ff98;
                }

                &--is-selected {
                    background-color: #524592;
                    // color: #3b3064;
                    color: #ffffff;
                }
            }
        }
    }



    &.dark_select {

        :global {
            .selectes {



                &__single-value {

                    color: #ffffff;
                }


                &__option {
                    color: #ffffff;
                    background-color: #181818;


                    &--is-focused {
                        background-color: #2a2541;
                        color: #ffffff;
                    }

                    &--is-selected {
                        color: #ffffff;
                        background-color: #6758b6;

                    }
                }
            }
        }

    }
}