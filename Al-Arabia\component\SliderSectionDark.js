import React, { useEffect, useState } from "react";
import comon from "@/styles/comon.module.scss";
import home from "@/styles/home.module.scss";
import buttion from "@/styles/buttion.module.scss";
import DynamicImage from "@/component/DynamicImage";

import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/scrollbar";
// import { Scrollbar, Autoplay, Navigation } from "swiper/modules";
import Image from "next/image";
import { useRouter } from "next/router";

import {
	Scrollbar,
	Autoplay,
	Navigation,
	Pagination,
	FreeMode,
	Thumbs,
} from "swiper/modules";
import { Box, Modal } from "@mui/material";

const SliderSection = ({ images, description, popUpAction = false, activeSlider }) => {
	const [activeIndex, setActiveIndex] = useState(0);
	// Array of images corresponding to each `li`
	//console.log(images);

	// ==========AOS SECTION==========

	const [open, setOpen] = useState(false);
	const [thumbsSwiper, setThumbsSwiper] = useState(null);

	// Handle modal open and close
	const handleOpen = () => setOpen(true);
	const handleClose = () => {
		setThumbsSwiper(null);
		setOpen(false);
	};

	const router = useRouter();

	return (
		<div>
			<section
				className={`${comon.pt_40}   ${comon.overflow_hide} swipper_home`}
			>
				<Swiper
					data-aos="fade-in"
					data-aos-delay="600"
					data-aos-duration="1000"
					onSwiper={(swiper) => {
						activeSlider(swiper.activeIndex); // This only runs on mount
					}}
					onSlideChange={(swiper) => {
						activeSlider(swiper.activeIndex); // This runs on every slide change
					}}

					initialSlide={1}
					scrollbar={{
						el: ".custom-scrollbar", // Reference to your custom scrollbar div
						draggable: true, // Makes the scrollbar draggable
					}}
					speed={1300}
					autoplay={{
						delay: 3000, // Delay between slides in milliseconds
						disableOnInteraction: false, // Keep autoplay running after user interaction
					}}
					autoHeight={true}
					loop={false}
					slidesPerView="auto"
					centeredSlides={true}
					navigation={{
						prevEl: '.prev_btn',
						nextEl: '.next_btn'
					}}
					modules={[Scrollbar, Autoplay, Navigation]}
					className="project_swiper_main"
					dir={router.locale == "ar" || router.locale == "rtl" ? "rtl" : "ltr"}
				>
					{[...images, ...images, ...images].map((data, index) => (
						<SwiperSlide
							className={`${home.slider_block}`}
							onClick={handleOpen}
						>
							<div className={`${home.slider_block_txt}`}>
								{description && <h4>{data.description}</h4>}
								{/* {images.map((data, index) => ( */}
							</div>

							<Image
								src={data.img}
								fill
								style={{ objectFit: "cover" }}
								alt=""
								quality={100}
							/>

							<Image
								className={`${comon.w_100} ${comon.holder}`}
								src={data.img}
								width={672}
								height={447}
								alt="button image"
								style={{
									height: "auto",
									width: "100%",
									display: "block",
								}}
								quality={100}
							/>
						</SwiperSlide>
					))}
					<div className="thumb_swiper_slide_btn top_adjusted black_bg">
						<div className="prev_btn swiper_btn ">
							<Image
								src="/images/next_icon1.svg"
								height={30}
								width={30}
								alt="next"
								
							/>
						</div>
						<div className="next_btn swiper_btn">
							<Image
								src="/images/next_icon1.svg"
								height={30}
								width={30}
								alt="prev"
							/>
						</div>
					</div>
				</Swiper>

				<div className={`${home.scroll_block} ${home.mt_40}`}>
					<div
						className={`${home.custom_scrollbar} custom-scrollbar custom-scrollbar-white`}
					></div>
				</div>
			</section>

			{/* Modal Sliders*/}
			{popUpAction && (
				<Modal
					className="sliders_popup_sec"
					open={open}
					data-lenis-prevent="true"
					onClose={handleClose}
				>
					<Box className={comon.popup_container}>
						<button className={comon.popup_close} onClick={handleClose}>
							<Image
								src="/images/close_btn.svg"
								height={30}
								width={30}
								alt="Close"
							/>
						</button>

						<Swiper
							spaceBetween={10}
							// navigation
							navigation={{
								prevEl: '.prev_btn',
								nextEl: '.next_btn'
							}} thumbs={{
								swiper:
									thumbsSwiper && !thumbsSwiper.destroyed ? thumbsSwiper : null,
							}}
							modules={[FreeMode, Navigation, Thumbs]}
							className="mySwiper2 ImageBlockSlide"
						>
							{images.map((src, index) => (
								<SwiperSlide key={index}>
									<div className={comon.swiper_image}>
										<img src={src.img} alt={`Slide ${index + 1}`} />
									</div>
								</SwiperSlide>
							))}
							<div className="thumb_swiper_slide_btn">
								<div className="prev_btn swiper_btn ">
									<Image
										src="/images/next_icon1.svg"
										height={30}
										width={30}
										alt="next"
									/>
								</div>
								<div className="next_btn swiper_btn">
									<Image
										src="/images/next_icon1.svg"
										height={30}
										width={30}
										alt="prev"
									/>
								</div>
							</div>
						</Swiper>

						{/* Thumbnails */}
						<Swiper
							onSwiper={setThumbsSwiper}
							spaceBetween={10}
							slidesPerView={3}
							freeMode
							watchSlidesProgress
							modules={[FreeMode, Navigation, Thumbs]}
							className="mySwiper ImageBlockSlideThumbs"
							breakpoints={{
								500: { slidesPerView: 4 },
							}}
						>
							{images.map((src, index) => (
								<SwiperSlide key={index}>
									<div className={comon.swiper_image_thumbs}>
										<img src={src.img} alt={`Thumbnail ${index + 1}`} />
									</div>
								</SwiperSlide>
							))}
						</Swiper>
					</Box>
				</Modal>
			)}
		</div>
	);
};

export default SliderSection;
