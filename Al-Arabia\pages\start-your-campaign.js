import React, { useState, useEffect } from "react";
import Head from "next/head";
import Link from "next/link";
import Image from "next/image";
import rtl from "@/styles/rtl.module.scss";
import InnerBanner from "@/component/InnerBanner";
import CampaignNetwork from "@/component/CampaignNetwork";
import CampaignDates from "@/component/CampaignDates";
import CampaignPreview from "@/component/CampaignPreview"; // New Step
import comon from "@/styles/comon.module.scss";
import campaign from "@/styles/campaign.module.scss";
import HeadMarquee from "@/component/HeadMarquee";
import { FALSE } from "sass";
import { useRouter } from "next/router";
import { fetchPageById } from '@/lib/api/pageApi';
import parse from 'html-react-parser';
import MultiDatePicker from "@/component/MultiDatePicker";
import contactF from "@/styles/contactForm.module.scss";
import RangeCalendar from "@/component/Calendar";
import Modal from "@mui/material/Modal";
import Box from '@mui/material/Box';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';


export default function startYourCampaign({ pageData }) {
  const [step, setStep] = useState(1); // Step 1: CampaignNetwork, Step 2: CampaignDates, Step 3: CampaignPreview
  const banner = pageData.acf.banner_details;
  const nextStep = () => {
    if (step < 3) setStep(step + 1);
  };

  const prevStep = () => {
    if (step > 1) setStep(step - 1);
  };

  const [darkMode, setDarkmode] = useState(false);
  const [plainDark, setPlainDark] = useState(false);

  // const route = useRouter();
  const { locale } = useRouter();
  const route = useRouter();

  useEffect(() => {
    if (route.asPath == "/start-your-campaign?theme" || route.asPath == "/start-your-campaign?theme#") {
      setDarkmode(true);
    } else {
      setDarkmode(false);
    }
  }, [route]);

  useEffect(() => {
    if (route.asPath == "/start-your-campaign?themeblack" || route.asPath == "/start-your-campaign?themeblack#") {
      setPlainDark(true);
      setDarkmode(true);

    } else {
      setPlainDark(false);

    }
  }, [route]);

  // step 1
  const [selected, setSelected] = useState({});

  // const handleChange = (id) => {
  //   setSelected((prev) => ({
  //     ...prev,
  //     [id]: !prev[id],
  //   }));
  // }; 



  const [formData, setFormData] = useState({
    Typeselected: '',
    Cityofcoverage: '',
    Formatsize: '',
    Requiredarea: '',
    Estematedbudget: '',
    startDate: '',
    endDate: '',
    duration: '',
    Fullname: '',
    EmailAddress: '',
    PhoneNumber: '',
    company: '',
    Message: '',
  });

  const [validationErrors, setValidationErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [responseMessage, setResponseMessage] = useState('');
  const [sresponseMessage, setSresponseMessage] = useState('');
  const [fieldErrors, setFieldErrors] = useState({});


  // update values

  useEffect(() => {
    // console.log('hgcvdghcvdghcvhbbbbbbbbbbbbbbbbbbbbbb', formData)
  }, [formData])
  const [selectedValues, setSelectedValues] = useState({
    type: '',
    CommercialElevators: '',
    ResidentialElevators: '',
    DIFCLargeFormat: '',
    OneCentralLargeFormat: '',
  });
  const handleSelectionChange = (groupId, selectedItems) => {
    const groupToKeyMap = Object.fromEntries(
      pageData.acf.preview_items.map((item, index) => [index, item.text])
    );

    if (groupToKeyMap[groupId] !== undefined) {
      const filteredItems = Array.isArray(selectedItems)
        ? selectedItems.filter((item) => item !== 'selectAll')
        : [];

      const key = groupToKeyMap[groupId];
      const value = filteredItems.join(',');
      const sanitizedKey = key.replace(/\s+/g, '');

      console.log(`Updated ${sanitizedKey}`);
      setFormData((prev) => ({
        ...prev,
        [sanitizedKey]: value,
      }));

      setSelectedValues((prev) => ({
        ...prev,
        [key]: value,
      }));


    } else {
      console.warn(`Group ID "${groupId}" not found in groupToKeyMap.`);
    }
  };

  const isAllGroupsSelected = pageData?.acf?.preview_items?.every((item) => {
    const key = item.text;
    const value = selectedValues[key];
    return value && value.trim() !== '';
  });




  const [campaignData, setCampaignData] = useState([]);

  const [formattedData, setFormattedData] = useState({
    duration: "",
    startDate: "",
    endDate: "",
  });
  // stage 2 - select date
  const handleDataChange = (data) => {
    // console.log("Received Campaign Data:", data);

    const formatDate = (date) => {
      if (!date || isNaN(new Date(date).getTime())) return '';
      const options = { day: 'numeric', month: 'long', year: 'numeric' };
      return new Intl.DateTimeFormat('en-GB', options).format(new Date(date));
    };

    setCampaignData(data);
    setFormData((prev) => ({
      ...prev,
      duration: data.dropdown
        ? data.dropdown
        : data.selectedDateRange1?.startDate && data.selectedDateRange1?.endDate
          ? (Math.round(
            (new Date(data.selectedDateRange1.endDate) - new Date(data.selectedDateRange1.startDate)) /
            (1000 * 60 * 60 * 24)
          ) + 1).toString()
          : '',
      startDate: formatDate(data.selectedDateRange1?.startDate),
      endDate: formatDate(data.selectedDateRange1?.endDate),
    }));


  };

  // useEffect(() => {
  //   console.log("campaignData changed:", campaignData);
  // }, [campaignData]);

  const formatDate = (date) => {
    if (!date) return "";
    const options = { day: "numeric", month: "long", year: "numeric" };
    return new Intl.DateTimeFormat("en-GB", options).format(new Date(date));
  };
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setResponseMessage('');
    setValidationErrors({})
    setSresponseMessage('');
    if (name === "PhoneNumber") {
      const numericValue = value.replace(/\D/g, "");
      setFormData({ ...formData, [name]: numericValue });
    }
    if (name === "FirstName" || name == "LastName") {
      const alphabeticValue = value.replace(/[^a-zA-Z]/g, "");
      setFormData({ ...formData, [name]: alphabeticValue });
    }
  };

  const formId = locale === 'ar' ? 3394 : 1214;
  const unitTag = locale === 'ar' ? 'wpcf7-f3394-o1' : 'wpcf7-f1214-o1';

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setResponseMessage('');

    // Validate form data
    // if (!validateForm()) {
    //   setIsSubmitting(false);
    //   return;
    // }

    // Create a new FormData object
    const formDataObj = new FormData();
    formDataObj.append('Fullname', formData.Fullname);
    formDataObj.append('EmailAddress', formData.EmailAddress);
    formDataObj.append('PhoneNumber', formData.PhoneNumber);
    formDataObj.append('company', formData.company);
    formDataObj.append('Message', formData.Message);
    formDataObj.append('type', formData.Typeselected || formData.نوعالحملةالمختار || '');
    formDataObj.append('Cityofcoverage', formData.Cityofcoverage || formData.المدينة || '');
    formDataObj.append('Formatsize', formData.Formatsize || formData.حجمالشكلالإعلاني || '');
    formDataObj.append('Requiredarea', formData.Requiredarea || formData.المساحةالمطلوبة || '');
    formDataObj.append('Estematedbudget', formData.Estematedbudget || formData.الميزانيةالتقديرية || '');
    formDataObj.append('startDate', formData.startDate || '');
    formDataObj.append('endDate', formData.endDate || '');
    formDataObj.append('duration', formData.duration || '');
    formDataObj.append('_wpcf7_unit_tag', unitTag);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_CONTACT_API_URL}/${formId}/feedback`, {
        method: 'POST',
        body: formDataObj,
        redirect: 'follow',
      });

      const result = await response.json();
      console.log(result);
      setIsSubmitting(false);

      if (response.ok) {
        if (result.status === 'validation_failed') {
          const fieldErrors = result.invalid_fields.reduce((acc, fieldError) => {
            acc[fieldError.field] = fieldError.message;
            return acc;
          }, {});
          setFieldErrors(fieldErrors);
          setTimeout(() => {
            setResponseMessage('');
            setFieldErrors({});
          }, 3000);
        } else if (result.status === 'mail_sent') {
          setSresponseMessage(result.message);
          setTimeout(() => {
            setSresponseMessage('');
          }, 3000);
          setFormData({
            Fullname: '',
            company: '',
            EmailAddress: '',
            PhoneNumber: '',
            Message: '',
            type: '',
            CommercialElevators: '',
            ResidentialElevators: '',
            DIFCLargeFormat: '',
            OneCentralLargeFormat: '',
            startDate: '',
            endDate: '',
            duration: '',
          });
        } else {
          setResponseMessage('An unexpected error occurred. Please try again.');
          setTimeout(() => {
            setResponseMessage('');
          }, 3000);
        }
      } else {
        setResponseMessage(result.message || 'Something went wrong. Please try again.');
        setTimeout(() => {
          setResponseMessage('');
        }, 3000);
      }
    } catch (error) {
      console.error('Error:', error);
      setIsSubmitting(false);
      setResponseMessage('An error occurred while submitting the form.');
    }
  };


  return (
    <>

      <HeadMarquee />
      <InnerBanner showLink={false} title={banner.title} details={banner} extraClass="The Future of Work Starts Now" />
      <section
        className={`${comon.pt_65} ${comon.pb_65} ${darkMode == true ? comon.dark_theme : ""
          }  ${plainDark && comon.black_bg_color}`}

      >
        <div className={`${comon.wrap} ${comon.d_flex_wrap}`}>
          {/* Step Components */}

          <form action="#" onSubmit={handleSubmit}
            style={{ width: '100%' }}
          >

            {step === 1 && <CampaignNetwork darkMode={darkMode}
              pageData={pageData}
              onSelectionChange={handleSelectionChange}
            />}
            {/* <pre>{JSON.stringify(selectedValues, null, 2)}</pre> */}

            {step === 2 && <CampaignDates
              onDataChange={handleDataChange}
              darkMode={darkMode}
              pageData={pageData} />}

            {/* {step === 3 && <CampaignPreview darkMode={darkMode} />} */}

            {step == 3 && (
              <>
                <div className={`${comon.w_100} ${comon.d_flex_wrap} ${comon.justify_space_bet} `} >
                  <div className={`${campaign.campaign_detail_left} ${comon.pt_20}  ${darkMode == true ? campaign.dark_theme : ''}`} >

                    <div className={`${comon.title_20} ${comon.head_20} ${comon.mb_20}`} >
                      <h3>{pageData.acf.preview_title_1}</h3>
                    </div>

                    <ul className={`${campaign.selected_list} ${comon.mb_20}`}>
                      {Object.entries(selectedValues).map(([key, value]) => (
                        value ? (
                          <li key={key}>
                            <strong>{key}:</strong>
                            <ul style={{ paddingTop: '10px' }}>
                              {value && value.split(',').map((item, index) => (
                                <li key={index}>{item}</li>
                              ))}
                            </ul>
                          </li>
                        ) : null
                      ))}
                    </ul>


                    {campaignData.selectedDateRange1 &&
                      <>
                        <div className={`${comon.title_20} ${comon.head_20} ${comon.mb_20}`} >
                          <h3>{pageData.acf.preview_title_2}</h3>
                        </div>
                        <ul className={`${campaign.selected_list} ${comon.mb_20}`}>
                          <li className="base_font"

                            style={{ direction: 'ltr' }}>
                            {formatDate(campaignData.selectedDateRange1.startDate)} to {formatDate(
                              campaignData.selectedDateRange1.endDate
                            )}</li>
                        </ul>
                      </>
                    }
                    <div className={`${comon.title_20} ${comon.head_20} ${comon.mb_20}`} >
                      <h3>{pageData.acf.preview_title_3}</h3>
                    </div>
                    <ul className={`${campaign.selected_list} ${comon.mb_40}`}>

                      {campaignData.dropdown ? (
                        <li>
                          {campaignData.dropdown}
                        </li>
                      ) : (
                        <li>
                          {campaignData.selectedDateRange1 && campaignData.selectedDateRange1.startDate && campaignData.selectedDateRange1.endDate ? (
                            <> <span className="base_font">{`${Math.round(
                              (new Date(campaignData.selectedDateRange1.endDate) -
                                new Date(campaignData.selectedDateRange1.startDate)) /
                              (1000 * 60 * 60 * 24)
                            ) + 1}  `}</span> {locale === "ar" ? "أيام" : "Days"} </>
                          ) : (
                            <>{`1 ${locale === 'ar' ? 'يوم' : 'Day'}`}</>

                          )}

                        </li>
                      )}
                    </ul>

                  </div>
                  <div className={`${campaign.campaign_detail_right} ${darkMode == true ? campaign.dark_theme : ''}`} >

                    <ul
                      className={`${contactF.contact_form_main_ul}`}
                    >
                      <li data-aos="fade-up" data-aos-duration="1000" className={`${contactF.form_li}`}>
                        <input
                          className={`${contactF.input_fld} ${contactF.font_14}`}
                          type="text"
                          placeholder={locale == "ar" ? "الاسم" : "Name"}
                          value={formData.Fullname}
                          onChange={handleChange}
                          name="Fullname"
                        ></input>
                        {fieldErrors.Fullname && <p style={{ color: 'red', marginTop: '5px', fontSize: '12px', padding: '0 8px' }}>{fieldErrors.Fullname}</p>}
                      </li>
                      <li data-aos="fade-up" data-aos-duration="1000" className={`${contactF.form_li}`}>
                        <input
                          className={`${contactF.input_fld} ${contactF.font_14}`}
                          type="email"
                          placeholder={locale == "ar" ? "البريد الإلكتروني" : "Email"}
                          value={formData.EmailAddress}
                          onChange={handleChange}
                          name="EmailAddress"
                        ></input>
                        {fieldErrors.EmailAddress && <p style={{ color: 'red', marginTop: '5px', fontSize: '12px', padding: '0 8px' }}>{fieldErrors.EmailAddress}</p>}
                      </li>
                      <li data-aos="fade-up" data-aos-duration="1000" className={`${contactF.form_li}`}>
                        <input
                          className={`${contactF.input_fld} ${contactF.font_14}`}
                          type="text"
                          placeholder={locale == "ar" ? "رقم الهاتف" : "Phone"}
                          value={formData.PhoneNumber}
                          onChange={handleChange}
                          name="PhoneNumber"
                        ></input>
                        {fieldErrors.PhoneNumber && <p style={{ color: 'red', marginTop: '5px', fontSize: '12px', padding: '0 8px' }}>{fieldErrors.PhoneNumber}</p>}
                      </li>
                      <li data-aos="fade-up" data-aos-duration="1000" className={`${contactF.form_li}`}>
                        <input
                          className={`${contactF.input_fld} ${contactF.font_14}`}
                          type="text"
                          placeholder={locale == "ar" ? "الشركة" : "Company"}
                          value={formData.company}
                          onChange={handleChange}
                          name="company"
                        ></input>
                        {fieldErrors.company && <p style={{ color: 'red', marginTop: '5px', fontSize: '12px', padding: '0 8px' }}>{fieldErrors.company}</p>}
                      </li>

                      <li data-aos="fade-up" data-aos-duration="1000" className={`${contactF.form_li} ${comon.w_100}`}>
                        <textarea
                          className={`${contactF.input_fld} ${contactF.font_14} ${contactF.textarea}`}
                          placeholder={locale == "ar" ? "الرسالة" : "Message"}
                          value={formData.Message}
                          onChange={handleChange}
                          name="Message"
                        ></textarea>
                        {fieldErrors.Message && <p style={{ color: 'red', marginTop: '5px', fontSize: '12px', padding: '0 8px' }}>{fieldErrors.Message}</p>}
                      </li>


                    </ul>
                  </div>

                </div>
              </>
            )}

            <div
              className={`${comon.w_100} ${comon.d_flex_wrap} ${comon.flex_center}`}
            // style={{position:'relative'}}
            >
              {/* Back Button (only show if step > 1) */}
              {step > 1 && (
                <a
                  onClick={prevStep}
                  className={`${comon.link} ${comon.font_17} ${darkMode == true ? comon.dark_theme : ""
                    }`}
                  style={{ cursor: 'pointer' }}
                >
                  {locale == "ar" ? 'الخلف' : 'Back'}
                </a>
              )}

              {/* Next / Preview Button */}
              {step == 1 ? (
                <a
                  onClick={isAllGroupsSelected ? nextStep : (e) => e.preventDefault()}
                  className={`${comon.buttion} ${darkMode == true ? comon.dark_theme : ""
                    } ${comon.but_h_02} ${comon.ml_auto} ${comon.but_fill} ${comon.but_min_w
                    } ${comon.but_min_w2} ${!isAllGroupsSelected ? "disabled" : ""}`}
                  style={{
                    cursor: isAllGroupsSelected ? 'pointer' : 'not-allowed',
                    pointerEvents: isAllGroupsSelected ? 'auto' : 'none',
                    opacity: isAllGroupsSelected ? 1 : 0.6,
                  }}
                >
                  {locale === "ar" ? "التالي" : "Next"}
                </a>

              ) : (
                <>
                  {step == 2 ? (
                    <a
                      onClick={nextStep}
                      className={`${comon.buttion}  ${comon.buttion3} ${darkMode == true ? comon.dark_theme : ""
                        } ${comon.but_h_02} ${comon.ml_auto} ${rtl.ml_auto} ${comon.but_fill} ${comon.but_min_w
                        } ${comon.but_min_w2}`}
                      style={{ cursor: 'pointer' }}
                    >
                      {locale == "ar" ? 'مشاهدة الحملة' : 'Preview Campaign'}
                    </a>
                  ) : (
                    <>
                      <button type="submit"
                        className={`${comon.buttion} ${comon.buttion4} ${comon.but_h_02
                          } ${comon.ml_auto} ${comon.but_fill} ${darkMode == true ? comon.dark_theme : ""
                          } ${comon.but_min_w}`}
                        style={{ cursor: 'pointer' }}
                      >
                        {locale === 'ar' ? 'إرسال الطلب' : 'Send Request'}
                      </button>

                    </>
                  )}
                </>
              )}
            </div> {responseMessage && (
              <p style={{
                color: "red", textAlign: "center", marginTop: "10px",

               }}>
                {responseMessage}
              </p>
            )}
            {sresponseMessage && (
              <p
                style={{ color: "green", textAlign: "center", marginTop: "10px" }}
              >
                {sresponseMessage}
              </p>
            )}
          </form>
        </div>
      </section>
    </>
  );
}

export async function getStaticProps({ locale }) {
  //const locale = locale === "ar" ? "ar" : "en";
  //const locale = "en"
  try {
    const pageData = await fetchPageById("start-campaign", locale);
    return {
      props: {
        pageData,
      },
      revalidate: 10,
    };
  } catch (error) {
    console.error('Error fetching data:', error);
    return {
      props: {
        pageData: null,
      },
    };
  }
}
