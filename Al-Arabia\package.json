{"name": "utility", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "sitemap": "node generate-sitemap.js"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fancyapps/ui": "^5.0.36", "@leenguyen/react-flip-clock-countdown": "^1.6.1", "@mui/icons-material": "^6.4.3", "@mui/lab": "^6.0.0-beta.27", "@mui/material": "^6.4.4", "@mui/x-date-pickers": "^7.25.0", "@react-google-maps/api": "^2.20.5", "aos": "^2.3.4", "arabic-persian-reshaper": "^1.0.1", "css-filter-converter": "^1.0.110", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "grapheme-splitter": "^1.0.4", "gsap": "^3.12.5", "hls.js": "^1.6.2", "html-react-parser": "^5.2.3", "i18next": "^23.15.1", "jszip": "^3.10.1", "leaflet": "^1.9.4", "lenis": "^1.1.20", "locomotive-scroll": "^5.0.0-beta.21", "mui": "^0.0.1", "next": "^14.2.24", "puppeteer": "^24.9.0", "react": "^18", "react-countdown": "^2.3.6", "react-countup": "^6.5.3", "react-date-range": "^2.0.1", "react-dom": "^18", "react-intersection-observer": "^9.15.1", "react-masonry-css": "^1.0.16", "react-select": "^5.10.1", "react-tooltip": "^5.28.0", "sass": "^1.79.4", "sitemap": "^8.0.0", "smooth-scrollbar": "^8.8.4", "swiper": "^11.1.14", "xlsx": "^0.18.5", "xml2": "^0.0.10", "xml2js": "^0.6.2"}}