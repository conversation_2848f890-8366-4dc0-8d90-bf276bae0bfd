"use client";

import React, { useRef, useEffect, useState } from "react";
import { useRouter } from "next/navigation"; // or 'next/router' in older versions
import reshape from "arabic-persian-reshaper";
import Link from "next/link";
import Image from "next/image";
import style from "@/styles/banner.module.scss";

const ArabicRoundText = ({ bannerData }) => {
  const textRef = useRef(null);
  const router = useRouter();
  const locale = router.locale || "en"; // fallback
  const isArabic = locale !== "en";

  const rawMessage = bannerData?.banner_text_1?.animation_text || "";
  const reshapedMessage = isArabic ? reshape(rawMessage) : rawMessage;
  const repeatCount = 5;
  const message = reshapedMessage.repeat(repeatCount);

  const [fontSize, setFontSize] = useState(11.7);
  const radius = fontSize <= 12 ? 35.1 : 37;
  const rotationStep = message.length > 0 ? 360 / message.length : 0;

  // Rotate letters for English only
  useEffect(() => {
    if (!isArabic && textRef.current && message.length > 0) {
      textRef.current.innerHTML = message
        .split("")
        .map((char, i) => {
          if (char === " ") {
            return `<span style="transform: rotate(${
              i * rotationStep
            }deg); opacity: 0;">&nbsp;</span>`;
          }
          return `<span style="transform: rotate(${
            i * rotationStep
          }deg); display: inline-block;">${char}</span>`;
        })
        .join("");
    }
  }, [isArabic, message, rotationStep]);

  const [isSafari, setIsSafari] = useState(false);

  useEffect(() => {
    const ua = navigator.userAgent;
    const isSafariCheck =
      ua.includes("Safari") &&
      !ua.includes("Chrome") &&
      !ua.includes("Chromium") &&
      !ua.includes("Edg") &&
      !ua.includes("OPR");
    setIsSafari(isSafariCheck);
  }, []);

  return (
    <Link
      href={bannerData.banner_text_1.banner_logo_link}
      className={`${style.banner_logo_box} ${style.banner_logo_text_box}`}
    >
      <Image
        src={bannerData.banner_text_1.banner_logo_1}
        width={82}
        height={107}
        alt="Footer Logo"
        style={{
          height: "auto",
          maxWidth: "100%",
          display: "block",
        }}
        quality={100}
      />

      <div className={style.text_sec}>
        {isSafari ? (
          <div className={style.image_text_safari}>
            <Image
              src={"../images/text_banner.svg"}
              width={200}
              height={200}
              alt="Footer Logo"
              quality={100}
            />
          </div>
        ) : (
          <svg
            viewBox="0 0 100 100"
            width="100"
            height="100"
            style={{ fill: "white" }}
          >
            <defs>
              <path
                id="circlePath"
                d={`
                  M 50, 50
                  m -${radius}, 0
                  a ${radius},${radius} 0 1,1 ${radius * 2},0
                  a ${radius},${radius} 0 1,1 -${radius * 2},0
                `}
              />
            </defs>
            <text textAnchor="middle">
              <textPath
                href="#circlePath"
                startOffset="50%"
                style={{ direction: "rtl" }}
              >
                {message}
              </textPath>
            </text>
          </svg>
        )}
      </div>
    </Link>
  );
};

export default ArabicRoundText;
