.projectslider_new {
    /* height: 80vh !important;
    background-color: rebeccapurple; */
    width: 100% !important;
}

.projectslider_new .swiper-wrapper {
    justify-content: space-between !important;
}

.slidernew {
    position: relative;
}

.project_slide_new_img {
    transition: all 0.3s ease !important;
}

.project_slide_new_img img {
    height: auto;
    width: 100%;
    object-fit: contain;
    display: block;
}

.projectslider_new .swiper-slide-next,
.projectslider_new .swiper-slide-prev,
.projectslider_new .swiper-slide-active {
    transition: all 0.3s ease !important;
}

.projectslider_new .swiper-slide {
    transform: scale(0.6) translateZ(0) !important;
    /* transition: all 0.3s ease !important; */
    /* margin-top: 350px !important; */
    margin-top: 300px !important;

    border: 1px solid #a097c8;
    /* background-color: #42376f !important; */
    background-color: #42376f !important;
    aspect-ratio: 1/1;
    display: flex;
    align-items: center;
}



.projectslider_new.maalem_swiper .swiper-slide .slider_anim_new_img {
    display: flex;
    justify-content: center;
    align-items: center;
}

.projectslider_new.maalem_swiper .swiper-slide-active img {
    height: 100%;
    width: 100%;
}

.projectslider_new.maalem_swiper .swiper-slide {
    background-color: #42376f00 !important;
    border: 1px solid rgba(160, 151, 200, 0.493);
    /* transform: scale(0.76) !important; */
    transform: scale(0.725) !important;
}

.projectslider_new.maalem_swiper .swiper-slide-active {
    border: 0 !important;
    transform: scale(2.07) translateZ(0) !important;
}

.projectslider_new.maalem_swiper .swiper-slide-prev {
    transform: scale(0.895) translateZ(0) translateX(-65px) !important;
}

.projectslider_new.maalem_swiper .swiper-slide-next {
    transform: scale(0.895) translateZ(0) translateX(65px) !important;
}










.projectslider_new.maleem_border .swiper-slide {
    border: 1px solid #ffffff;
    background-color: #42376f00 !important;
    margin-right: 0 !important;
    margin-left: 0 !important;
}

.projectslider_new .swiper-slide {
    /* transform: scale(0.6) translateZ(0) translateX(-20px) !important; */
    transform: scale(0.6) translateZ(0) !important;
}

.projectslider_new .swiper-slide-next,
.projectslider_new .swiper-slide-prev {
    transform: scale(0.6) translateZ(0) !important;
    /* margin-top: 250px !important; */
    margin-top: 185px !important;
}

.projectslider_new.maleem_border .swiper-slide {
    border: 1px solid rgba(160, 151, 200, 0.4) !important;
}

.projectslider_new.maleem_border .swiper-slide-active {
    border: 0px solid rgba(160, 151, 200, 0.4) !important;
}

.projectslider_new .swiper-slide-prev {
    transform: scale(0.83) translateZ(0) translateX(-80px) !important;
}

.projectslider_new .swiper-slide-next {
    transform: scale(0.83) translateZ(0) translateX(80px) !important;
}

.projectslider_new.maleem_border .swiper-slide-next,
.projectslider_new.maleem_border .swiper-slide-prev {
    border: 1px solid #ffffffb4;
}

.projectslider_new.maleem_border .swiper-slide-active,
.projectslider_new .swiper-slide-active {
    margin-top: 100px !important;

    transform: scale(1.5) translateZ(0) !important;
    border: 1px solid rgba(255, 255, 255, 0);
    background-color: #ffffff00 !important;
}

.projectslider_new.maleem_border .swiper-slide-active {
    transform: scale(1.94) translateZ(0) !important;
}

.projectslider_new.maleem_border .swiper-slide .project_slide_new_img,
.projectslider_new.maleem_border .swiper-slide .project_slide_new_img {
    transform: scale(0.8) !important;
}

.projectslider_new.maleem_border .swiper-slide-active .project_slide_new_img {
    transform: scale(1) !important;
}

.projectslider_new .swiper-slide-next .project_slide_new_img,
.projectslider_new .swiper-slide-prev .project_slide_new_img {
    transform: scale(0.8) !important;
}

.project_slider_new_btn {
    z-index: 1;
    text-align: center;
    position: absolute;
    bottom: 130px;
    left: 50%;
    transform: translateX(-50%);
}

.project_slider_new_btn.maleem_btn {
    display: flex;
    gap: 30px;
    width: fit-content;
    /* bottom: 100px; */
    bottom: 40px;
}

.slider_heading {
    text-align: center;
    margin: auto;
    color: white;
    font-size: 25px;

    position: absolute;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
}

.slider_heading.dubai_page {
    font-size: 30px;
    /* bottom: 12%; */
    bottom: 8%;
}

@media screen and (max-width: 1366px) {
    .slider_heading.dubai_page {
        font-size: 25px;
        /* bottom: 12%; */
        bottom: 8%;
    }

    .projectslider_new .swiper-slide-prev {
        transform: scale(0.83) translateZ(0) translateX(-60px) !important;
    }

    .projectslider_new .swiper-slide-next {
        transform: scale(0.83) translateZ(0) translateX(60px) !important;
    }



}

@media screen and (max-width: 1024px) {
    .project_slider_new_btn.maleem_btn {

        /* bottom: 85px; */
        bottom: 40px;
    }

    .projectslider_new .swiper-slide {
        margin-top: 250px !important;
    }

    .projectslider_new .swiper-slide-next,
    .projectslider_new .swiper-slide-prev {
        margin-top: 150px !important;
    }

    .projectslider_new .swiper-slide-active {
        margin-top: 50px !important;
    }

    .project_slider_new_btn {
        bottom: 100px;
    }

    .slider_heading {
        font-size: 20px;
        bottom: 50px;
    }

    .projectslider_new.maalem_swiper .swiper-slide {
        margin-top: 250px !important;
    }

    .projectslider_new.maalem_swiper .swiper-slide-prev,
    .projectslider_new.maalem_swiper .swiper-slide-next {
        margin-top: 150px !important;
    }

    .projectslider_new.maalem_swiper .swiper-slide-active {
        margin-top: 80px !important;
    }



}

@media screen and (max-width: 768px) {


    .project_slider_new_btn.maleem_btn {
        gap: 20px;
    }

    .projectslider_new .swiper-slide {
        justify-content: center;
    }

    .projectslider_new.maleem_border .swiper-slide {
        border: 0px solid rgba(160, 151, 200, 0.4) !important;
    }

    .projectslider_new .swiper-slide {
        margin-top: 150px !important;
    }

    .projectslider_new .swiper-slide-next,
    .projectslider_new .swiper-slide-prev {
        margin-top: 100px !important;
    }

    .projectslider_new .swiper-slide-active {
        margin-top: 50px !important;
    }

    /* ---------------------- */

    .projectslider_new.maleem_border .swiper-slide {
        margin-top: 0px !important;
        border: 0;
        transform: unset !important;
    }

    .projectslider_new.maleem_border .swiper-slide-next,
    .projectslider_new.maleem_border .swiper-slide-prev {
        margin-top: 0px !important;
    }

    .projectslider_new.maleem_border .swiper-slide-active {
        margin-top: 0px !important;
    }

    .project_slider_new_btn.project_slider_btn.maleem_btn {
        position: unset !important;
        margin: auto;
        transform: unset;
    }
}

.slider_secton_new.new_slider .swiper-slide {
    width: 15%;
}

.slider_secton_new.new_slider .swiper-slide-active {
    width: 35%;
}

@media screen and (max-width:820px) {
    .projectslider_new.maalem_swiper .swiper-slide {
        transform: scale(1) !important;
        margin-top: 0 !important;
        border: 0 !important;
    }

    .project_slider_new_btn {
        bottom: 0px !important;
    }
}




.rtl .projectslider_new.maalem_swiper .swiper-slide-next {
    transform: translateX(-65px) !important;
}

.rtl .projectslider_new.maalem_swiper .swiper-slide-prev {
    transform: translateX(65px) !important;
}