@import "variable", "base", "mixin";

.test_height {
  height: 1000px;
  font-family: var(--helveticaneuelt_arabic_55);
}

.h1 {
  font-size: 4.688rem;
  font-weight: normal;
  font-family: var(--helveticaneueltarabicBold);
  line-height: 100%;

  span {
    background: linear-gradient(-90deg,
        rgba(227, 181, 255, 1) 0%,
        rgba(94, 69, 255, 1) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: left;
    display: inline-block;
  }
}

.text_capitalize {
  text-transform: capitalize;
}

.font_15 {
  P {
    line-height: 140%;
    font-size: 15px !important;

    @media #{$media-600} {
      font-size: 13px !important;
    }
  }
}

.h1 {
  h3 {
    @include rem(40);
    color: #3b3064;
    line-height: 3rem;
  }
}

.title_02 {
  h3 {
    // font-family: var(--aller_lt);
    font-family: "Aller", sans-serif;

    @include rem(22);
    color: #fff;
    font-weight: 400;
    color: #3b3064;

    @media #{$media-768} {
      @include rem(28);
    }
  }
}

.title_20 {
  h3 {
    // font-family: var(--aller_rg);
    font-family: "Aller", sans-serif;

    @include rem(22);
    color: #3b3064;
    font-weight: 600;
  }
}

.head_20 {
  h3 {
    // font-family: var(--aller_rg);
    font-family: "Aller", sans-serif;

    @include rem(20);
    color: #3b3064;
    font-weight: 600;
  }
}

.custom_but {
  position: absolute;
  top: 50%;
  margin-top: -7px;
  z-index: 900;
  cursor: pointer;
  height: auto;
  width: 15px;

  .img {
    height: auto;
    width: 100%;
    object-fit: contain;
  }

  &.custom_but_next {
    // right: -5%;
    left: calc(100% + 3%);

    @media #{$media-1440} {
      left: calc(100% + 1%);
    }

    @media #{$media-1024} {
      // right: 5px;
      left: calc(100% + 5px);
    }
  }

  &.custom_but_prev {
    // left: -5%;
    right: calc(100% + 3%);

    @media #{$media-1440} {
      right: calc(100% + 1%);
    }

    @media #{$media-1024} {
      // left: 5px;
      right: calc(100% + 5px);
    }
  }

  &:hover {
    opacity: 0.8;
  }
}

.project_wrap {
  @media #{$media-1024} {
    padding: 0 40px;
  }
}

.gap_10 {
  gap: 10px;
}

.overflow_hide {
  overflow: hidden;
}

.more_than_sec {
  text-transform: none;

  @media #{$media-820} {
    flex-direction: column;
    margin-bottom: 20px;
    gap: 20px;
  }

  @media #{$media-700} {
    gap: 0;
    margin-bottom: 10px;
  }
}

.more_than_counter {
  @media #{$media-820} {
    margin: 0 !important;
  }
}

.buttion {
  display: inline-flex;
  padding: 0 25px;
  border: solid 1px #ccc;
  color: #fff;
  font-size: 16px;
  cursor: pointer;
  height: 42px;
  align-items: center;
  justify-content: center;
  font-family: "Aller", sans-serif;

  &:hover {
    background: rgba(88, 72, 170, 0.9);
  }

  &.but_blue {
    border: solid 1px #6758b6;
    color: #6758b6;

    &:hover {
      color: #fff;
    }
  }

  &.but_white {
    background: #fff;
    color: #6758b6;
    border: 1px solid #fff;

    &:hover {
      color: #6758b6;
      background: #e8e1ff;
      border: 1px solid #e8e1ff;
    }
  }

  &.dark_bg {
    background: #000000 !important;
    color: #ffffff !important;
    border: 1px solid #000000;

    &:hover {
      color: #ffffff !important;
      border: 1px solid #000000;
      background: #353535 !important;
    }
  }

  &.dark_theme {
    background: #fff !important;
    color: #000000 !important;
    border: 1px solid #fff !important;

    &:hover {
      background: #dddddd !important;
    }
  }

  &.but_black {
    background: #000000;
    color: #fff;
    border: 1px solid #000;

    &:hover {
      color: #fff;
      background: #6758b6;
      border: 1px solid #6758b6;
    }
  }

  &.but_fill {
    background: #6758b6;
    color: #fff;
    border: none;

    &:hover {
      color: #fff;
      background: #8d7bed;
    }

    &.whiteBtn {
      background-color: #fff;
      color: #000;

      &:hover {
        color: #6758b6;
        background: #e8e1ff;
      }
    }

    @media #{$media-700} {
      height: 40px !important;
    }

    &.but_h_02 {
      height: 52px;
    }
  }

  &.buttion2 {
    padding: 0 25px;
  }

  &.buttion3 {
    padding: 0 2.53%;
  }

  &.buttion4 {
    padding: 0 3.7%;
  }

  @media #{$media-820} {
    font-size: 13px;
  }

  &.but_h_02 {
    height: 52px;

    @media #{$media-700} {
      height: 40px;
    }
  }

  @media #{$media-700} {
    font-size: 13px;
    // padding: 0 17px;
    // height: 35px;
  }
}

.custom_btn {
  @media #{$media-600} {
    height: 40px !important;
  }
}

.d_none {
  display: none;
}

.paralax_test {
  width: 50%;
}

.test_paralax {
  display: flex;
  flex-wrap: wrap;

  li {
    width: 25%;
    list-style: none;
  }
}

.link {
  color: #6758b6;
  text-decoration: underline;

  &.link_white {
    color: #fff;
    font-size: 15px;
    font-weight: 200;

    &:hover {
      color: #dbd3f8 !important;
    }
  }

  &.dark_theme {
    color: #ffffff;
  }
}

.title_30 {
  h3 {
    @include rem(30);
    color: #3b3064;
    font-family: "Aller", sans-serif;

    @media #{$media-820} {
      @include rem(30);
    }

    @media #{$media-768} {
      @include rem(30);
    }

    @media #{$media-500} {
      @include rem(25);
    }
  }
}

.title_35 {
  h3 {
    @include rem(35);
    color: #3b3064;

    @media #{$media-820} {
      @include rem(30);
    }
  }
}

.link {
  color: #6758b6;
  cursor: pointer;
  text-decoration: underline;

  @media #{$media-700} {
    font-size: 14px;
  }
}

.white_color h3,
.white_color p {
  color: #fff;
}

.dark_colour h3,
.dark_colour p {
  color: #000000;
}

.select {
  width: 100%;
  border: none;
  border-bottom: solid 1px #3b3064;
  background: transparent;
  height: 52px;
  font-size: 16px;

  &.dark_color {
    color: #fff;
  }

  &.white {
    border-bottom: solid 1px #fff;
    color: #fff;

    option {
      color: #000;
    }
  }

  &:focus-visible {
    outline: none;
  }

  @media #{$media-820} {
    font-size: 13px;
    height: 40px;
  }
}

.select {
  width: 100%;
  border: none;
  // border-bottom: solid 1px #3b3064;
  background: transparent;
  height: 52px;
  font-size: 16px;
  color: #3b3064;
  font-family: var(--aller_rg) !important;

  &:focus-visible {
    outline: none;
  }

  @media #{$media-820} {
    font-size: 13px;
    height: 40px;
  }
}

.selection_icn {
  position: absolute;
  right: 4px;
  height: 49px;
  background: #3b3064;
  width: 15px;
  z-index: 5;
  pointer-events: none;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;

  &.dark_theme {
    background: rgb(0, 0, 0) !important;

    >img {
      filter: invert(1) brightness(10);
    }
  }

  @media #{$media-820} {
    height: 38px;
  }
}

.image_hover {
  overflow: hidden;

  .img {
    transform: scale(1);
    transition: all 0.5s ease-in-out;
  }

  &:hover {
    .img {
      transform: scale(1.1);
    }
  }
}

.video_block {
  display: block;
}

.text_collor {
  p {
    color: #3b3064;
    font-size: 15px;
    // max-width: 70%;
    line-height: 24px;

    @media #{$media-700} {
      max-width: 100%;
      font-size: 13px;
      line-height: 17px;
    }
  }
}

.text_collor {
  p {
    color: #3b3064;
  }
}

.video_section_02 {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;

  // @media #{$media-700} {
  //   height: 100px;
  // }

  .video_thumbnail {
    position: absolute;
    width: 100%;
    height: 100%;
    transition: all 0.3s ease;

    >img {
      height: 100%;
      width: 100%;
      display: block;
      object-fit: cover;
    }

    &.hide {
      opacity: 0;
      visibility: hidden;
    }
  }
}

.title_block_main {
  width: calc(100% - 253px);

  &.title_block_sub {
    width: 50%;

    @media #{$media-700} {
      width: 100%;
    }
  }

  p {
    font-size: 14px;
  }

  @media #{$media-700} {
    width: 100%;
    margin-bottom: 15px;
  }
}

.no_banner {
  padding-top: 185px;

  @media #{$media-700} {
    padding-top: 110px;
  }
}

.but_min_w {
  min-width: 120px;

  &.but_min_w2 {
    min-width: 130px;
  }
}

.black_bg {
  background-image: url("/images/dark_theme.svg");
  // background-image: url(../public/images/black-bg.png);

  background-color: #000;
  background-size: cover;
  background-repeat: no-repeat;
}

.sitemap {
  li {
    margin-bottom: 10px;
    list-style: circle;
  }
}

.boulevard_video_sec {
  max-height: 650px;
  overflow: hidden;
  width: 100%;
  pointer-events: all;

  video {
    height: 100%;
    width: 100%;
    display: block;
    cursor: pointer !important;
    object-fit: contain !important;
  }

  &.inactive {
    pointer-events: none;
  }
}

.video_play_but {
  width: 108px;
  height: 108px;
  cursor: pointer;
  position: absolute;
  background: #6758b6;
  border-radius: 100%;
  border: solid 2px #fff;
  display: flex;
  flex-wrap: wrap;
  z-index: 50;
  justify-content: center;
  align-items: center;
  transition: all 0.5s ease;

  &:hover {
    background: #7a66db;
  }

  @media #{$media-1280} {
    width: 80px;
    height: 80px;
  }

  @media #{$media-700} {
    width: 70px;
    height: 70px;
  }

  &.play {
    opacity: 0;
  }
}

.text_block_heading {
  text-align: center;

  span {
    color: #3b3064;
    // font-size: 30px;
    @include rem(30);
    font-weight: 400;
    font-family: var(--aller_lt);
    margin-bottom: 15px;
    display: block;

    @media #{$media-1366} {
      margin-bottom: 10px;
    }

    @media #{$media-768} {
      margin-bottom: 7px;
    }

    @media #{$media-600} {
      @include rem(27);
    }

    @media #{$media-500} {
      @include rem(23);
    }
  }

  h4 {
    color: #3b3064;
    @include rem(30);

    // font-size: 30px;
    font-weight: 700;
    margin-bottom: 10px;

    @media #{$media-768} {
      margin-bottom: 7px;
    }

    @media #{$media-600} {
      @include rem(27);
    }

    @media #{$media-500} {
      @include rem(25);
    }
  }

  p {
    color: #3b3064;
    font-family: var(--aller_lt);
    @include rem(30);

    // font-size: 30px;
    font-weight: 400;
    margin-bottom: 10px;

    @media #{$media-768} {
      margin-bottom: 7px;
    }

    @media #{$media-600} {
      @include rem(27);
    }

    @media #{$media-500} {
      @include rem(23);
    }
  }
}

.max_w_detail {
  max-width: 70%;
  margin-left: auto;
  margin-right: auto;

  @media #{$media-700} {
    max-width: 100%;
    margin-top: 20px;
  }

  p {
    @media #{$media-700} {
      font-size: 13px;
      line-height: 22px;
    }
  }
}

.detail_ul {
  margin-left: 0;
  margin-right: 0;
  padding: 0;
  display: flex;
  flex-wrap: wrap;

  .detail_ul_icn {
    width: 29px;
    margin-left: 15px;
    margin-right: 15px;
    margin-top: 5px;
    display: block;

    img {
      width: 100%;
    }
  }

  li {
    display: flex;
    align-items: flex-start;
    margin: 0 3%;

    h3 {
      color: #3b3064;

      span {
        display: block;
        font-weight: 300;
        display: block;
        margin-top: 5px;
      }
    }

    @media #{$media-700} {
      width: 100%;
      justify-content: center;
      margin-top: 10px;
      margin-bottom: 15px;
    }
  }
}

.slider_section_outer {
  .pt_80 {
    padding-top: 0;
  }
}

// -------more news section-------

.news_date_block {
  background: #ffffff;
  padding: 4.5px 10.24px;
  color: rgba(59, 48, 100, 0.9);
  font-size: 14px;
  font-weight: 400;
  position: absolute;
  display: block;
  top: 15px;
  left: 15px;

  @media #{$media-1024} {
    padding: 5px 10px;

    top: 10px;
    left: 10px;
  }

  @media #{$media-700} {
    padding: 3px 7px;

    font-size: 12px;
  }
}

.more_news_section {
  width: 100%;
  background: #e1e2e1;

  .more_news_container {
    width: 100%;

    h3 {
      color: #3b3064;
      // font-size: 30px;
      font-size: 1.875rem;
      line-height: 35px;
      font-weight: 700;
      margin-bottom: 33px;

      @media #{$media-1440} {
        margin-bottom: 30px;
      }

      @media #{$media-1024} {
        margin-bottom: 20px;
      }

      @media #{$media-600} {
        margin-bottom: 10px;
      }
    }

    .more_news_swiper_sec {
      width: 100%;
      position: relative;

      .more_news_swiper_card {
        width: 100%;

        .card_img {
          width: 100%;
          height: auto;
          display: block;
          position: relative;

          >img {
            width: 100%;
            height: auto;
            display: block;
            object-fit: cover;
          }
        }

        h4 {
          margin-top: 15px;
          color: #3b3064;
          font-size: 17px;
          // line-height: 24px;
          font-weight: 400;

          &:hover {
            color: #6758b6;
          }

          @media #{$media-1440} {
            font-size: 16px;
          }

          @media #{$media-1024} {
            font-size: 15px;
          }

          @media #{$media-820} {
            margin-top: 10px;
            line-height: unset;

            font-size: 14px;
          }
        }

        @media #{$media-820} {
          height: 250px;
        }

        @media #{$media-400} {
          height: 240px;
        }
      }

      .swiper_arrow {
        width: 10px;
        // width: 7px;
        height: auto;
        position: absolute;
        top: 50%;
        z-index: 2;
        transform: translateY(-50%);
        cursor: pointer;

        >img {
          height: auto;
          width: 100%;
          display: block;
          object-fit: contain;
        }

        &.left {
          // left: -50px;
          left: -4%;

          @media #{$media-1366} {
            left: -2%;
          }

          @media #{$media-600} {
            left: -15px;
          }
        }

        &.right {
          // right: -50px;
          right: -4%;
          transform: scaleX(-1);

          @media #{$media-1366} {
            right: -2%;
          }

          @media #{$media-600} {
            right: -15px;
          }
        }

        // @media #{$media-500} {

        @media #{$media-1024} {
          display: none;
        }
      }
    }

    @media #{$media-600} {
      width: 95%;
    }
  }
}

// --------------Counte up section Starts-------

.count_up_secton {
  background-color: #6758b6;
  position: relative;
  z-index: 1;

  .count_up_field {
    width: 100%;
    gap: 15px;
    display: flex;
    justify-content: space-evenly;

    &.count_up_field_min_height {
      li {
        min-height: 110px;
      }
    }

    li {
      position: relative;
      z-index: 1;
      width: 100%;
      padding: 15px 10px;
      min-height: 141px;
      background-color: #6f5fc3;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      gap: 10px;
      transition: all 0.3s ease;

      >div {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
      }

      &:hover {
        // background-color: #8675db;
        background-color: #7f75b6;
      }

      span {
        color: white;
        // font-size: 26px;
        @include rem(26);
        font-family: "Aller", sans-serif;

        font-weight: 700;
        margin-bottom: 5px;
        display: block;

        &.count_up_span {
          display: flex;
          align-items: center;
          justify-self: center;
          gap: 2px;
        }
      }

      p {
        color: white;
        font-size: 17px;
        font-weight: 400;
        font-family: var(--aller_lt);

        @media #{$media-1440} {
          font-size: 16px;
        }

        @media #{$media-768} {
          font-size: 14px;
        }
      }

      @media #{$media-820} {
        width: calc(50% - 5px);
        gap: 7px;
        padding: 15px 10px;

        &:last-child {
          width: 100%;
        }
      }
    }

    @media #{$media-820} {
      flex-wrap: wrap;
      gap: 10px;
    }


    &.even_number {
      li {
        @media #{$media-820} {
          &:last-child {
            width: calc(50% - 5px);
          }
        }
      }
    }


  }

  &.count_up_new {
    .count_up_field {

      li {

        &:hover {
          background-color: #6f5fc3 ;

        }
      }
    }
  }
}

// --------------Counte up section Ends- ------

.slider_img {
  max-height: 447px;
}

// ----------text block section starts---------

.text_block_section {
  width: 100%;

  .text_block_field {
    width: 100%;
    display: flex;

    li {
      overflow: hidden;
      width: 50%;
      padding: 130px 10%;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
      position: relative;
      gap: 15px;
      transition: all 0.3s ease;

      .backgroun_img {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;
        pointer-events: none;
        transition: all 0.3s ease;

        >img {
          height: 100%;
          width: 100%;
          object-fit: cover;
          display: block;

          background-position: center;
        }
      }

      &:hover {
        width: 53%;
        padding-left: 12%;
        padding-right: 12%;

        .backgroun_img {
          transform: scale(1.05);
        }

        @media #{$media-1440} {
          padding-left: 10%;
          padding-right: 10%;
        }

        @media #{$media-1024} {
          width: 50%;

          padding-left: 5%;
          padding-right: 5%;
        }

        @media #{$media-820} {
          width: 100%;
          padding-left: 8%;
          padding-right: 8%;

          .backgroun_img {
            transform: scale(1);
          }
        }

        @media #{$media-600} {
          padding-left: 5%;
          padding-right: 5%;
        }
      }

      &::after {
        position: absolute;
        content: "";
        height: 100%;
        width: 100%;
        background-color: hsla(253, 35%, 29%, 0.85);
        left: 0;
        top: 0;
        pointer-events: none;
      }

      &:first-child::after {
        background-color: hsla(250, 39%, 53%, 0.85);
      }

      .img_icon {
        height: 65px;
        width: 65px;
        position: relative;
        z-index: 1;

        img {
          height: 100%;
          width: 100%;
          object-fit: contain;
          display: block;
        }
      }

      h4 {
        color: #ffffff;
        // font-size: 30px;
        @include rem(30);

        // line-height: 35px;
        font-weight: 700;
        position: relative;
        z-index: 1;
        max-width: 430px;

        @media #{$media-1440} {
          max-width: 400px;
        }

        @media #{$media-1366} {
          max-width: 370px;
        }

        @media #{$media-1280} {
          max-width: 350px;
        }

        @media #{$media-820} {
          margin-bottom: 10px;
        }

        @media #{$media-600} {
          @include rem(25);
        }
      }

      p {
        max-width: 430px;
        color: #ffffff;
        font-size: 15px;
        line-height: 24px;
        font-weight: 400;
        position: relative;
        z-index: 1;

        @media #{$media-1440} {
          max-width: 400px;
        }

        @media #{$media-1366} {
          max-width: 370px;
        }

        @media #{$media-1280} {
          max-width: 350px;
        }

        @media #{$media-820} {
          font-size: 14px;
          max-width: 100%;
        }
      }

      @media #{$media-1440} {
        padding: 100px 10%;
      }

      @media #{$media-1024} {
        padding: 70px 5%;
      }

      @media #{$media-820} {
        width: 100%;
        padding: 60px 8%;
      }

      @media #{$media-600} {
        padding: 50px 5%;
      }
    }

    &.kkia_page {
      li {
        align-items: center;
        text-align: center;

        h4 {
          max-width: 350px;
        }
      }
    }

    @media #{$media-820} {
      flex-wrap: wrap;
    }
  }
}

// ----------text block section Ends---------

// -----------Filter block starts----------

.csr_fltr_block {
  max-width: 660px;
  margin-left: auto;
  margin-right: auto;

  .csr_fltr_block_ul {
    margin: 0 -2%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;

    &.kkia_page {
      li {
        &:first-child {
          width: calc(450px - 57.5px);

          @media #{$media-600} {
            width: 100%;
          }
        }

        @media #{$media-600} {
          width: 100%;
        }
      }
    }

    li {
      list-style: none;
      width: calc(44% - 57.5px);
      list-style: none;
      margin: 0 2%;
      position: relative;
      height: 52px;

      @media #{$media-700} {
        width: 46%;
        margin-bottom: 10px;
        height: 45px;
      }
    }

    &> :last-child {
      width: 115px;

      a {
        width: 100%;
        justify-content: center;
        height: 100%;

        @media #{$media-700} {
          padding: 0 15px;
        }
      }

      @media #{$media-700} {
        // width: 100%;
        width: unset;
      }
    }
  }
}

// -----------Filter block ends----------

// -----------Airport Page section Starts----------

.airport_img_text_section {
  width: 100%;

  li {
    display: flex;
    flex-wrap: wrap;
    padding: 60px 3%;
    z-index: 1;
    position: relative;
    // &.mx_3 {
    //   margin: 0 3%;
    // }

    .img_section {
      width: 50%;
      // height: auto;
      // height: 100%;
      z-index: 1;
      position: relative;

      >img {
        // height: auto;
        height: 100%;

        width: 100%;
        object-fit: cover;
        display: block;
      }

      @media #{$media-600} {
        width: 100%;
      }
    }

    .text_section {
      width: 50%;
      // padding: 80px 0% 80px 5%;
      padding: 65px 0% 65px 5%;

      h3 {
        color: #3b3064;
        // font-size: 30px;
        font-family: "Aller", sans-serif;

        @include rem(30);
        font-weight: 700;
        margin-bottom: 25px;

        @media #{$media-1366} {
          margin-bottom: 15px;
        }

        @media #{$media-820} {
          margin-bottom: 10px;
        }

        @media #{$media-600} {
          @include rem(27);
        }

        @media #{$media-400} {
          @include rem(25);
        }
      }

      p {
        color: #3b3064;
        font-size: 15px;
        margin-bottom: 25px;
        font-weight: 400;

        @media #{$media-1366} {
          margin-bottom: 15px;
        }

        @media #{$media-820} {
          font-size: 14px;
          margin-bottom: 10px;
        }
      }

      a {
        display: inline-block;
        padding: 16.5px 35px;

        background: #6758b6;
        color: #ffffff;
        font-size: 16px;
        font-weight: 400;
        transition: all 0.3s ease;

        &:hover {
          background: #473a8b;
        }

        @media #{$media-1440} {
          padding: 15px 30px;
        }

        @media #{$media-1366} {
          padding: 10px 20px;
          font-size: 15px;
        }

        @media #{$media-820} {
          font-size: 14px;
        }

        @media #{$media-768} {
          padding: 8px 15px;
        }
      }

      @media #{$media-1440} {
        padding: 40px 0% 40px 5%;
      }

      @media #{$media-1366} {
        padding: 30px 0% 30px 5%;
      }

      @media #{$media-768} {
        padding: 0px 0% 0px 5%;
      }

      @media #{$media-600} {
        width: 100%;
        padding: 20px 0 0 0;
      }
    }

    @media #{$media-1600} {
      padding: 60px 5%;
    }

    @media #{$media-1440} {
      padding: 40px 5%;
    }

    @media #{$media-600} {
      padding: 25px 5%;
    }

    &:nth-child(2n) {
      flex-direction: row-reverse;
      background-color: #000;
      margin: 0 3%;
      // position: unset;
      position: relative;
      z-index: 2;

      .text_section {
        padding-left: 0;
        padding-right: 5%;

        h3 {
          color: #ffffff;
        }

        p {
          color: #ffffff;
        }

        a {
          background: #ffffff;

          color: #000000;

          &:hover {
            background: #dfdfdf;
          }
        }
      }

      @media #{$media-1600} {
        margin: 0 5%;
      }

      @media #{$media-1440} {
        margin: 0 5%;
      }
    }
  }
}

// -----------Airport Page section ends----------

// ---------Breadcrub start--------
.detail_breadcrumb {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 35px;
  white-space: nowrap;
  row-gap: 5px;
  padding-top: 2px;
  overflow: hidden;
  overflow-x: auto;
  padding-bottom: 5px;

  li {
    list-style: none;
    // padding: 0 16px;
    padding: 0 20px;

    .bread_icon {
      height: auto;
      width: 23px;
      margin-right: 10px;

      >img {
        height: auto;
        width: 100%;
        object-fit: contain;
        display: block;
      }

      @media #{$media-1024} {
        width: 20px;
      }

      @media #{$media-700} {
        // width: 17px;
        width: 17px;
      }
    }

    &:first-child {
      padding: 0;
      position: absolute;
      left: 0;
      top: 0;
    }

    &:first-child a {
      position: unset !important;

      &::before {
        position: unset !important;
      }
    }

    a {
      display: block;
      color: #3b3064e6;
      font-size: 14px;
      font-weight: 400;
      font-family: "Aller", sans-serif;

      position: relative;

      &.active {
        font-weight: 700;
        color: rgba(103, 88, 182, 0.9);

        &::before {
          background-image: url("/images/breadcumb_arrow1.svg");
        }
      }

      &:hover {
        color: #9382cee6;
      }

      &::before {
        position: absolute;
        content: "";
        height: 12px;
        width: 7px;
        background-image: url("/images/breadcumb_arrow.svg");
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        left: -20px;
        top: 50%;
        transform: translateY(-50%);

        @media #{$media-700} {
          left: -16px;
        }
      }

      @media #{$media-700} {
        font-size: 10px;
      }
    }

    @media #{$media-700} {
      padding: 0 10px;
    }
  }

  &.breadcrumb_white {
    li {
      a {
        color: #e4e4e4;

        &.active {
          color: #fff;

          &::before {
            background-image: url("/images/breadcumb_arrow_white1.svg");
          }
        }

        &:hover {
          color: #9382cee6;
        }

        &::before {
          background-image: url("/images/breadcumb_arrow_white.svg");
        }
      }
    }
  }

  @media #{$media-1024} {
    padding-top: 0px;
  }

  @media #{$media-700} {
    padding-left: 30px;
    padding-top: 2px;
  }

  @media #{$media-600} {
    margin-bottom: 25px;
  }
}

.bread_crumb_mian {
  width: 100%;

  .bread_crumb_mian_ul {
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    position: relative;
    padding-left: 30px;
    flex-wrap: wrap;
    row-gap: 5px;

    li {
      width: auto;
      list-style: none;
      padding-left: 20px;
      padding-right: 20px;
      position: relative;
      display: flex;
      flex-wrap: wrap;
      align-items: center;

      &::after {
        content: "";
        width: 20px;
        height: 20px;
        background: url(../public/images/brud_arrow.svg) no-repeat center center;
        display: block;
        position: absolute;
        right: -13px;

        @media #{$media-700} {
          width: 8px;
          height: 8px;
          background-size: contain;
          right: -9px;
        }
      }

      .bread_icon {
        height: auto;
        width: 20px;
        margin-right: 10px;

        >img {
          height: auto;
          width: 100%;
          object-fit: contain;
          display: block;
        }

        @media #{$media-700} {
          width: 17px;
        }
      }

      span {
        font-weight: 500;
        font-family: var(--aller_bd);
        color: #6758b6;
        font-size: 14px;

        @media #{$media-700} {
          font-size: 10px;
        }
      }

      a {
        color: #3b3064;
        font-size: 14px;

        @media #{$media-700} {
          font-size: 11px;
        }
      }

      @media #{$media-600} {
        padding-left: 10px;
        padding-right: 10px;
      }

      &:first-child {
        padding: 0;
        position: absolute;
        left: 0;
        top: 0;

        a {
          position: unset !important;

          &::after {
            position: unset !important;
          }
        }
      }
    }

    &> :last-child {
      &::after {
        display: none;
      }
    }
  }

  @media #{$media-600} {
    padding-top: 10px;
    padding-bottom: 10px;
  }
}

:global(body.rtl) .bread_crumb_mian .bread_crumb_mian_ul li::after {
  left: -10px;
  right: inherit;
  transform: scale(-1);
}

:global(body.rtl) .detail_breadcrumb li:first-child {
  position: absolute;
  left: inherit;
  right: 0;
}

:global(body.rtl) .detail_breadcrumb {
  padding-right: 50px;
}

:global(body.rtl) .detail_breadcrumb li a::before {
  right: -25px;
  transform: scale(-1);
  top: 5px;

  @media #{$media-700} {
    right: -16px;
    top: 2px;
  }
}

:global(body.rtl) .bread_crumb_mian .bread_crumb_mian_ul li:first-child:after {
  left: inherit;
  right: 40px;
  transform: scale(-1);
}

.center_text {
  width: 100%;
  max-width: 655px;
  margin: 0 auto;
  text-align: center;
}

.airport_gallery_main {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin: 5px 0;

  ul {
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    width: 100%;

    li {
      overflow: hidden !important;

      .image_hover {
        display: block;
        width: 100%;
        height: 100%;
      }
    }

    &.gallery01 {
      width: 100%;

      li {
        width: calc(41% - 4.4px);
        height: 425px;

        @media #{$media-1200} {
          height: 300px;
        }

        @media #{$media-768} {
          height: 200px;
        }

        @media #{$media-500} {
          height: 120px;
        }
      }

      li:nth-child(2) {
        width: calc(18.2% - 5px);
      }
    }

    &.gallery02 {
      width: calc(20.2% - 5px);

      li {
        width: 100%;
        height: 735px;

        @media #{$media-1200} {
          height: 100%;
        }
      }
    }

    &.gallery03 {
      width: calc(80% - 4px);

      li {
        height: 365px;
        width: calc(78% - 4.6px);

        &:nth-child(2) {
          width: calc(22.3% - 5px);
        }

        &:nth-child(3n) {
          width: 100%;
        }

        @media #{$media-1200} {
          height: 280px;
        }

        @media #{$media-768} {
          height: 200px;
        }

        @media #{$media-500} {
          height: 120px;
        }
      }
    }

    &.gallery04 {
      width: 100%;

      li {
        height: 365px;
        width: calc(22.2% - 5px);

        &:nth-child(1) {
          width: calc(55.8% - 5px);
        }

        @media #{$media-1200} {
          height: 280px;
        }

        @media #{$media-768} {
          height: 200px;
        }

        @media #{$media-500} {
          height: 120px;
        }
      }
    }
  }

  &.masonry_sec {
    display: block;
  }
}

.grey_gradient {
  background: linear-gradient(180deg, #8b8b8b 0%, #282828 100%);
}

.linear_bg {
  background: linear-gradient(107.56deg, #675aa7 0%, #3a3064 57%);
}

.width_870 {
  max-width: 870px !important;
}

// --------dark theme--------

.dark_theme {
  background-image: url("/images/dark_theme.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  background-color: #000;

  h3 {
    color: white;
  }
}

.hover_visible_sec {
  .hover_visible {
    height: 100%;
    width: 100%;
    position: absolute;
    transition: all 0.5s ease;
    top: 0%;
    left: 0;
    background-color: hwb(250 35% 29% / 0.9);
    opacity: 0;

    .hover_visible_container {
      width: 100%;
      overflow-y: auto;
      scrollbar-width: thin;
      height: 100%;
      transform: translateY(50px);
      transition: all 0.5s ease;
      padding: 80px 9% 25px 9%;

      @media #{$media-1366} {
        padding: 40px 9%;
      }

      @media #{$media-820} {
        padding: 25px 9%;
      }

      &.mob_hover_container {
        @media #{$media-500} {
          padding: 20px 7%;

          h4 {
            @include rem(23);
            margin-bottom: 5px;
          }

          button,
          .hover_popup_btn {
            padding: 7px 10px;
          }
        }
      }

      h4 {
        // font-size: 25px;

        @include rem(25);
        font-weight: 700;
        margin-bottom: 20px;
        color: white;
        position: relative;
        z-index: 1;

        @media #{$media-1280} {
          margin-bottom: 10px;
        }
      }

      p {
        color: white;
        line-height: 140%;
        font-size: 15px;
        font-weight: 400;
        margin-bottom: 20px;
        position: relative;
        z-index: 1;

        @media #{$media-1280} {
          margin-bottom: 10px;
          font-size: 14px;
        }

        @media #{$media-500} {
          font-size: 13px;
        }
      }

      button,
      .hover_popup_btn {
        display: inline-block;
        padding: 10px 15px;
        background-color: white;
        color: #6758b6;
        position: relative;
        z-index: 1;
        font-family: var(--aller_rg);

        cursor: pointer;
        font-size: 16px;
        border: 0;
        outline: none;

        &:focus {
          outline: none;
        }

        @media #{$media-1280} {
          font-size: 14px;

          padding: 7px 10px;
        }

        @media #{$media-820} {
          font-size: 13px;
        }
      }
    }
  }

  &:hover {
    .hover_visible {
      top: 0%;
      opacity: 1;

      .hover_visible_container {
        transform: translateY(0px);
      }
    }
  }
}

.popup_container {
  position: absolute;
  // top: 46%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 1020px;

  // background-color: white;

  &.min_height {
    @media #{$media-1440} {
      top: calc(50% + 15px);
    }

    @media #{$media-1280} {
      max-width: 800px;
      top: calc(50% + 15px);
    }

    .swiper_image {
      max-height: 550px;

      &.swiper_image_contain {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      @media #{$media-1599} {
        max-height: 500px;
      }

      @media #{$media-1280} {
        max-height: 400px;
      }
    }
  }

  @media #{$media-600} {
    width: 90%;
  }

  &.map_pop_up {
    top: 50%;

    max-width: 1315px;

    @media #{$media-1440} {
      top: calc(50% + 15px);
    }
  }

  &:focus {
    outline: none;
  }

  .popup_close {
    position: absolute;
    height: 25px;
    width: 25px;
    background-color: #ececec;
    display: flex;
    justify-content: center;
    align-items: center;
    // left: calc(100% + 5px);
    right: 10px;
    top: 15px;
    z-index: 2;
    border: 0;
    border-radius: 0px;
    cursor: pointer;

    &:hover {
      background-color: #d4d4d4;
    }

    img {
      width: 80%;
      height: auto;
      display: block;
      object-fit: contain;
    }

    // &.small_close {

    // }
    // @media #{$media-600} {
    //   height: 25px;
    //   width: 25px;
    //   left: unset;
    //   right: 0;
    //   top: unset;
    //   bottom: calc(100% + 5px);
    // }
    @media #{$media-600} {
      height: 25px;
      width: 25px;
      top: 10px;
    }
  }

  .swiper_image {
    width: 100%;
    max-height: 600px;
    aspect-ratio: 5/3;

    &.swiper_image_contain {
      img {
        object-position: center;
        width: auto;
        height: 100%;
        display: block;
        object-fit: contain;
      }
    }

    img {
      object-position: top;
      width: 100%;
      height: 100%;
      display: block;
    }

    &.swiper_image_cover {
      img {
        object-fit: cover;
        object-position: center;
      }
    }

    @media #{$media-1024} {
      max-height: 450px;
    }
  }

  .swiper_image_thumbs {
    height: 100px;
    width: 100%;
    cursor: pointer;

    img {
      height: 100%;
      width: 100%;
      object-fit: cover;
      display: block;
    }

    &.border {
      border: 1px solid rgba(253, 253, 253, 0.363);
    }

    @media #{$media-768} {
      height: 70px;
    }

    @media #{$media-500} {
      height: 60px;
    }
  }

  .popup_section {
    margin: 0 !important;
    padding-top: 15px;
    color: #3b3064;

    .head_sec {
      margin-left: auto;
      margin-right: auto;
      max-width: 500px;
      text-align: center;
      margin-bottom: 20px;

      h3 {
        color: #3b3064;
        text-align: center;
        font-family: "aller_bd";
        font-size: 30px;
        line-height: 35px;
        font-weight: 700;
        margin-bottom: 10px;

        @media #{$media-768} {
          font-size: 25px;
        }
      }

      p {
        color: #3b3064;
        text-align: center;
        font-family: "aller_rg";
        font-size: 15px;
        line-height: 24px;
        font-weight: 400;

        @media #{$media-768} {
          font-size: 14px;
        }
      }
    }

    ul {
      margin: 0;
    }

    .pop_up_input_fld {
      color: #3b3064;

      font-family: "aller_lt";

      label {
        font-family: "aller_rg";

        font-size: 14px;
      }
    }

    .popup_btn_sec {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-family: "aller_rg";
      gap: 25px;

      .upload_sec {
        display: flex;
        align-items: center;
        gap: 40px;
        font-size: 14px;

        label {
          padding: 0px 30px;
          height: 52px;
          display: flex;
          align-items: center;
          border: 1px solid #3b3064;
          background-color: white;
          color: #3b3064;
          font-family: "aller_rg";
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 14px;

          &:hover {
            border: 1px solid #ffffff;
            background-color: #3b3064;
            color: #ffffff;
          }

          @media #{$media-700} {
            height: 40px !important;
          }
        }

        @media #{$media-768} {
          gap: 20px;
        }
      }
    }
  }
}

:global(body.rtl) .pop_up_input_fld {
  font-family: var(--arbfonts) !important;
}

:global(body.rtl) .popup_container .popup_close {
  right: unset;
  left: 10px;

  // @media #{$media-600} {
  //   right: unset;
  //   left: 10px;
  // }
}

.project_partners_img {
  height: 100%;
  width: 100%;

  >img {
    height: 100%;
    width: 100%;
    display: block;

    object-fit: contain !important;
  }

  &.project_partners_img_new {
    // transform: scale(0.75);
    transform: scale(0.9);
  }
}

.page_not_found_section {
  background: linear-gradient(107.56deg,
      rgba(103, 90, 167, 1) 0%,
      rgba(58, 48, 100, 1) 100%);
  width: 100%;

  .page_not_found_container {
    height: 100dvh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    h2 {
      @include rem(60);
      color: white;
      margin-bottom: 30px;

      @media #{$media-768} {
        margin-bottom: 10px;
      }

      @media #{$media-500} {
        margin-bottom: 5px;
      }
    }

    p {
      @include rem(30);
      color: white;

      @media #{$media-500} {
        @include rem(25);
      }
    }
  }
}

.pr_10 {
  padding-right: 10px !important;
}

.latest_insight_img_sec {
  max-height: 359px;

  @media #{$media-700} {
    max-height: 220px;
    margin-left: auto;
    margin-right: auto;
  }
}

.insight_img_new_sec {

  @media #{$media-700} {
    height: 400px;

    img {
      height: 100%;
      object-fit: cover;
      width: 100%;
    }
  }
}

.the_gallery_sec {
  h3 {
    @include rem(30);
  }
}

.video_min_height_section {
  max-height: 700px;
  object-fit: cover !important;
  object-position: top;
}

.font_17 {
  font-size: 17px;

  @media #{$media-1280} {
    font-size: 16px;
  }

  @media #{$media-768} {
    font-size: 15px;
  }
}

.airport_px_3 {
  padding-left: 3%;
  padding-right: 3%;

  @media #{$media-1600} {
    padding-left: 5%;
    padding-right: 5%;
  }
}

.around_detail_page_sec {
  p {
    max-width: 770px;
  }
}

.all_around_detail_icon_sec {
  .detail_ul_icn {
    @media #{$media-700} {
      margin-left: 0;
      margin-right: 0;
    }
  }

  li {
    @media #{$media-700} {
      width: unset;

      gap: 10px;
    }
  }
}

.detail_breadcrumb_padding {
  @media #{$media-820} {
    margin-top: 25px;
  }
}

.select_dropdown_section {
  width: 100%;
  height: 100%;
  // background-color: rebeccapurple;

  position: relative;

  >label {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border-bottom: 1px solid rgb(207, 207, 207);
    cursor: pointer;
    color: #3a3064;
  }

  .select_dropdown_list {
    position: absolute;
    top: 101%;
    left: -2%;
    height: auto;
    width: 104%;
    z-index: 800;
    border-radius: 5px;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.521);

    ul {
      height: auto;
      width: 100%;
      padding: 5px 0;

      li {
        height: auto;
        width: 100%;
        display: flex;
        position: unset !important;
        cursor: pointer;
        padding: 10px 3%;
        margin: 0;
        font-size: 15px;
        color: #3a3064;

        &:hover {
          background-color: #c5baff2c;
        }

        &.active {
          background-color: #9f95cf4b;
        }

        &::after {
          display: none;
        }
      }
    }
  }
}

.res_pb_20 {
  @media #{$media-500} {
    padding-bottom: 20px;
  }
}

.black_bg_color {
  background: rgb(0, 0, 0) !important;
}

.tab_sticky {
  position: sticky;
  top: 65px;
  z-index: 3;
  background-color: #ececec;

  @media #{$media-1024} {
    top: 0px;
  }
}

.display_hidden {
  visibility: hidden;
  display: none;
}

.d_block {
  display: block;
}

.image_grid_layout {
  display: grid;
  padding-left: 5px;
  flex-wrap: wrap;
  grid-template-columns: repeat(5, 1fr);
  // grid-template-rows: repeat(4, 1fr);
  grid-auto-rows: 350px;

  // auto-rows: minmax(100px, auto); /* Let rows auto-size */

  gap: 5px;
  width: 100%;

  a,
  .image_sec {
    width: 100%;
    cursor: pointer;
    overflow: hidden;

    >img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: all 0.3s ease;
    }

    &:hover img {
      transform: scale(1.05);
    }
  }

  :global {
    .grid_sec1 {
      grid-column: span 2;
    }

    .grid_sec2 {
      grid-column-start: 3;
    }

    .grid_sec3 {
      grid-column: span 2;
      // grid-column-start: 4;
    }

    .grid_sec4 {
      grid-row: span 2;
      grid-row-start: 2;
      grid-row-end: 4;
    }

    .grid_sec5 {
      grid-column: span 3;
      grid-row-start: 2;
      grid-column-start: 2;
      grid-column-end: 5;
    }

    .grid_sec6 {
      grid-column-start: 5;
      grid-row-start: 2;
    }

    .grid_sec7 {
      grid-column: span 4;
      grid-column-start: 2;
      grid-row-start: 3;
      grid-column-end: 7;
    }

    .grid_sec8 {
      grid-column: span 3;
      grid-row-start: 4;
    }

    .grid_sec9 {
      grid-column-start: 4;
      grid-row-start: 4;
    }

    .grid_sec10 {
      grid-column-start: 5;
      grid-row-start: 4;
    }
  }

  @media #{$media-700} {
    grid-auto-rows: 300px;
  }

  @media #{$media-700} {
    width: 100%;
    display: flex;
    padding-left: 3px;
    padding-right: 3px;
  }
}

.blvd_season_btn {
  color: rgb(0, 0, 0) !important;
}

.zoom_btn_sec {
  position: absolute;
  bottom: 50px;
  right: 20px;

  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: 15px;

  >button {
    border: 0;
    // border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.904);
    height: 40px;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background-color: rgb(255, 255, 255);
    }

    .zoom_img {
      width: 50%;
      height: auto;

      >img {
        height: auto;
        width: 100%;
        display: block;
        object-fit: contain;
      }
    }

    @media screen and (max-width: 820px) {
      height: 35px;
      width: 35px;
    }

    @media screen and (max-width: 700px) {
      height: 30px;
      width: 30px;
    }
  }

  &.main_page {
    @media screen and (max-width: 820px) {
      right: 10px;
      gap: 10px;
      top: 120px;
    }

    @media screen and (max-width: 500px) {
      top: 150px;
    }
  }

  &.black_theme_map_btn {
    @media screen and (max-width: 1024px) {
      right: 10px;
      gap: 10px;
      top: 50px;

      >button {
        @media screen and (max-width: 1024px) {
          height: 25px;
          width: 25px;
        }
      }
    }
  }

  @media screen and (max-width: 820px) {
    top: 20px;
    bottom: unset;
    right: 5%;
    gap: 10px;
  }
}

.swiper_popup_container {
  // background-color: #342a5a8a;
}


:global(body.rtl) {
  .buttion {
    // font-family: var(--arbfontsBase) !important;
    font-family: var(--arbfonts) !important;
  }

  .zoom_btn_sec {
    right: unset !important;
    left: 20px;

    &.main_page {
      @media screen and (max-width: 820px) {
        left: 10px;
      }
    }

    &.black_theme_map_btn {
      @media screen and (max-width: 1024px) {
        left: 10px;
      }
    }

    @media screen and (max-width: 820px) {
      top: 20px;

      left: 5%;

    }


  }
}

.button_border_0 {
  border-radius: 0 !important;
}

.button_grp_new {

  @media (max-width:700px) {
    gap: 10px;
    flex-wrap: nowrap;

    .title_block_main {
      width: unset;
      margin-bottom: 5px;
    }

    .buttion {
      flex-shrink: 0;
      padding: 0 20px;

      @media (max-width:400px) {
        padding: 0 15px;

      }
    }
  }
}

.smaller_btn_new {
  @media (max-width:700px) {


    padding: 0 15px;
    height: 37px;
  }
}

.mob_title_new {
  @media (max-width:700px) {

    margin-bottom: 10px;

    br {
      display: none;
    }

  }

}

.video_section_02 {

  &.mob_video_hide {
    @media (max-width:700px) {
      display: none;
    }
  }
}

.mob_pt_10 {
  @media (max-width:700px) {


    li {
      &:first-child {
        padding-top: 10px;

      }
    }
  }
}

.mob_pb_0 {
  @media (max-width:700px) {
    padding-bottom: 0 !important;
  }
}

.mob_hide {

  @media (max-width:700px) {
    display: none;
  }
}

.mob_pt_15 {

  @media (max-width:700px) {
    padding-top: 15px;
  }

}