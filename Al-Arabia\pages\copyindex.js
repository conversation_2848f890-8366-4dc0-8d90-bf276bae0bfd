import React, { useState, useEffect, useRef } from "react";
import Head from "next/head";
// ---buttion-import--start
import But<PERSON> from "@/component/button/Buttons";

import Marquee from "@/component/Marquee";
// ---buttion-import--end

import Image from "next/image";
import BannerHome from "@/component/BannerHome";
import Parallax from "@/component/Paralax";
import Map from "@/component/contactmap";
// Import Swiper styles
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/scrollbar";
import { Scrollbar, Autoplay, Navigation } from "swiper/modules";
import comon from "@/styles/comon.module.scss";
import rtl from "@/styles/rtl.module.scss";
import home from "@/styles/home.module.scss";
import buttion from "@/styles/buttion.module.scss";
import DynamicImage from "@/component/DynamicImage";

import HeadMarquee from "@/component/HeadMarquee";
import SliderSection from "@/component/SliderSection";
import ProjectPartners from "@/component/ProjectPartners";
import CountUp from "react-countup";
import { useInView } from "react-intersection-observer";
import parse from "html-react-parser";
import { fetchPageById, fetchPostList } from "@/lib/api/pageApi";
import { useRouter } from "next/router";
import Link from "next/link";
import ContactmapHover from "@/component/contactmapHover";


export default function Home({ pageData, NewsList, LocList, LocList1 }) {
	const [activeIndex, setActiveIndex] = useState(0);
	const { locale } = useRouter();
	const { ref, inView } = useInView({ triggerOnce: true, threshold: 0.2 });
	console.log("Is section in view?", inView); // Debugging
	// Array of images corresponding to each `li`
	const images = [
		"/images/insights_img_1.jpg", // Image for the first `li`
		"/images/insights_img_2.jpg", // Image for the second `li`
		"/images/insights_img_3.jpg", // Image for the third `li`
	];
	const router = useRouter();
	// ==========AOS SECTION==========

	// const [isMobile, setIsMobile] = useState(false);
	// const [isTb, setIsTab] = useState(false);

	// useEffect(() => {
	//   AOS.init({
	//     easing: "ease-out-cubic",
	//     once: false,
	//     offset: 50,
	//   });
	//   const handleResize = () => {
	//     setIsMobile(window.innerWidth <= 768);
	//     setIsTab(window.innerWidth <= 1000);
	//   };

	//   handleResize();
	//   window.addEventListener("resize", handleResize);

	//   return () => window.removeEventListener("resize", handleResize);
	// }, []);

	const ProjectPartnersImg = [
		"/images/pro_01.png",
		"/images/pro_02.png",
		"/images/pro_03.png",
		"/images/pro_05.png",
		"/images/pro_06.png",
		"/images/pro_07.png",
		"/images/pro_05.png",
	];
	console.log("inView", inView);

	const [mergedLocations, setMergedLocations] = useState([]);

	const monthNames = {
        en: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
        ar: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
    };

	useEffect(() => {
		const merged = [];
		const combinedLocList = [...LocList, ...LocList1];
		const normalize = str => str?.toLowerCase().replace(/\s+/g, '') || '';
	
		combinedLocList.forEach(item => {
			const postName = item?.title?.rendered || "";
			const postIcon = item?.acf?.location_details?.location_icon || null; // Keep it null if not available
			const addIconLevel = item?.acf?.location_details?.add_icon || "Add Common Icon";
			const locationList = item?.acf?.location_details?.location_list || [];
	
			locationList.forEach(newCountry => {
				const existingCountry = merged.find(
					c => normalize(c.country?.name) === normalize(newCountry.country?.name)
				);
	
				const transformedCities = (newCountry.cities || []).map(city => {
					const transformedCoords = (city.co_ords || []).map(coord => {
						let icon = null; // Default to null (no icon)
	
						if (addIconLevel === "Add City Wise Icon" && city.product_icon) {
							icon = city.product_icon;
						} else if (addIconLevel === "Add Country Wise Icon" && newCountry.country?.product_icon_c) {
							icon = newCountry.country.product_icon_c;
						} else if (addIconLevel === "Add Common Icon") {
							icon = postIcon;
						}
	
						return { ...coord, name: postName, icon };
					});
	
					return { ...city, co_ords: transformedCoords };
				});
	
				if (existingCountry) {
					transformedCities.forEach(newCity => {
						const existingCity = existingCountry.cities.find(
							city => normalize(city.city_name) === normalize(newCity.city_name)
						);
	
						if (existingCity) {
							const updatedCoords = newCity.co_ords.map(coord => {
								let icon = null;
	
								if (addIconLevel === "Add City Wise Icon" && newCity.product_icon) {
									icon = newCity.product_icon;
								} else if (addIconLevel === "Add Country Wise Icon" && newCountry.country?.product_icon_c) {
									icon = newCountry.country.product_icon_c;
								} else if (addIconLevel === "Add Common Icon") {
									icon = postIcon;
								}
								return { ...coord, name: postName, icon };
							});
							existingCity.co_ords = [...(existingCity.co_ords || []), ...updatedCoords];
						} else {
							existingCountry.cities.push(newCity);
						}
					});
				} else {
					merged.push({ country: newCountry.country, cities: transformedCities });
				}
			});
		});
	
		setMergedLocations(merged);
	}, [LocList, LocList1]);


	useEffect(() => {
		if (mergedLocations.length > 0) {
			console.log('Merged Location List:', mergedLocations);
		}
	}, [mergedLocations]);

	const formattedContent = pageData.acf.slider_details_.map((item) => {
		if (!item.acf_fc_layout) return "";
		if (item.acf_fc_layout === "small_bold_text") {
		  return `<b>${item.text_bold}</b>`;
		} else if (item.acf_fc_layout === "regular_big_text") {
		  return item.text__reg;
		}
		return "";
	  });
	return (
		<>
			{/* <Head>
				<title>Al Arabia</title>
				<meta name="description" content="Generated by create next app" />
				<meta name="viewport" content="width=device-width, initial-scale=1" />
				<link rel="icon" href="/favicon-new.ico" />
			</Head> */}
			{/* <HeadMarquee /> */}
			<BannerHome
				isOverHeight={true}
				bannerData={pageData.acf.banner_details}
			/>

			<section
				className={`${comon.pt_50} ${comon.pb_50}`}
				style={{ backgroundColor: "#6758B6", overflow: "hidden" }}
			>
				<div ref={ref} className={`${comon.wrap}`}>
					{" "}
					{/* Moved ref here */}
					<div
						className={`${comon.d_flex_wrap} ${comon.more_than_sec} ${comon.w_100}`}
					>
						<div className={`${home.home_txt_01} ${rtl.home_txt_01}`}>
							<h1>
								{pageData.acf.overview_details.title_overview}
								<br />
								<span>{pageData.acf.overview_details.short_description}</span>
							</h1>
						</div>
						<div className={`${home.home_txt_02}`}>
							<ul
								className={`${home.home_txt_counder} ${rtl.home_txt_counder} ${comon.more_than_counter}`}
							>
								{pageData.acf.overview_details.counter.map((item, index) => {
									return (
										<li>
											<h2>
												<CountUp
													start={0}
													end={item.value}
													duration={3}
													startOnMount={false}
													redraw={true}
													delay={0}
													play={inView}
													style={{ fontFamily: 'Aller' }}
												>
													{({ countUpRef }) => (
														<span ref={countUpRef} className="base_font" />
													)}

												</CountUp>
												{item.postfix}
											</h2>
											<p >{item.text_}</p>
										</li>
									);
								})}
							</ul>
						</div>
					</div>
				</div>
			</section>

			<SliderSection
				// images={pageData.acf.list_details.list}
				images={pageData.acf.list_details.list.map((item) => ({
					img: item.image,
					description: item.title,
					link: item.project_link
				}))}
				title={pageData.acf.list_details.title_list}
				PageLink={true}
			/>

			{/* <Map
				isHideMoreLocation={true} /> */}

			{mergedLocations.length > 0 && pageData && (
				<ContactmapHover mapDetails={pageData.acf.location_details} locationList={mergedLocations} />
			)}

			{
				formattedContent && (
					<Marquee
						data-aos="fade-in"
						data-aos-duration="1000"
						content={formattedContent}
						speed={99999}
						direction="left"
					/>
				)
			}
			
			<ProjectPartners images={pageData.acf.partners_list} />

			{/* <div  className={`${comon.paralax_test}`}>
      <ImageParallax src="/images/c-social-bg.jpg" alt="Sample Image" speed={0.8} height="600px" />

      </div> */}

			{/* <section
				ref={ref}
				className={`${comon.pt_60} ${comon.pb_40}`}
				style={{ backgroundColor: "#6758B6", overflow: "hidden" }}
			>
				<div className={`${comon.wrap}`}>
					<div
						className={`${comon.d_flex_wrap} ${comon.more_than_sec} ${comon.w_100}`}
					>
						<div
							className={`${home.home_txt_01}`}
							data-aos="fade-in"
							data-aos-duration="1000"
						>
							<h1>
								More than 40 years
								<br />
								<span>
									of authenticity, experience,
									<br />
									and excellence.
								</span>
							</h1>
						</div>
						<div className={`${home.home_txt_02}`}>
							<ul
								className={`${home.home_txt_counder} ${rtl.home_txt_counder} ${comon.more_than_counter}`}
							>
								<li
									data-aos="fade-in"
									data-aos-delay="300"
									data-aos-duration="1000"
								>
									<h2>
										{inView && <CountUp start={0} end={200} duration={3} />}+
									</h2>
									<p>Locations across The Kingdom</p>
								</li>

								<li
									data-aos="fade-in"
									data-aos-delay="600"
									data-aos-duration="1000"
								>
									<h2>
										{inView && <CountUp start={0} end={5000} duration={3} />}+
									</h2>
									<p>Campaigns</p>
								</li>

								<li
									data-aos="fade-in"
									data-aos-delay="900"
									data-aos-duration="1000"
								>
									<h2>
										{inView && <CountUp start={0} end={350} duration={3} />}+
									</h2>
									<p>Clients and Brands</p>
								</li>
							</ul>
						</div>
					</div>
				</div>
			</section> */}

			<section
				data-aos="fade-in"
				data-aos-duration="1000"
				className={`${comon.pt_95} ${comon.pb_95} custom_next_4`}
				style={{
					backgroundImage: `url(${pageData.acf.about_details.image})`,
					backgroundSize: "cover", // Optional: Adjust background size
					backgroundPosition: "top -20px center", // Optional: Adjust background position
					backgroundRepeat: "no-repeat", // Optional: Prevent image repetition
				}}
			>
				<div
					className={`${comon.wrap}`}
					data-aos="fade-in"
					data-aos-duration="1000"
				>

					<div className={`${home.corporate_block}`}>
						<h3 data-aos="fade-up" data-aos-duration="1000">
							{parse(pageData.acf.about_details.title)}
						</h3>

						<Link
							className={`${comon.buttion} ${comon.mt_20}`}
							href={''}
							onClick={() => router.push('/csr')}
						>
							{parse(pageData.acf.about_details.button.title)}
						</Link>

						<div
							className={`${home.bottom_row_corporate} ${home.mt_40}`}
							data-aos="fade-in"
							data-aos-delay="300"
							data-aos-duration="1000"
						>
							{parse(pageData.acf.about_details.description)}
						</div>
					</div>
				</div>
			</section>

			<section className={`${comon.pt_65} ${comon.pb_65}`}>
				<div className={`${comon.wrap}`}>
					<div
						className={`${comon.w_100} ${comon.d_flex_wrap} ${comon.justify_space_bet} ${home.news_insight_fixed_height_block}`}
					>
						<div className={`${home.insight_left_block}`}>
							<div className={`${comon.title_30} ${comon.mb_10}`}>
								<h3 data-aos="fade-up" data-aos-duration="1000">
									{parse(pageData.acf.details_news.title)}
								</h3>
							</div>

							<div
								data-aos="fade-up"
								data-aos-delay="300"
								data-aos-duration="1000"
							>
								<Link
									className={` ${comon.mt_20}  ${comon.link}`}
									href={"/news"}
								>
									{pageData.acf.details_news.button.title}
								</Link>
							</div>

							{/* Image Slider */}
							<div
								className={`${comon.w_100} ${comon.latest_insight_img_sec} ${home.latest_insight_img_sec} ${comon.mt_100}`}
								style={{
									position: "relative",
									overflow: "hidden",

									aspectRatio: "3 / 3",
								}}
								aria-hidden="true"
								data-aos="fade-up"
								data-aos-delay="350"
								data-aos-duration="1000"
							>
								<div
									style={{
										display: "flex",
										height: "100%",
										flexDirection: "column", // Stack images vertically
										transition: "transform 0.5s ease",
										transform: `translateY(-${activeIndex * 100}%)`,
									}}
								>
									{NewsList.map((image, index) => (
										<div
											key={index}
											style={{
												minHeight: "100%",
												width: "100%",
												position: "relative",
											}}
										>
											<Image
												className={`${comon.w_100} ${comon.holder} slide_img`}
												src={image._embedded["wp:featuredmedia"][0].source_url}
												alt={`Insight Image ${index + 1}`}
												fill
												style={{
													objectFit: "cover",
												}}
												quality={100}
											/>
										</div>
									))}
								</div>
							</div>
						</div>

						<div className={`${home.insight_right_block}`}>
							<ul className={`${home.news_list_ul} ${rtl.news_list_ul}`}>
								{NewsList.map((item, index) => {
									const fixedDate = new Date(item.date);
                                    const day = fixedDate.getDate();
                                    const year = fixedDate.getFullYear();
                                    const month = monthNames[locale === 'ar' ? 'ar' : 'en'][fixedDate.getMonth()];

									const formattedFixedDate = (
										<div className="news_date_section">
											<span className="base_font ">{day}</span> {month} <span className="base_font">{year}</span>
										</div>
									);
									return (
										<li
											key={index}
											onMouseEnter={() => setActiveIndex(index)}
											data-aos="fade-up"
											data-aos-delay={index * 250}
											data-aos-duration="1000"
											style={{ position: 'relative' }}
										>
											{/* <Link href={`news/${item.slug}`}
											style={{position:'absolute', width:'100%', height:'100%',
												left:'0', top:'0'
											}}
											></Link> */}
											<span className={`${home.news_date} ${rtl.news_date} `} 
											>
												{formattedFixedDate}
											</span>
											<p
												style={{
													display: "-webkit-box",
													WebkitLineClamp: 1,
													WebkitBoxOrient: "vertical",
													overflow: "hidden",
													// minHeight: '55px',
													// maxHeight: '55px'
												}}
											>{parse(item.title.rendered)}</p>
											<Link href={`news/${item.slug}`}>
												{locale === "ar" ? "اقرأ المزيد" : "Read More"}
											</Link>
										</li>
									);
								})}
							</ul>
						</div>
					</div>
				</div>
			</section>
		</>
	);
}

export async function getStaticProps({ locale }) {
	const langCode = locale === "ar" ? "ar" : "en";
	///const langCode = "en"
	const slug = null;
	try {
		const pageData = await fetchPageById("home", langCode);
		const NewsList = await fetchPostList("news", 3, langCode, slug);
		const LocList = await fetchPostList("all-around", 100, langCode, slug);
		const LocList1 = await fetchPostList("locations", 100, langCode, slug);
		return {
			props: {
				pageData,
				NewsList,
				LocList,
				LocList1
			},
			revalidate: 10,
		};
	} catch (error) {
		console.error("Error fetching data:", error);
		return {
			props: {
				pageData: null,
			},
		};
	}
}
