import React, { useState, useEffect, useRef } from "react";
import Head from "next/head";
// ---buttion-import--start
import But<PERSON> from "@/component/button/Buttons";

import Marquee from "@/component/Marquee";
// ---buttion-import--end

import Image from "next/image";
import BannerHome from "@/component/BannerHome";
import Parallax from "@/component/Paralax";
import Map from "@/component/contactmap";
// Import Swiper styles
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/scrollbar";
import { Scrollbar, Autoplay, Navigation } from "swiper/modules";
import comon from "@/styles/comon.module.scss";
import rtl from "@/styles/rtl.module.scss";
import home from "@/styles/home.module.scss";
import buttion from "@/styles/buttion.module.scss";
import DynamicImage from "@/component/DynamicImage";

import HeadMarquee from "@/component/HeadMarquee";
import SliderSection from "@/component/SliderSection";
import ProjectPartners from "@/component/ProjectPartners";
import CountUp from "react-countup";
import { useInView } from "react-intersection-observer";
import parse from "html-react-parser";
import { fetchPageById, fetchPostList } from "@/lib/api/pageApi";
import { useRouter } from "next/router";
import Link from "next/link";
import ContactmapHover from "@/component/contactmapHover";

export default function Home({ pageData, NewsList, LocList, LocList1 }) {
  const [activeIndex, setActiveIndex] = useState(0);
  const { ref, inView } = useInView({ triggerOnce: true, threshold: 0.2 });
  const [mergedLocations, setMergedLocations] = useState([]);
  const [fetchedData, setFetchedData] = useState(null);
  const { locale } = useRouter();

  const monthNames = {
    en: [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ],
    ar: [
      "يناير",
      "فبراير",
      "مارس",
      "أبريل",
      "مايو",
      "يونيو",
      "يوليو",
      "أغسطس",
      "سبتمبر",
      "أكتوبر",
      "نوفمبر",
      "ديسمبر",
    ],
  };

  const formattedContent = pageData.acf.slider_details_.map((item) => {
    if (!item.acf_fc_layout) return "";
    if (item.acf_fc_layout === "small_bold_text") {
      return `<b>${item.text_bold}</b>`;
    } else if (item.acf_fc_layout === "regular_big_text") {
      return item.text__reg;
    }
    return "";
  });
  const rearrangedData = locale === "ar"
    ? pageData?.map_locations_serialized_ar
    : pageData?.map_locations_serialized;


  return (
    <>
      <Head>
        <title>Al Arabia</title>
        <meta name="description" content="Generated by create next app" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon-new.ico" />
      </Head>

      <BannerHome
        isOverHeight={true}
        bannerData={pageData.acf.banner_details}
      />

      <section
        className={`${comon.pt_50} ${comon.pb_50}`}
        style={{ backgroundColor: "#6758B6", overflow: "hidden" }}
      >
        <div ref={ref} className={`${comon.wrap}`}>
          <div
            className={`${comon.d_flex_wrap} ${comon.more_than_sec} ${comon.w_100}`}
          >
            <div className={`${home.home_txt_01} ${rtl.home_txt_01}`}>
              <h1>
                {pageData.acf.overview_details.title_overview}
                <br />
                <span>{pageData.acf.overview_details.short_description}</span>
              </h1>
            </div>
            <div className={`${home.home_txt_02}`}>
              <ul
                className={`${home.home_txt_counder} ${rtl.home_txt_counder} ${comon.more_than_counter}`}
              >
                {pageData.acf.overview_details.counter.map((item, index) => (
                  <li key={index}>
                    <h2>
                      <CountUp
                        start={0}
                        end={item.value}
                        duration={3}
                        startOnMount={false}
                        redraw={true}
                        delay={0}
                        play={inView}
                      >
                        {({ countUpRef }) => (
                          <span ref={countUpRef} className="base_font" />
                        )}
                      </CountUp>
                      {item.postfix}
                    </h2>
                    <p>{item.text_}</p>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      <SliderSection
        images={pageData.acf.list_details.list.map((item) => ({
          img: item.image,
          description: item.title,
          link: item.project_link,
        }))}
        title={pageData.acf.list_details.title_list}
        PageLink={true}
      />

      {rearrangedData && pageData ? (
        <ContactmapHover
          textBtn_zoomBtn_hide={true}
          //temporarly hide this for client changes
          mapDetails={pageData.acf.location_details}
          locationList={rearrangedData}
        />
      ) : (
        <p className="text-gray-500">Loading map data...</p>
      )}

      {formattedContent && (
        <Marquee
          data-aos="fade-in"
          data-aos-duration="1000"
          content={formattedContent}
          speed={99999}
          direction="left"
        />
      )}

      <ProjectPartners images={pageData.acf.partners_list} />

      <section
        data-aos="fade-in"
        data-aos-duration="1000"
        className={`${comon.pt_95} ${comon.pb_95} custom_next_4`}
        style={{
          backgroundImage: `url(${pageData.acf.about_details.image})`,
          backgroundSize: "cover", // Optional: Adjust background size
          backgroundPosition: "top -20px center", // Optional: Adjust background position
          backgroundRepeat: "no-repeat", // Optional: Prevent image repetition
        }}
      >
        <div
          className={`${comon.wrap}`}
          data-aos="fade-in"
          data-aos-duration="1000"
        >
          <div className={`${home.corporate_block}`}>
            <h3 data-aos="fade-up" data-aos-duration="1000">
              {parse(pageData.acf.about_details.title)}
            </h3>

            <Link
              className={`${comon.buttion} ${comon.mt_20}`}
              href={pageData.acf.about_details.button.url}
            >
              {parse(pageData.acf.about_details.button.title)}
            </Link>

            <div
              className={`${home.bottom_row_corporate} ${home.mt_40}`}
              data-aos="fade-in"
              data-aos-delay="300"
              data-aos-duration="1000"
            >
              {parse(pageData.acf.about_details.description)}
            </div>
          </div>
        </div>
      </section>

      <section className={`${comon.pt_65} ${comon.pb_65}`}>
        <div className={`${comon.wrap}`}>
          <div
            className={`${comon.w_100} ${comon.d_flex_wrap} ${comon.justify_space_bet} ${home.news_insight_fixed_height_block}`}
          >
            <div className={`${home.insight_left_block}`}>
              <div className={`${comon.title_30} ${comon.mb_10}`}>
                <h3 data-aos="fade-up" data-aos-duration="1000">
                  {parse(pageData.acf.details_news.title)}
                </h3>
              </div>

              <div
                data-aos="fade-up"
                data-aos-delay="300"
                data-aos-duration="1000"
              >
                <Link
                  className={` ${comon.mt_20}  ${comon.link}`}
                  href={pageData.acf.details_news.button.url}
                >
                  {pageData.acf.details_news.button.title}
                </Link>
              </div>

              {/* Image Slider */}
              <div
                className={`${comon.w_100} ${comon.latest_insight_img_sec}  ${comon.insight_img_new_sec} ${home.latest_insight_img_sec} ${comon.mt_100}`}
                style={{
                  position: "relative",
                  overflow: "hidden",

                  aspectRatio: "3 / 3",
                }}
                aria-hidden="true"
                data-aos="fade-up"
                data-aos-delay="350"
                data-aos-duration="1000"
              >
                <div
                  style={{
                    display: "flex",
                    height: "100%",
                    flexDirection: "column", // Stack images vertically
                    transition: "transform 0.5s ease",
                    transform: `translateY(-${activeIndex * 100}%)`,
                  }}
                >
                  {NewsList.map((image, index) => (
                    <div
                      key={index}
                      style={{
                        minHeight: "100%",
                        width: "100%",
                        position: "relative",
                      }}
                    >
                      <Image
                        className={`${comon.w_100} ${comon.holder} slide_img`}
                        src={image._embedded["wp:featuredmedia"][0].source_url}
                        alt={`Insight Image ${index + 1}`}
                        fill
                        style={{
                          objectFit: "cover",
                        }}
                        quality={100}
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className={`${home.insight_right_block}`}>
              <ul className={`${home.news_list_ul} ${rtl.news_list_ul}`}>
                {NewsList.map((item, index) => {
                  const fixedDate = new Date(item.date);
                  const day = fixedDate.getDate();
                  const year = fixedDate.getFullYear();
                  const month =
                    monthNames[locale === "ar" ? "ar" : "en"][
                    fixedDate.getMonth()
                    ];

                  const formattedFixedDate = (
                    <div className="news_date_section">
                      <span className="base_font ">{day}</span> {month}{" "}
                      <span className="base_font">{year}</span>
                    </div>
                  );
                  return (
                    <li
                      key={index}
                      onMouseEnter={() => setActiveIndex(index)}
                      data-aos="fade-up"
                      data-aos-delay={index * 50}
                      data-aos-duration="1000"
                      style={{ position: "relative" }}
                    >
                      {/* <Link href={`news/${item.slug}`}
												style={{position:'absolute', width:'100%', height:'100%',
													left:'0', top:'0'
												}}
												></Link> */}
                      <span className={`${home.news_date} ${rtl.news_date} `}>
                        {formattedFixedDate}
                      </span>
                      <p
                        style={{
                          display: "-webkit-box",
                          WebkitLineClamp: 1,
                          WebkitBoxOrient: "vertical",
                          overflow: "hidden",
                          // minHeight: '55px',
                          // maxHeight: '55px'
                        }}
                      >
                        {parse(item.title.rendered)}
                      </p>
                      <Link href={`news/${item.slug}`}>
                        {locale === "ar" ? "اقرأ المزيد" : "Read More"}
                      </Link>
                    </li>
                  );
                })}
              </ul>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}

export async function getStaticProps({ locale }) {
  const langCode = locale === "ar" ? "ar" : "en";
  const slug = null;
  try {
    const pageData = await fetchPageById("home", langCode);
    const NewsList = await fetchPostList("news", 3, langCode, slug);
    const LocList = await fetchPostList("all-around", 100, langCode, slug);
    const LocList1 = await fetchPostList("locations", 100, langCode, slug);
    return {
      props: {
        pageData,
        NewsList,
        LocList,
        LocList1,
      },
      revalidate: 10,
    };
  } catch (error) {
    console.error("Error fetching data:", error);
    return {
      props: {
        pageData: null,
      },
    };
  }
}
