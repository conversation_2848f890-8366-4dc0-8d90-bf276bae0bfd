import React, { useRef, useEffect, useState } from "react";
import { gsap } from "gsap";
import styles from "@/styles/marquee.module.scss";

const HeadMarquee = ({
  content = [
    {
      text1: "Arabian contracting services",
      text2: "SAR 167",
      specialChar: "8.10%",
    },
    {
      text1: "Arabian contracting services",
      text2: "SAR 167",
      specialChar: "8.10%",
    },
    {
      text1: "Arabian contracting services",
      text2: "SAR 167",
      specialChar: "8.10%",
    },
  ],
  speed = 400,
  direction = "left",
  loop = "true",
  className = "",
}) => {
  // const marqueeRef = useRef(null);
  // const marqueeContentRef = useRef(null);
  // const animationRef = useRef(null);
  // const [contentWidth, setContentWidth] = useState(0);
  // console.log("contentWidth", contentWidth);

  // useEffect(() => {
  //   const content = marqueeContentRef.current;
  //   if (!content) return;

  //   // Ensure we get the correct content width
  //   const updateWidth = () => {
  //     setContentWidth(content.scrollWidth);
  //   };

  //   updateWidth();
  //   window.addEventListener("resize", updateWidth);

  //   return () => {
  //     window.removeEventListener("resize", updateWidth);
  //   };
  // }, [content]);

  // useEffect(() => {
  //   if (!contentWidth) return;

  //   const content = marqueeContentRef.current;
  //   if (!content) return;

  //   gsap.set(content, { x: 0 });

  //   animationRef.current = gsap.to(content, {
  //     x: direction === "left" ? -contentWidth : contentWidth,
  //     duration: speed,
  //     repeat: 20,
  //     ease: "none",
  //     onUpdate: () => {
  //       if (parseFloat(gsap.getProperty(content, "x")) <= -contentWidth) {
  //         gsap.set(content, { x: 0 }); // Reset position seamlessly
  //       }
  //     },
  //   });

  //   return () => {
  //     animationRef.current.kill();
  //   };
  // }, [direction, speed, contentWidth]);

  // const handleMouseEnter = () => {
  //   if (animationRef.current) {
  //     animationRef.current.pause();
  //   }
  // };

  // const handleMouseLeave = () => {
  //   if (animationRef.current) {


  //     animationRef.current.play();
  //   }
  // };

  return (
    // <div
    //   className={`${styles.head_marqueeWrapper} ${styles.head_marqueeWrapper_overlay} ${className}`}
    //   ref={marqueeRef}
    //   onMouseEnter={handleMouseEnter}
    //   onMouseLeave={handleMouseLeave}
    // >
    //   <div className={styles.marqueeContent} ref={marqueeContentRef}>
    //     {Array.from({ length: 10 }).map((_, iteration) => (
    //       <>
    //         {content.map((item, index) =>
    //           typeof item === "string" && item.startsWith("http") ? (
    //             <img
    //               key={index}
    //               src={item}
    //               alt={`Marquee item ${index}`}
    //               className={styles.marqueeImage}
    //             />
    //           ) : (
    //             <>
    //               <p key={index} className={styles.marqueeText}>
    //                 {item.text1}
    //               </p>
    //               <p key={index} className={styles.marqueeText}>
    //                 {item.text2}
    //               </p>
    //               <p
    //                 key={index}
    //                 className={`${styles.marqueeText} ${styles.specialChar}`}
    //               >
    //                 {item.specialChar}
    //               </p>
    //             </>
    //           )
    //         )}
    //       </>
    //     ))}
    //   </div>
    // </div>
    <></>

  );
};

export default HeadMarquee;
