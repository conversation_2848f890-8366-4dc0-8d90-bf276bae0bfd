import React, { useEffect, useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import comon from "@/styles/comon.module.scss";
import rtl from "@/styles/rtl.module.scss";
import "swiper/css/effect-coverflow";
import "swiper/css/pagination";
import project from "@/styles/project.module.scss";
import parse from 'html-react-parser';
import {
	Autoplay,
	EffectCoverflow,
	Navigation,
	Pagination,
} from "swiper/modules";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";

import gsap from "gsap";
import ScrollTrigger from "gsap/dist/ScrollTrigger";
import Link from "next/link";

gsap.registerPlugin(ScrollTrigger);

const SliderAnimMezah = ({ sliderData,secondData, ImageSrc, className, isHideMainImg = false }) => {
	const [isMobile, setIsMobile] = useState(
		document.documentElement.clientWidth <= 819
	); // More accurate initial state

	useEffect(() => {
		const checkScreenSize = () => {
			const width = document.documentElement.clientWidth; // Excludes scrollbar
			console.log("Current clientWidth:", width); // Debugging log
			setIsMobile(width <= 819);
		};

		// Initial check
		checkScreenSize();

		// Attach listener
		window.addEventListener("resize", checkScreenSize);

		// Cleanup listener
		return () => {
			window.removeEventListener("resize", checkScreenSize);
		};
	}, []);

	// console.log("isMobile:", isMobile); // Debugging log

	// console.log("isMobile:", isMobile);

	const [activeIndex, setActiveIndex] = useState(0);

	// console.log("className", className);

	
	const [HeadName, setHeadname] = useState(sliderData[0]['slider_descriptions'].title);
	// console.log(HeadName);
	const sliderCount = (swiper) => {
		const activeIndex = swiper.activeIndex; 
		setHeadname(sliderData[activeIndex]['slider_descriptions'].title); 
	};


	const getActiveImagePath = () => {
		if (sliderData[activeIndex]) {
			return sliderData[activeIndex].slider_image;
		}
		return "";
	};

	// useEffect(() => {

	//     const imgContainer = document.getElementById("activeImage");
	//     const containerBody = document.getElementById("activeImageSection");

	//     if (imgContainer && containerBody) {
	//         // Ensure the image starts with the correct transform and scale
	//         gsap.set("#activeImage", {
	//             // scale: 10,
	//             scale: 10,
	//             opacity: 1,

	//             transform: "translate(-50%, -50%)",
	//         });

	//         // Create the ScrollTrigger-based animation
	//         gsap.to("#activeImage", {
	//             scale: 1,

	//             ease: "power1.out",
	//             duration: 2,
	//             scrollTrigger: {
	//                 trigger: "#activeImageSection", // The element to watch
	//                 start: "top 30%", // Start when the top of the container touches the top of the viewport
	//                 end: "bottom bottom", // Optional: define the end of the animation if needed
	//                 toggleActions: "play none none none", // Only play the animation once
	//             },
	//             onComplete: () => {
	//                 // Reduce opacity over 0.3s
	//                 gsap.to("#activeImage", { opacity: 0, duration: 0.3 });
	//             },
	//         });
	//     }
	// }, []);

	useEffect(() => {
		const mm = gsap.matchMedia(); // GSAP media query system

		mm.add("(min-width: 820px)", () => {
			// Runs only if screen width is >= 820px
			gsap.set("#activeImage", {
				// scale: 8,
				// scale: 9,
				scale: 10,
				opacity: 1,
				transform: "translate(-50%, -50%)",
			});

			gsap.to("#activeImage", {
				scale: 1,
				ease: "power1.out",
				duration:1.3,
				scrollTrigger: {
					trigger: "#activeImageSection",
					start: "top 40%",
					// start: "top 20%",
					end: "bottom bottom",
					toggleActions: "play none none none",
				},
				onComplete: () => {
					gsap.to("#activeImage", { opacity: 0, duration: 0.3 });
				},
			});
		});

		return () => mm.revert(); // Cleanup on unmount
	}, []);

	useEffect(() => {
		const mm = gsap.matchMedia(); // GSAP media query system

		mm.add("(min-width: 1280px)", () => {
			// Runs only if screen width is >= 820px
			// gsap.set(".slider_anim_new", {
			gsap.set(".slider_anim_new", {
				y: 150,
				scale: 0.9,
			});

			gsap.to(".slider_anim_new", {
				y: 0,
				scale: 1,

				ease: "power1.out",
				duration: 1.3,
				scrollTrigger: {
					trigger: "#activeImageSection",
					start: "top 60%",
					end: "bottom bottom",
					toggleActions: "play none none none",
				},
			});
		});

		return () => mm.revert(); // Cleanup on unmount
	}, []);

		const swiperRef = useRef()
	
	return (
		<>
			{isMobile == false ? (
				<div className="h_100">
					{/* {getActiveImagePath() && (
						<div className="overlap_image  mezah" id="activeImage">
							<Image
								src={getActiveImagePath()}
								alt="slider1"
								width={500}
								height={500}
								quality={100}
								loading="lazy"
							/>
						</div>
					)} */}

					{isHideMainImg == false ? (
						<div className="overlap_image mezah" id="activeImage">
							<Image
								src={getActiveImagePath()}
								alt="Active Slider Image"
								width={500}
								height={500}
								quality={100}
								loading="lazy"
							/>
						</div>
					) : (
						""
					)}
					<div
						className={`${comon.wrap} ${project.project_title} ${project.min_width} ${comon.d_flex_wrap}  ${comon.pt_60}`}
						data-aos="fade-in"
						data-aos-duration="1000"
						id="activeImageSection"
					>
						<div class={`${comon.w_50}  `}>
							<div class={`${comon.title_30} ${comon.mb_10}  ${comon.pr_10}`}>
								{/* <h3>Smart Riyadh</h3> */}
								<div className="img_logo">
								{secondData.logo_title ? (
									<Image
										src={secondData.title_logo}
										height={100}
										width={200}
										alt=""
										quality={100}
									/>
								):(
									<h2>{secondData.title_text}</h2> 
								)}
								</div>
								<>
									{parse(secondData.description)}
								</>
							</div>
						</div>

						{secondData.project_switch_logos.logo_1 && secondData.project_switch_logos.logo_2 && (
						<div className={`${comon.w_50}  ${comon.d_flex_wrap}`}>
							<ul className={`${project.project_tab} ${rtl.project_tab}`}>
								{secondData.project_switch_logos.logo_1 &&
								 secondData.project_switch_logos.link_1 && (
								<li className={`${project.active}`}>
									<Link href={secondData.project_switch_logos.link_1}>
										<Image
											src={secondData.project_switch_logos.logo_1}
											width={74}
											height={42}
											alt="button image"
											quality={100}
										/>
									</Link>
								</li>
								)}
								{secondData.project_switch_logos.logo_2 &&
								 secondData.project_switch_logos.link_2 && (
								<li >
									<Link href={secondData.project_switch_logos.link_2}>
										<Image
											src={secondData.project_switch_logos.logo_2}
											width={70}
											height={42}
											alt="button image"
											quality={100}
										/>
									</Link>
								</li>
								)}
							</ul>
						</div>
						)}
					</div>

					<div className={`${comon.wrap}  `}>
						<div className="slidernew1 mezah_page ">
							<Swiper
								centeredSlides={true}
								slidesPerView={5}
								onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)} // `realIndex` is more accurate for looped swipers
								loop={true}
								spaceBetween={20}
								coverflowEffect={{
									rotate: 30,
									stretch: 0,
									depth: 200,
									modifier: 1,
									slideShadows: true,
								}}
                                // onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
                                onSwiper={(swiper) => (swiperRef.current = swiper)}

								navigation={{
									nextEl: ".swiper-button.next",
									prevEl: ".swiper-button.prev",
								}}
								pagination={{ clickable: true }}
								//   ${className == "maleem" ? "maleem_border" : ""}
								className={`slider_anim_new mezah_page
									
									`}
								modules={[Navigation, Autoplay, EffectCoverflow]}
							>
								{sliderData.map((data, index) => (
									<SwiperSlide  onClick={() => swiperRef.current?.slideToLoop(index)} key={index} className="slider_top_moving">
										<div className={`slider_anim_new_img mezah `}>
											<Image
												src={data.slider_image}
												alt={`slider-${index + 1}`}
												width={500}
												height={500}
												quality={100}
											/>
										</div>
									</SwiperSlide>
								))}
							</Swiper>

							<div
								className={`slider_anim_new_btn mezah_page bt_60  project_slider_btn`}
							>
								<button className="swiper-button prev">❮</button>
								<button className="swiper-button next">❯</button>
							</div>
							{/* {sliderData[activeIndex]['slider_descriptions'].title !== 0 && (
								<div className="slider_heading">
									<h4>{sliderData[activeIndex]['slider_descriptions'].title}</h4>
								</div>
							)} */}
						</div>
					</div>

					<div
						className={`${project.project_second_section}    ${comon.wrap} ${comon.d_flex_wrap} ${comon.pt_70}`}
					>
						<div
							class={`${project.w_45}`}
							data-aos="fade-in"
							data-aos-duration="1000"
						>
							<div
								class={`${project.project_logo} ${project.project_logo_icon} ${comon.mb_30}`}
							>
								{sliderData[activeIndex].slider_descriptions.logo_title_d ? (
									<Image
										src={`${sliderData[activeIndex].slider_descriptions.logo}`}
										// src="/images/mezah.png"
										alt="slider"
										width={304}
										className="max_w_img"
										height={44}
										quality={100}
									/>
								):(
									<h2>{sliderData[activeIndex].slider_descriptions.title}</h2>
								)}
								
							</div>
							{sliderData[activeIndex].slider_descriptions.button && (

								<Link
									href="#"
									className={`${comon.buttion} ${comon.display_hidden} ${comon.but_blue} ${comon.but_h_02}  ${comon.but_white}`}
								>
									Check Locations
								</Link>
							)}
							
						</div>

						<div
							class={`${project.w_55} ${project.project_description}`}
							data-aos="fade-in"
							data-aos-duration="1000"
						>
							<>
								{/* The innovative 3d screen spotted across very important locations
								and landmarks across all of riyadh. On the 90th Saudi National
								Day, AlArabia launched a campaign across the country to support
								90 startup businesses to overcome the limitations caused by
								Covid-19. AlArabia allocated 18 million Saudi Riyal to be
								distributed among the 90 winners. */}

								{parse(sliderData[activeIndex].slider_descriptions.description)}
							</>
						</div>
					</div>
				</div>
			) : (
				<div>
					<div
						className={`${comon.wrap} ${project.project_title} ${comon.d_flex_wrap}  ${project.col_rev_mob}     ${comon.pt_50}`}
						data-aos="fade-in"
						data-aos-duration="1000"
					>
						<div class={`${comon.w_50}`}>
							<div class={`${comon.title_30} ${comon.mb_10} ${comon.pr_10}`}>
								{/* <h3>Smart Riyadh</h3> */}
								<div className="img_logo  project_mezah_img_logo  ">
								{secondData.logo_title ? (
									<Image
										src={secondData.title_logo}
										height={100}
										width={200}
										alt=""
										quality={100}
									/>
								):(
									<h2>{secondData.title_text}</h2>
								)}
								</div>
								<>
									{parse(secondData.description)}
								</>
							</div>
						</div>

						<div
							className={` ${comon.w_50} ${project.mob_margin} ${comon.d_flex_wrap}`}
						>
							<ul className={`${project.project_tab}`}>
								<li className={`${project.active}`}>
									<Link href="project-mezah">
										<Image
											src={secondData.project_switch_logos.logo_1}
											width={74}
											height={42}
											alt="button image"
											quality={100}
										/>
									</Link>
								</li>

								<li>
									<Link href="project-maalem">
										<Image
											src={secondData.project_switch_logos.logo_2}
											width={70}
											height={42}
											alt="button image"
											quality={100}
										/>
									</Link>
								</li>
							</ul>
						</div>
					</div>

					<div className={`${comon.pt_60} ${comon.pb_60}`}>
						<Swiper
							centeredSlides={true}
							slidesPerView={1}
							onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)} // `realIndex` is more accurate for looped swipers
							loop={true}
							spaceBetween={20}
							coverflowEffect={{
								rotate: 30,
								stretch: 0,
								depth: 200,
								modifier: 1,
								slideShadows: true,
							}}
                                onSwiper={(swiper) => (swiperRef.current = swiper)}

							navigation={{
								nextEl: ".swiper-button.next",
								prevEl: ".swiper-button.prev",
							}}
							breakpoints={{
								600: {
									slidesPerView: 2.5,
								},
							}}
							pagination={{ clickable: true }}
							modules={[Navigation, Autoplay, EffectCoverflow]}
							className="slider_anim dubai_mobile_swiper dubai"
						>
							{sliderData.map((data, index) => (
								<SwiperSlide key={index}
								onClick={() => swiperRef.current?.slideToLoop(index)}>
									<div className="slider_anim_img">
										<Image
											src={data.slider_image}
											alt={`slider-${index + 1}`}
											width={500}
											height={500}
											quality={100}
										/>
									</div>
								</SwiperSlide>
							))}
						</Swiper>

						<div
							className={`slider_anim_new_btn mezah_btn dubai project_slider_btn`}
						>
							<button className="swiper-button prev"> </button>
							<button className="swiper-button next"> </button>
						</div>
						{/* {SliderHead.length !== 0 && (
							<div className="slider_anim_heading">
								<h4>{HeadName}</h4>
							</div>
						)} */}
					</div>

					<div
						className={`${project.project_second_section}  ${comon.wrap} ${comon.d_flex_wrap} ${comon.pt_70}`}
					>
						<div
							class={`${project.w_45}`}
							data-aos="fade-in"
							data-aos-duration="1000"
						>
							<div
								class={`${project.project_logo} ${project.project_logo_icon} ${comon.mb_30}`}
							>
								{/* <Image
									src="/images/mezah.png"
									alt="slider"
									width={304}
									className="max_w_img"
									height={44}
								/> */}
								{sliderData[activeIndex].slider_descriptions.logo_title_d ? (
									<Image
										src={`${sliderData[activeIndex].slider_descriptions.logo}`}
										// src="/images/mezah.png"
										alt="slider"
										width={304}
										className="max_w_img"
										height={44}
									/>
								):(
									<h2>{sliderData[activeIndex].slider_descriptions.title}</h2>
								)}
							</div>

							{sliderData[activeIndex].slider_descriptions.button && (

								<Link
									href="#"
									className={`${comon.buttion} ${comon.display_hidden} ${comon.but_blue} ${comon.but_h_02}  ${comon.but_white}`}
								>
									Check Locations
								</Link>
							)}
						</div>

						<div
							class={`${project.w_55} ${project.project_description}`}
							data-aos="fade-in"
							data-aos-duration="1000"
						>
							<>
								{/* The innovative 3d screen spotted across very important locations
								and landmarks across all of riyadh.On the 90th Saudi National
								Day, AlArabia launched a campaign across the country to support
								90 startup businesses to overcome the limitations caused by
								Covid-19. AlArabia allocated 18 million Saudi Riyal to be
								distributed among the 90 winners. */}

								{parse(sliderData[activeIndex].slider_descriptions.description)}

							</>
						</div>
					</div>
				</div>
			)}
		</>
	);
};

export default SliderAnimMezah;