import React, { useState } from "react";
import { DateRangePicker } from "react-date-range";
import { addDays } from "date-fns";
import { enGB } from "date-fns/locale";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";


const customLocale = {
    ...enGB,
    localize: {
        ...enGB.localize,
        day: (n) => ["S", "M", "T", "W", "T", "F", "S"][n],
    },
};

const RangeCalendar = ({ darkMode = false, onDateChange  }) => {
    const [dateRange, setDateRange] = useState({
        startDate: new Date(),
        endDate: new Date(),
        key: "selection",
    });

    const handleRangeSelect = (ranges) => {
        const newRange = ranges.selection;
        setDateRange(newRange);
        if (onDateChange) {
            onDateChange(newRange); 
        }
    };
    return (
        <div
            data-aos="fade-in"
            data-aos-duration="1000"
            className={`${darkMode ? "dark_theme" : ""}`}
        >
            <DateRangePicker
                ranges={[dateRange]}
                onChange={handleRangeSelect}
                showSelectionPreview={false}
                showMonthAndYearPickers={true}
                showDateDisplay={false}
                staticRanges={[]}
                inputRanges={[]}
                minDate={new Date()}
                locale={customLocale}
            />
        </div>
    );
};

export default RangeCalendar;
