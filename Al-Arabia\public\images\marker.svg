<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.3.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 100 100" style="enable-background:new 0 0 100 100;" xml:space="preserve">
<style type="text/css">
	.st0{opacity:0.6;fill:none;stroke:#E2DFED;stroke-width:5.1154;enable-background:new    ;}
	.st1{opacity:0.6;fill:none;stroke:#6758B6;stroke-width:5.1154;enable-background:new    ;}
	.st2{opacity:0.6;fill:none;stroke:#6758B6;stroke-width:4;enable-background:new    ;}


 
  circle {
    animation: pulse 2s infinite;
  }
  @keyframes pulse {
    0% { r: 40; }
    50% { r: 50; }
    100% { r: 40; }
  }
 




</style>
<circle class="st0" cx="50" cy="48.3" r="9.1"/>
<circle class="st1" cx="50" cy="48.3" r="23.5"/>
<circle class="st0" cx="50" cy="48.3" r="36.1"/>
<circle class="st2" cx="50" cy="48.3" r="25.7"/>
</svg>
