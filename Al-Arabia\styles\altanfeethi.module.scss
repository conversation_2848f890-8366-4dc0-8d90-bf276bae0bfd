@import "variable", "base", "mixin";

.altanfeethi_about_wrap {
  align-items: center;

  .altanfeethi_about {
    width: 45%;
    padding-right: 8%;

    @media #{$media-768} {
      width: 100%;
      padding-right: 0;
      margin-bottom: 30px;
    }
  }

  .altanfeethi_blocks {
    width: 55%;

    @media #{$media-768} {
      width: 100%;
    }

    ul {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      align-items: flex-start;

      li {
        width: calc(50% - 10px);
        min-height: 220px;
        padding: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        z-index: 1;
        border: 1px solid rgba(0, 0, 0, 0);

        transition: all 0.3s ease;

        &:hover {
          border: 1px solid rgb(0, 0, 0);

          h4 {
            color: #000000;
          }

          &::after {
            background-color: hsla(0, 0%, 100%, 0.75);
          }
        }

        &::after {
          background-color: #000000;
          content: "";
          position: absolute;
          height: 100%;
          width: 100%;
          top: 0;
          left: 0;

          transition: all 0.3s ease;
        }

        @media #{$media-768} {
          min-height: 150px;
          padding: 10px;
        }

        h4 {
          @include rem(20);
          color: #fff;
          font-weight: 400;
          text-align: center;
          transition: all 0.3s ease;
          position: relative;
          z-index: 1;
          pointer-events: none;
        }

        .background_img_sec {
          position: absolute;
          z-index: 0;
          top: 0;
          left: 0;
          height: 100%;
          width: 100%;

          >img {
            height: 100%;
            width: 100%;
            object-fit: cover;
            display: block;
            filter: grayscale(1);
          }
        }

        &:nth-child(1) {
          margin-top: 60px;

          @media #{$media-768} {
            margin-top: 30px;
          }
        }

        &:nth-child(4) {
          margin-top: -60px;

          @media #{$media-768} {
            margin-top: -30px;
          }
        }
      }
    }
  }
}

.altanfeethi_image {
  width: 100%;

  img {
    width: 100%;
    height: auto;
  }
}

.altanfeethi_banner_content {
  @media #{$media-1200} {
    margin-top: 20px;
  }

  @media #{$media-768} {
    margin-top: 30px;
  }

  ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    li {
      width: 45%;

      @media #{$media-1200} {
        width: 44%;
      }

      @media #{$media-768} {
        width: 100%;
        margin-bottom: 15px;
      }

      &:nth-child(2) {
        width: 45%;

        @media #{$media-768} {
          width: 100%;
        }
      }

      p {
        color: #fff;
      }
    }
  }
}

.altanfeethi_slider_img {
  width: 60%;
  padding-right: 8%;
  padding-top: 100px;

  @media #{$media-768} {
    width: 100%;
    padding-right: 0;
    margin-bottom: 25px;
    padding-top: 30px;
  }

  img {
    width: 100%;
  }
}

.altanfeethi_slider_content {
  width: 40%;

  @media #{$media-768} {
    width: 100%;
    margin-bottom: 20px;
  }

  .slider_img {
    width: 100%;
    margin-bottom: 10px;

    img {
      width: 100%;
    }
  }

  .slider_content {
    width: 100%;

    h4 {
      @include rem(18);
      font-weight: 400;
      color: #fff;
      margin-bottom: 10px;
    }

    h3 {
      @include rem(30);
      font-weight: bold;
      color: #fff;
      margin-bottom: 10px;
    }

    p {
      color: #fff;
    }
  }
}

.nav_count_wrap {
  align-items: flex-end;
  column-gap: 20px;

  .nav {
    width: 22px;
    height: 16px;
    background-color: transparent;
    border: none;
    cursor: pointer;
    height: 30px;
    opacity: 0.6;
    transition: all 0.3s ease-in-out;

    &:hover {
      opacity: 1;
    }

    img {
      width: 22px;
      height: 16px;
    }
  }

  .nav_count {
    color: #fff;

    .count_active {
      @include rem(40);
      color: #fff;
      font-weight: 400;
    }

    .count_main {
      @include rem(21);
      color: #fff;
      font-weight: 400;
    }
  }

  .nav_btn {
    display: flex;
    column-gap: 6px;
  }
}

.altanfeethi_select {
  width: 100%;
  max-width: 350px;
  position: absolute;
  top: 0;
  z-index: 9;
  background: linear-gradient(180deg, #838383 0%, #7c7c7c 100%);

  @media #{$media-768} {
    position: static;
    max-width: inherit;
    background: transparent;
  }
}

.tuwqiq_banner_wrap {
  width: 100%;
  row-gap: 30px;
  align-items: center;
  margin-top: 35px;

  @media #{$media-768} {
    row-gap: 25px;
    margin-top: 25px;
  }

  .tuwqiq_banner_image {
    width: 54%;

    @media #{$media-768} {
      width: 100%;
    }

    img {
      width: 100%;
      height: auto;
    }
  }

  .tuwqiq_banner_content {
    width: 45%;
    padding-left: 5%;

    @media #{$media-768} {
      width: 100%;
      padding-left: 0;
    }

    img {
      margin-bottom: 15px;
    }

    p {
      color: #fff;
      margin-bottom: 20px;

      @media #{$media-768} {
        margin-bottom: 15px;
      }
    }
  }
}

.image_text_section {
  width: 100%;
  display: flex;
  flex-wrap: wrap;

  .image_block {
    width: 45%;

    @media #{$media-768} {
      width: 100%;
    }

    .image_sec {
      width: 100%;

      >img {
        width: 100%;
        display: block;

        height: auto;
        object-fit: cover;
      }
    }
  }

  .text_container {
    width: 55%;
    height: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-left: 5%;

    @media #{$media-768} {
      width: 100%;
      padding-left: 0;
      padding-top: 20px;
      justify-content: left;
    }

    .text_block {
      max-width: 510px;
      width: 100%;

      >p {
        color: #000000;
        font-size: 15px;
        font-weight: 400;

        @media #{$media-768} {
          font-size: 13px;
        }
      }

      >.img_icon_block {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-evenly;
        gap: 40px;

        position: relative;

        li {
          .img_icon {
            width: 65px;
            cursor: pointer;

            >img {
              width: 100%;
              height: auto;
              display: block;
              object-fit: contain;
            }

            @media #{$media-1440} {
              width: 50px;
            }

            @media #{$media-1280} {
              width: 45px;
            }

            @media #{$media-1024} {
              width: 35px;
            }
          }

          .desc_block {
            position: absolute;
            top: calc(100% + 15px);
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;

            transition: all 0.3s ease;
            width: 200px;

            p {
              color: black;
              font-size: 17px;
              line-height: 130%;
              text-align: center;
              text-transform: capitalize;

              @media #{$media-1366} {
                font-size: 15px;
              }

              @media #{$media-768} {
                font-size: 13px;
              }
            }

            @media #{$media-768} {
              top: calc(100% + 5px);

            }
            @media #{$media-700} {
              width: 150px;
            }
          }

          &:hover .desc_block {
            opacity: 1;
          }
        }




        @media #{$media-1024} {
          gap: 20px;
        }

        @media #{$media-700} {
          width: 100%;
          justify-content: center;
        }
      }
      @media #{$media-768} {
        max-width: 100%;
      }


    }

    @media #{$media-700} {
      width: 100%;
    }
  }
}

.pt_overview_hiddeen {
  overflow: hidden;
}

.pt_overview_gradient {
  overflow: hidden;
  background: rgb(139, 139, 139);
  background: linear-gradient(180deg,
      rgba(139, 139, 139, 1) 0%,
      rgba(40, 40, 40, 1) 100%);
}