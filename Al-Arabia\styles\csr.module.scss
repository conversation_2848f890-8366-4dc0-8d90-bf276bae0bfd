@import "variable", "base", "mixin";

.csr_fltr_block {
  max-width: 660px;
  margin-left: auto;
  margin-right: auto;

  .csr_fltr_block_ul {
    margin: 0 -2%;
    display: flex;
    flex-wrap: wrap;
    row-gap: 3px;

    &.new_dropdown {

      li {
        &:last-child {
          &::after {
            position: unset;
          }
        }
      }
    }

    &.kkia_page_drop_down {

      justify-content: center;

      .csr_select {

        list-style: none;
        // width: 451px;
        width: 68%;

        @media #{$media-700} {
          width: 100%;
        }

      }
    }

    li {
      list-style: none;
      width: calc(44% - 57.5px);
      list-style: none;
      margin: 0 2%;
      position: relative;
      height: 52px;

      &.csr_select {

        border-bottom: 1px solid #3B3064;
      }

      // background-color: red;

      // &::after {
      //   top: 50%;
      //   right: 4px;
      //   width: 13px;
      //   height: 10px;
      //   position: absolute;
      //   content: "";
      //   z-index: 8;
      //   background-image: url('/images/drop_down_arrow.svg');
      //   background-position: center;
      //   background-repeat: no-repeat;
      //   background-size: contain;
      //   pointer-events: none;
      // }




      @media #{$media-700} {
        width: 46%;
        margin-bottom: 10px;
        height: 45px;
      }

      // &.csr_select {
      //   position: relative;

      //   &::after {
      //     bottom: 0;
      //     left: 4px;
      //     width: calc(100% - 4px);
      //     height: 1px;
      //     position: absolute;
      //     content: "";
      //     z-index: 8;
      //     background-color: #3b3064;
      //   }
      // }
    }

    &> :last-child {
      width: 115px;
      display: flex;
      align-items: flex-end;

      a {
        width: 100%;
        justify-content: center;
        height: 100%;
      }

      @media #{$media-700} {
        width: 100%;
        justify-content: center;
      }
    }
  }
}


:global(body.rtl) .csr_fltr_block .csr_fltr_block_ul li::after {
  right: inherit;
  left: 4px;
}

.block_ul {
  display: flex;
  flex-wrap: wrap;
  margin-left: -0.6%;
  margin-right: -0.6%;

  li {
    width: 25%;
    padding: 0.6%;

    @media #{$media-700} {
      width: 50%;
    }
  }
}