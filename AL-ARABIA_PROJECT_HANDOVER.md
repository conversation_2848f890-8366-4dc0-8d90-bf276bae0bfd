# AL-ARABIA – Project Handover Document 

📌 **PROJECT OVERVIEW**

**Project Name:** Al-Arabia Outdoor Advertising Platform  
**Client:** Al-Arabia  
URL:https://al-arabia-stage.vercel.app/
**Handover Date:** 28-07-2025  
**Developer:** Development Team  

Al-Arabia is an outdoor advertising platform built as a headless WordPress solution with Next.js frontend. The platform provides location-based advertising services, interactive maps, campaign management, and multilingual support (English/Arabic).

🧱 **TECHNICAL ARCHITECTURE**

## Frontend Technology Stack

• **Framework:** Next.js 14.2.24  
• **React Version:** 18  
• **Styling:** SCSS Modules + Global CSS  
• **Internationalization:** Next.js i18n (English/Arabic)  
• **Animation Libraries:**  
  - AOS (Animate On Scroll)  
  - GSAP  
  - Locomotive Scroll  
  - Lenis Smooth Scroll  
• **UI Components:**  
  - React Google Maps API  
  - Swiper.js  
  - React CountUp  
  - React Date Range  
  - React Select  
  - React Tooltip  
  - React Intersection Observer  
• **Additional Libraries:**  
  - HTML React Parser  
  - Fancyapps UI  
  - Leaflet Maps  
  - Material-UI Components  
  - XLSX (Excel processing)  
  - Puppeteer (PDF generation)  

## Backend / CMS

• **CMS:** WordPress (Headless)  
• **API:** WordPress REST API  
• **Custom Fields:** Advanced Custom Fields (ACF)  
• **File Management:** WordPress media library  
• **External Integrations:** SharePoint document processing  

📁 **PROJECT STRUCTURE**

```
/component               # Reusable React components
  /areaTemplates        # Location-specific templates
  /button               # Button components
  /form                 # Form components (Contact, Career, Newsletter)
  /functions            # Utility functions
/pages                  # Next.js pages
  /all-around          # Location services pages
  /areas               # Area-specific pages
  /news                # News/articles pages
  /api                 # API routes
/styles                 # SCSS files
  /_base               # Base styles and mixins
  globals.css          # Global styles
  rtl.module.scss      # Arabic RTL styles
/public                 # Static assets
  /images              # Image assets
/lib                    # API utility functions
  /api                 # WordPress API calls
```

## Key Pages

1. **Home Page** (/)
2. **About Us** (/about-us)
3. **Contact Us** (/contact-us)
4. **Areas** (/areas)
5. **All Around Services** (/all-around)
6. **News** (/news)
7. **Careers** (/careers)
8. **CSR** (/csr)
9. **Start Campaign** (/start-your-campaign)
10. **Privacy Policy** (/privacy-policy)
11. **Cookie Settings** (/cookies-settings)

⚙️ **WORDPRESS CONFIGURATION**

## Custom Post Types:
• **News/Articles**  
• **Locations**  
• **All-Around Services**  
• **Areas**  
• **Careers**  

## API Endpoints Used:
• `/wp-json/wp/v2/pages`  
• `/wp-json/wp/v2/news`  
• `/wp-json/wp/v2/locations`  
• `/wp-json/wp/v2/all-around`  
• `/wp-json/wp/v2/areas`  
• `/wp-json/wp/v2/careers`  
• Custom endpoints for option fields  
• Custom endpoints for map data import  

🚀 **FEATURES IMPLEMENTED**

## 1. Multilingual Support (English/Arabic)
   - RTL layout for Arabic
   - Language switcher
   - Locale-based content fetching
   - Arabic font integration

## 2. Interactive Map System
   - Google Maps integration
   - Multi-level location browsing (Country → City → Locations)
   - Custom map markers and info windows
   - Location-based filtering
   - Coordinate management system

## 3. Content Management
   - Dynamic page content via WordPress
   - News/articles with categories
   - Career listings with application forms
   - Location-based content management
   - Image galleries and sliders

## 4. Forms & Interactions
   - Contact forms with validation
   - Newsletter subscription
   - Career application forms
   - Request quotation forms
   - File upload functionality

## 5. Campaign Management
   - Campaign preview system
   - Date range selection
   - Location-based campaign planning
   - Interactive campaign builder

## Design Features:
• Responsive design for all devices  
• Smooth scrolling and animations  
• Interactive counters and statistics  
• Image galleries and sliders  
• Custom loading states  
• Advanced SCSS architecture with mixins  

🔧 **CONFIGURATION FILES**

## Environment Variables Required:
```
NEXT_PUBLIC_API_BASE_URL=https://alarabia-newapp.e8demo.com/wp-json/wp/v2
NEXT_PUBLIC_CONTACT_API_URL=https://alarabia-newapp.e8demo.com/wp-json/contact-form-7/v1/contact-forms
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=[Google Maps API Key]
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

## Next.js Configuration (next.config.js):
• Image optimization for WordPress media  
• i18n configuration for English/Arabic  
• Remote image patterns for WordPress uploads  
• Disabled powered by header for security  

📦 **DEPLOYMENT INSTRUCTIONS**

## Prerequisites:
• Node.js 18+ installed  
• WordPress backend configured and accessible  
• Google Maps API key configured  
• Environment variables set  

## Build Process:
```bash
npm install
npm run build
npm start
```

## Development:
```bash
npm run dev
```

## Sitemap Generation:
```bash
npm run sitemap
```

🛡️ **MAINTENANCE GUIDELINES**

## Regular Updates:
1. **Dependencies:** Update Next.js and React regularly  
2. **WordPress:** Keep WordPress core and plugins updated  
3. **Security:** Monitor for security vulnerabilities  
4. **Performance:** Regular performance audits  
5. **Maps:** Monitor Google Maps API usage and costs  

## Content Management:
• All content managed through WordPress admin  
• ACF fields for structured content  
• Media library for image management  
• Form submissions tracked in WordPress  
• Location data managed through custom endpoints  

## Backup Strategy:
• Regular WordPress database backups  
• Frontend code repository backups  
• Media file backups  
• Environment configuration backups  

🔐 **ACCESS CREDENTIALS**

## WordPress Admin Access:
2. ADMIN : https://alarabia-newapp.e8demo.com/wp-admin
    <EMAIL>
    ebapp#$@$asdadalications

## Development URLs:
**Local Development:** http://localhost:3000/  
**Staging:** https://al-arabia-stage.vercel.app/  

FTP
URL: https://alarabia-newapp.e8demo.com
SFTP
----
HOST:     ************
USERNAME:  arabia-new
PASSWORD:  7dIwhS0MB2>swNH9#8rcbBA7HDbeiY0F

DATABASE
-------- 
NAME:  alarabiadb
USERNAME: 	alarabiauser
PASSWORD: 	OHptK3+Y0g0F^SYb,2cEt}.GLfiqh+3:P_zkqcl4:k*RFswsR#

📂 **DEPLOYMENT DETAILS**

## Repository Information:
**Repository URL:** [To be provided]  
**Branch:** main/development  
**Deployment Platform:** Vercel  

## Database Information:
**WordPress Database:** [To be provided]  
**Backup Schedule:** [To be configured]  

🔧 **SPECIAL FEATURES**

## SharePoint Integration:
• Excel file processing from SharePoint  
• Automatic coordinate conversion  
• Location data synchronization  

## Map Data Management:
• Dynamic location import system  
• Geocoding integration  
• Multi-language location support  
• Custom marker system  

## Performance Optimizations:
• Image optimization with Next.js  
• Static site generation (SSG)  
• API response caching  
• Lazy loading implementation  

📋 **MAINTENANCE CHECKLIST**

### Weekly:
- [ ] Check website functionality  
- [ ] Monitor form submissions  
- [ ] Review Google Maps API usage  

### Monthly:
- [ ] Update dependencies  
- [ ] WordPress security updates  
- [ ] Performance audit  
- [ ] Backup verification  

### Quarterly:
- [ ] Full security audit  
- [ ] Content review and optimization  
- [ ] SEO performance review  
- [ ] User experience testing  

---

**Note:** This document should be updated as the project evolves. Keep all credentials secure and update them regularly for security purposes.
