import React from 'react';
import { fetchByPost } from "@/lib/api/pageApi";
import KingKhaledInternationalAirport from '@/component/areaTemplates/king-khaled-international-airport';
import Altanfeethi from '@/component/areaTemplates/altanfeethi';
import Terminals from '@/component/areaTemplates/terminals';
import Accessroad from '@/component/areaTemplates/access-road';
import Tuwaiq from '@/component/areaTemplates/tuwaiq';

const Index = ({ pageData }) => {
  return (
    <>
      {pageData?.template === "airport1.php" ? (
        <KingKhaledInternationalAirport />
      ) : pageData?.template === "airport2.php" ? (
        <Altanfeethi />
      ) : pageData?.template === "airportAllterminal.php" ? (
        <Terminals />
      ) : pageData?.template === "template2.php" ? (
        <Accessroad />
      ) : pageData?.template === "tuwaiq.php" ? (
        <Tuwaiq />
      ) : (
        <p>Page not found or template missing.</p>
      )}
    </>
  );
};

export default Index;

export async function getServerSideProps(context) {

  const { params, locale } = context;  
  const slug = params.slug; 
  const langCode = locale === "ar" ? "ar" : "en";

  try {
    const pageData = await fetchByPost('areas',slug,langCode); 
    if (!pageData) {
      return { notFound: true }; 
  }

    return {
      props: {
          pageData
      },
    };
  } catch (error) {
    console.error('Error fetching data:', error);
    return {
      props: {
          pageData: null, 
      },
    };
  }
}