import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import Image from "next/image";
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import comon from "@/styles/comon.module.scss";
import pro from "@/styles/projectSliderNew.module.scss";
import { Pagination } from 'swiper/modules';

const ProjectSliderNewV1 = () => {
  const slides = [
    { id: 1, src: "/images/project-slide1.png", alt: "Slide 1" },
    { id: 2, src: "/images/project-slide2.png", alt: "Slide 2" },
    { id: 3, src: "/images/project-slide3.png", alt: "Slide 3" },
    { id: 4, src: "/images/project-slide4.png", alt: "Slide 4" },
    { id: 5, src: "/images/project-slide5.png", alt: "Slide 5" }
  ];

  return (
    <div className={comon.wrap}>
      <div className="slider_secton slider_secton_new new_slider">
        <Swiper
          slidesPerView={"auto"}
          centeredSlides={true}
          loop={true}
          spaceBetween={20}
          pagination={{ clickable: true }}
          modules={[Pagination]}
          className="mySwiper"
        >
          {slides.map((slide) => (
            <SwiperSlide key={slide.id}>
              <div className="slide-content">
                <Image src={slide.src} alt={slide.alt} width={500} height={300} quality={100} />
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
};

export default ProjectSliderNewV1;
