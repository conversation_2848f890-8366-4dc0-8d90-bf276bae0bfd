@import "variable", "base", "mixin";



.footer {
  background: linear-gradient(107.56deg,
      rgba(103, 90, 167, 1) 0%,
      rgba(58, 48, 100, 1) 100%);
  color: #fff;

  .footer_cl_01 {
    width: 46.3%;

    @media #{$media-700} {
      width: 100%;
      border-bottom: solid 1px #64559b;
      padding-bottom: 25px;
      margin-bottom: 25px;
    }







  }

  .footer_cl_02 {
    width: 53.7%;

    @media #{$media-700} {
      width: 100%;
    }
  }

  p {
    color: #ece8ff;
    font-family: var(--aller_lt);
    @include rem(12);


    @media #{$media-700} {
      font-size: 14px;
    }
  }

  .social_ul {
    margin: 0 -3%;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    margin-top: 30px;

    li {
      list-style: none;
      padding: 0 3%;

      a {
        display: inline-flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: #4c3d83;
        }
      }
    }

    @media #{$media-700} {
      margin-top: 20px;
    }
  }

  .footer_ul {
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;

    li {
      list-style: none;

      h5 {
        @include rem(15);
        font-family: var(--aller_bd);
        margin-bottom: 15px;
        font-weight: normal;

        @media #{$media-700} {
          margin-bottom: 10px;
          font-size: 16px;
        }

      }

      a {
        font-size: 14px;
        font-family: var(--aller_lt);

        @media #{$media-700} {
          font-size: 14px;
        }
      }

      .footer_link {
        li {
          margin-bottom: 10px;

          @media #{$media-700} {
            margin-bottom: 7px;
          }
        }
      }

      p {
        line-height: 25px;
        @include rem(14);

        @media #{$media-700} {
          font-size: 14px;
        }
      }
    }



    .footer_link_cl_01 {
      width: 32%;

      @media #{$media-700} {
        width: 50%;
      }
    }

    .footer_link_cl_02 {
      width: 32%;

      @media #{$media-700} {
        width: 50%;
      }
    }

    .footer_link_cl_03 {
      width: 36%;

      @media #{$media-700} {
        width: 100%;
        border-top: solid 1px #64559b;
        padding-top: 25px;
        margin-top: 25px;
      }
    }
  }

  .copy_block {
    border-top: solid 1px rgba($color: #ffffff, $alpha: 0.15);
    margin-top: 25px;
    padding-top: 20px;
    padding-bottom: 20px;
  }


  .footer_bot_link_ul_outer {
    .footer_bot_link_ul {
      display: flex;
      flex-wrap: wrap;
      margin: 0;

      li {
        padding-left: 20px;
        padding-right: 20px;

        a {
          color: #fff;
          font-family: var(--aller_lt);
          @include rem(12);

          &:hover {
            color: #ccbeff;
          }
        }

        @media #{$media-768} {
          padding-left: 3%;
          padding-right: 3%;
        }

      }

      @media #{$media-768} {
        margin: 0
      }

    }
  }


  .footer_bot_link_outer {

    border-top: solid 1px rgba($color: #fff, $alpha: 0.15);
    padding-top: 20px;
    padding-bottom: 20px;
    margin-top: 15px;

    .footer_bot_link_outer_bot {
      width: 53.5%;

      .footer_bot_link_ul {
        display: flex;
        flex-wrap: wrap;

        li {
          width: 32%;

          a {
            @include rem(12);

            @media #{$media-700} {
              font-size: 14px;
            }
          }

          @media #{$media-700} {
            width: 50%;
            padding-bottom: 7px;
          }
        }
      }

      @media #{$media-700} {
        width: 100%;
      }
    }


    .col_rew {
      flex-direction: column-reverse;
    }

  }
}