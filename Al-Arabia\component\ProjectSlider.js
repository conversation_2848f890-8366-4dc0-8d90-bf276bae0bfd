import React, { useRef, useState } from 'react';
// Import Swiper React components
import { Swiper, SwiperSlide } from 'swiper/react';

import Image from "next/image";
// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';


import comon from "@/styles/comon.module.scss";
import pro from "@/styles/projectSlider.module.scss";


import { Autoplay, Pagination, Navigation } from 'swiper/modules';


const ProjectSlider = () => {




  const slides = [
    { id: 1, src: "/images/project-slide1.png", alt: "Slide 1" },
    { id: 2, src: "/images/project-slide2.png", alt: "Slide 2" },
    { id: 3, src: "/images/project-slide3.png", alt: "Slide 3" },
    { id: 4, src: "/images/project-slide4.png", alt: "Slide 4" },
    { id: 5, src: "/images/project-slide5.png", alt: "Slide 5" },

  ];




  return (

    <div className='slider_secton'>
      <Swiper
        spaceBetween={10}
        loop={true}
        slidesPerView={"auto"}
        centeredSlides={false}
        centerInsufficientSlides={true}
        autoplay={{
          delay: 2500,
          disableOnInteraction: false,
        }}
        speed={1200}
        pagination={false}
        navigation={false}
        modules={[Autoplay, Pagination, Navigation]}
        className="mySwiper Project_swiper"
      >
        {slides.map((slide) => (
          <SwiperSlide key={slide.id} className={`${pro.slide_block} slide_block`}>
            <div className={`${pro.slide_img}`}>
              <Image src={slide.src} width={274} height={242} alt={slide.alt} quality={100} />
            </div>
          </SwiperSlide>
        ))}
      </Swiper>;
    </div>
  );
};

export default ProjectSlider;