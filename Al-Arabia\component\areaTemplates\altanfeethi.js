"use client";
import React from "react";
import Link from "next/link";
import Image from "next/image";
import HeadMarquee from "@/component/HeadMarquee";
import comon from "@/styles/comon.module.scss";
import rtl from "@/styles/rtl.module.scss";
import style from "@/styles/altanfeethi.module.scss";
import SliderSectionDark from "@/component/SliderSectionDark";
import InnerBanner from "@/component/InnerBanner";
import ContactSection from "@/component/ContactSection";
import StartCampaign from "@/component/StartCampaign";
import HoverSlide from "@/component/HoverSlide";
import Head from "next/head";
import SliderBannerText from "@/component/SliderBannerText";
import { useRouter } from 'next/router';
import parse from 'html-react-parser';
const HoverSlideseason = [
	{
		title: "Lorem ipsum dolor",
		image: "/images/altanfeethi-gallerynew1.jpg",
	},
	{
		title: "Lorem ipsum dolor",
		image: "/images/altanfeethi-gallerynew2.jpg",
	},
	{
		title: "Lorem ipsum dolor",
		image: "/images/altanfeethi-gallerynew3.jpg",
	},
	{
		title: "Lorem ipsum dolor",
		image: "/images/altanfeethi-gallerynew4.jpg",
	},
	{
		title: "Lorem ipsum dolor",
		image: "/images/altanfeethi-gallerynew5.jpg",
	},
	{
		title: "Lorem ipsum dolor",
		image: "/images/altanfeethi-gallerynew6.jpg",
	},
];

// const imageSlides = cardData.sliderImg
const HoverSlideImage = [
	"/images/Imageblock_slide1.png",
	"/images/csr_2.jpg",
	"/images/Imageblock_slide1.png",
	"/images/Imageblock_slide1.png",
	"/images/Imageblock_slide1.png",
	"/images/Imageblock_slide1.png",
	"/images/Imageblock_slide1.png",
	"/images/Imageblock_slide1.png",
	"/images/Imageblock_slide1.png",
];

const Altanfeethi = ({ pageData }) => {
	const { locale, asPath } = useRouter();
	const lastSegment = asPath.split("/").filter(Boolean).pop();
	return (
		<div>
			{/* <Head>
				<title>Altanfeethi</title>
				<meta name="description" content="Generated by create next app" />
			</Head> */}
			<HeadMarquee />
			{pageData.acf.banner_details.description_field ? (

				<SliderBannerText
					title={pageData.acf.banner_details.title}
					paragraph={pageData.acf.banner_details.description_}
					details={pageData.acf.banner_details}
					className={'width_870'}
					bgColor={true}
					extra_height_class={true}
					full_height_banner={true}
					black_overlay={true}
				/>
			) : (
				<InnerBanner darkBackground={true} showLink={false} title={pageData.acf.banner_details.title} details={pageData.acf.banner_details} />
			)}
			<section className={`dark_bg ${comon.pb_60}`}>
				<div className={`${comon.wrap}  ${comon.pt_35}`}>
					<ul
						className={`${comon.mb_30} ${comon.detail_breadcrumb} ${rtl.detail_breadcrumb} ${comon.breadcrumb_white} `}
					>
						<li>
							<Link href={"/"}>
								<div className={` ${comon.bread_icon}  `}>
									<Image
										src={"/images/breadcumb_home_white.svg"}
										height={30}
										width={30}
										alt=""
									/>
								</div>
							</Link>
						</li>
						{pageData.parent_title && (
							<li>
								<Link href={`${pageData.parent_slug}`}>{pageData.parent_title}</Link>
							</li>
						)}

						<li>
							<a

								className={` ${comon.active}`}
							>
								{pageData.title.rendered}
							</a>
						</li>
					</ul>
				</div>
				<div
					className={`${comon.wrap} ${comon.d_flex_wrap} ${comon.d_flex_wrap} ${style.altanfeethi_banner_content}`}
				>
					<ul>
						<li data-aos="fade-in" data-aos-duration="1000">
							<>
								{pageData.acf.description.description_left && parse(pageData.acf.description.description_left)}
							</>
						</li>

						<li data-aos="fade-in" data-aos-duration="1000">
							<>
								{pageData.acf.description.description_right && parse(pageData.acf.description.description_right)}
							</>
						</li>
					</ul>
					{pageData.acf.description.image && (
					<div
						className={`${style.altanfeethi_image} ${comon.mt_40}`}
						data-aos="fade-up"
						data-aos-duration="1000"
					>
						<Image
							src={pageData.acf.description.image}
							width={1200}
							height={600}
							alt="banner"
						/>
					</div>
					)}
				</div>
			</section>

			<section className={`${comon.pb_65} ${comon.pt_65}`}>
				<div
					className={`${comon.wrap} ${comon.d_flex_wrap}  ${style.image_text_section}  `}
				>
					<div
						className={` ${style.image_block} `}
						data-aos="fade-in"
						data-aos-duration="1000"
					>
						<div className={` ${style.image_sec} `}>
							<Image
								src={pageData.acf.other_details.detail_image}
								height={700}
								width={700}
								alt=""
								quality={100}
							/>
						</div>
					</div>
					<div
						className={`  ${style.text_container} ${rtl.text_container} `}
						data-aos="fade-in"
						data-aos-duration="1000"
					>
						<div className={`  ${style.text_block} `}>
							<p>
								{pageData.acf.other_details.description}
							</p>

							<ul className={`  ${style.img_icon_block} ${comon.mt_40} `}>
								{pageData.acf.other_details.image_list && pageData.acf.other_details.image_list.map((item, index) => {
									return (
										<li data-aos="fade-in" data-aos-duration="1000" key={index}>
											<div className={`  ${style.img_icon}   `}  >
												<Image
													src={item.image}
													height={200}
													width={200}
													alt=""
													quality={100}
												/>
											</div>
											<div className={`${style.desc_block}   `}>
												<p>{item.title && parse(item.title)}</p>
											</div>
										</li>
									)
								})}
							</ul>
						</div>
					</div>
				</div>
			</section>

			<section
				className={`${comon.pb_65} ${comon.pt_65}  ${style.pt_overview_hiddeen}  dark_bg`}
			>
				<div className={`${comon.wrap} ${comon.d_flex_wrap}`}>
					<div
						className={`${comon.title_30} ${comon.w_100} ${comon.white_color}`}

					>
						<h3>{parse(pageData.acf.gallery_details.gallery_title)}</h3>
					</div>
				</div>

				<div  >
					<HoverSlide
						season={pageData.acf.gallery_details.gallery}
						// imageSlides={HoverSlideImage}
						popUpAction={false}
						// extra_expand={true}
						extra_width_arrows={true}
						black_bg_arrows={true}

					/>
				</div>
			</section>

			<StartCampaign bgColor={"#ECECEC"} darkColor={true} plainBlack={true} />

			<section
				className={`${comon.pt_60} ${comon.pb_10} ${comon.black_bg} `}
				style={{ backgroundImage: "none" }}
			>
				<ContactSection
					title="Request for Quotation"
					paragraph="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore."
					button="Request a Quote"
					showAutoMargin={false}
					className="white_text"
					classNameSecond="form_white"
					whiteBtn={true}
				/>
			</section>
		</div>
	);
};

export default Altanfeethi;
