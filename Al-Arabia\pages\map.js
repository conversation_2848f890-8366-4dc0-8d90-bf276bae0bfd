import React, { useState, useEffect } from "react";
import AOS from "aos";
import "aos/dist/aos.css";
import Map from "@/component/contactmap";
import comon from "@/styles/comon.module.scss";
import { useRouter } from "next/router";

const API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
const geocodeCache = {};

async function geocodeLocation(location) {
  if (geocodeCache[location]) return geocodeCache[location];

  const response = await fetch(
    `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(
      location
    )}&key=${API_KEY}`
  );
  if (!response.ok) throw new Error(`Failed to fetch geocode for ${location}`);
  const data = await response.json();
  if (data.status === "OK" && data.results.length > 0) {
    const { lat, lng } = data.results[0].geometry.location;
    geocodeCache[location] = { lat, lng };
    return { lat, lng };
  } else {
    console.warn(`No geocode results for ${location}`);
    return { lat: null, lng: null };
  }
}

function cleanCityName(name) {
  return name.replace(/–.*$/, "").replace(/-.*$/, "").trim();
}

async function rearrangeDataToCountryCityArrayWithGeocoding(data) {
  const result = [];

  for (const [product, countries] of Object.entries(data)) {
    for (const [countryRaw, cities] of Object.entries(countries)) {
      const country = countryRaw.trim();
      let countryEntry = result.find((c) => c.country.name === country);

      if (!countryEntry) {
        const countryCoords = await geocodeLocation(country);
        countryEntry = {
          country: {
            name: country,
            lat: countryCoords.lat,
            long: countryCoords.lng,
            product_icon_c: false,
          },
          cities: [],
        };
        result.push(countryEntry);
      }

      for (const [cityRaw, coordsArray] of Object.entries(cities)) {
        const cleanedCity = cleanCityName(cityRaw.trim());
        let cityEntry = countryEntry.cities.find(
          (c) => c.city_name === cleanedCity
        );

        if (!cityEntry) {
          const cityCoords = await geocodeLocation(
            `${cleanedCity}, ${country}`
          );
          cityEntry = {
            city_name: cleanedCity,
            lat: cityCoords.lat,
            long: cityCoords.lng,
            product_icon: false,
            co_ords1: false,
            co_ords: [],
          };
          countryEntry.cities.push(cityEntry);
        }

        coordsArray.forEach((coord) => {
          cityEntry.co_ords.push({
            lat: coord.latitude,
            long: coord.longitude,
            name: product,
            icon: null,
          });
        });
      }
    }
  }

  return result;
}

export default function Home({ pageData }) {
  const { locale } = useRouter();
  const [isClient, setIsClient] = useState(false);
  const [rearrangedData, setRearrangedData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  const getKey = (row, en, ar) => (locale === "ar" ? row[ar] : row[en]);

  useEffect(() => {
    setIsClient(true);
    AOS.init();
  }, []);

  useEffect(() => {
    async function fetchData() {
      try {
        const response = await fetch("/api/readExcel");
        const json = await response.json();
        const rows = json.data;

        const newStructuredData = {};

        await Promise.all(
          rows.map(async (row) => {
            const product = getKey(row, "Product", "المنتج");
            const country = getKey(row, "Country", "الدولة");
            const city = getKey(row, "City", "المدينة");
            let locationLink = row["Location"];

            if (!locationLink) return;

            try {
              const mid = new URL(locationLink).searchParams.get("mid");
              if (!mid) return;
              locationLink = `https://www.google.com/maps/d/kml?mid=${mid}`;
            } catch {
              return;
            }

            const kmlRes = await fetch(
              `/api/locations?kmlUrl=${encodeURIComponent(locationLink)}`
            );
            if (!kmlRes.ok) return;
            const kmlData = await kmlRes.json();

            if (!kmlData.coordinates?.length) return;

            newStructuredData[product] ??= {};
            newStructuredData[product][country] ??= {};
            newStructuredData[product][country][city] = kmlData.coordinates;
          })
        );

        const structured = await rearrangeDataToCountryCityArrayWithGeocoding(
          newStructuredData
        );
        setRearrangedData(structured);
      } catch (err) {
        console.error("Error:", err);
        setError("An error occurred while loading map data.");
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [locale]);

  // ⬇ Send data to WordPress when rearrangedData is ready
  useEffect(() => {
    const importMap = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/importMap`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              locations: rearrangedData,
              locale: locale,
            }),
          }
        );

        const data = await response.json();
        console.log("Import map response:", data);
      } catch (error) {
        console.error("Error posting importMap:", error);
      }
    };

    if (rearrangedData?.length > 0) {
      importMap();
    }
  }, [rearrangedData, locale]);

  if (!isClient) return null;

  return (
    <section className={comon.pt_50}>
      <div>
        <div style={{ padding: "100px" }}>
          {loading ? (
            <p role="status">Loading map data...</p>
          ) : error ? (
            <p role="alert" style={{ color: "red" }}>
              {error}
            </p>
          ) : rearrangedData ? (
            <Map
              mapDetails={pageData?.acf?.location_details || ""}
              textBlockShow={false}
              locationList={rearrangedData}
            />
          ) : (
            <p>No map data available.</p>
          )}
        </div>
      </div>
    </section>
  );
}

export async function getServerSideProps() {
  return {
    props: { pageData: null }, // Replace with real data if needed
  };
}
