import { parseStringPromise } from 'xml2js';

export async function GET() {
  try {
    const res = await fetch(
      'https://www.google.com/maps/d/kml?mid=1p-9eFSqpgq8Yeq7Uk_MAiI37uyJuMFE'
    );

    if (!res.ok) {
      return new Response(JSON.stringify({ error: 'Failed to fetch KML' }), { status: 500 });
    }

    const kmlText = await res.text();
    const result = await parseStringPromise(kmlText);

    // Check structure by logging
    console.log('KML Parsed:', JSON.stringify(result, null, 2));

    const placemarks = result?.kml?.Document?.[0]?.Folder?.[0]?.Placemark ?? [];

    const locations = placemarks.map((place) => ({
      name: place.name?.[0] || 'Unnamed',
      coordinates: place.Point?.[0]?.coordinates?.[0]
        ?.trim()
        .split(',')
        .slice(0, 2)
        .map(Number),
    })).filter(loc => loc.coordinates); // Filter out any invalid ones

    return Response.json(locations);
  } catch (err) {
    console.error('Error:', err);
    return new Response(JSON.stringify({ error: err.message }), { status: 500 });
  }
}
