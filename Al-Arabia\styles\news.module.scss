@import "variable", "base", "mixin";

.news_section {
    width: 100%;

    .swiper_content {
        width: 100%;
        display: flex;
        flex-wrap: wrap;

        font-family: var(--aller_rg);

        .swiper_section {
            // width: 50%;
            width: 53%;
            padding-right: 5%;
            padding-bottom: 30px;
            // max-height: 370px;

            .breadcrumb {
                display: flex;
                list-style: none;
                margin-top: 15px;

                li {
                    span {
                        display: block;
                        padding: 1px 15px;
                        border-right: 2px solid rgba(59, 48, 100, 0.377);

                        color: rgba(59, 48, 100, 0.7);
                        text-decoration: underline;
                        font-size: 15px;
                        font-weight: 400;
                        line-height: 100%;

                        @media #{$media-820} {
                            padding: 0px 10px;
                            font-size: 13px;
                        }
                    }

                    &:first-child span {
                        padding-left: 0;
                    }

                    &:last-child span {
                        font-style: italic;
                        text-decoration: none;

                        border-right: 0px solid rgba(59, 48, 100, 0.377);
                    }
                }
            }

            h4 {
                color: #3b3064;
                // font-size: 25px;
                font-size: 1.5625rem;
                // line-height: 35px;
                font-weight: 700;
                margin-bottom: 20px;
                // display: -webkit-box;
                // -webkit-line-clamp: 3;
                // -webkit-box-orient: vertical;
                // overflow: hidden;
                font-family: "Aller", sans-serif;

                // &:hover {
                //     color: #685f8d;
                // }





                @media #{$media-820} {
                    margin-bottom: 15px;
                }

                @media #{$media-600} {
                    font-size: 16px;

                    margin-bottom: 10px;
                }
            }

            p {
                color: #3b3064;
                font-size: 15px;
                line-height: 24px;
                font-weight: 400;
                margin-bottom: 20px;
                max-width: 555px;
                width: 100%;


                @media #{$media-820} {
                    font-size: 14px;
                }

                @media #{$media-700} {
                    font-size: 13px;
                    line-height: 20px;
                }
            }

            .link {
                display: block;
                color: #3b3064;
                font-size: 16px;
                font-weight: 400;
                line-height: 100%;
                margin-bottom: 40px;
                text-decoration: underline;

                &:hover {
                    color: rgba(59, 48, 100, 0.7);
                }

                @media #{$media-700} {
                    margin-bottom: 20px;

                }

                @media #{$media-700} {
                    font-size: 13px;
                }
            }

            @media #{$media-700} {
                width: 100%;
                padding-right: 0%;
                padding-bottom: 10px;

            }
        }
    }

    .img_section {
        // width: 50%;
        width: 47%;

        >img {
            height: auto;
            width: 100%;
            display: block;
            object-fit: contain;
        }

        &.img_cover_sec {
            >img {
                height: 100%;
                object-fit: cover;
            }
        }

        @media #{$media-700} {
            width: 100%;
            height: 300px;

            >img {
                height: 100%;
                width: 100%;
                object-position: top;
                object-fit: cover;
            }
        }
    }
}

.list_section {
    width: 100%;
    font-family: var(--aller_rg);

    .ul_section {
        width: 100%;

        >li {
            width: 100%;
            list-style: none;
            position: relative;
            padding: 22.5px 0;
            border-bottom: 1px solid #c9c6d1;
            transition: all 0.3s ease-in-out;

            &:first-child {
                border-top: 1px solid #c9c6d1;
            }

            &:hover {
                background-color: rgba(191, 180, 231, 0.295);

                @media #{$media-700} {
                    background: transparent;
                }

                &::after {
                    @media #{$media-700} {
                        background: rgba(191, 180, 231, 0.295);
                    }
                }
            }

            .ul_content {
                display: flex;
                flex-wrap: wrap;

                li {
                    width: 81%;

                    &:first-child {
                        width: 19%;

                        @media #{$media-1024} {
                            width: 25% !important;
                        }

                        @media #{$media-700} {
                            width: 50% !important;
                        }
                    }

                    span {
                        color: rgba(59, 48, 100, 0.7);
                        font-style: italic;
                        transition: all 0.3s ease-in-out;
                        font-size: 15px;
                        display: block;
                        font-weight: 500;
                        padding-right: 10px;
                        display: inline-block;
                        // width: fit-content;


                        @media #{$media-1200} {
                            font-size: 14px;
                        }

                        @media #{$media-700} {
                            padding-right: 0px;
                            font-size: 13px;
                        }

                    }

                    p {
                        color: #3b3064;
                        font-size: 15px;
                        transition: all 0.3s ease-in-out;

                        line-height: 24px;
                        font-weight: 500;
                        max-width: 750px;
                        width: 95%;

                        >span {
                            padding: 0;
                        }

                        @media #{$media-1200} {
                            line-height: unset;
                            font-size: 14px;
                        }

                        @media #{$media-700} {
                            max-width: unset;
                            width: 100%;
                            font-size: 13px;
                        }

                        @media #{$media-600} {
                            margin-top: 8px;
                        }
                    }

                    @media #{$media-1024} {
                        width: 75% !important;
                    }

                    @media #{$media-600} {
                        width: 100% !important;
                    }
                }
            }

            @media #{$media-1200} {
                padding: 15px 0;
            }

            @media #{$media-600} {
                padding: 10px 0;
            }

            &::after {
                position: absolute;
                left: -3%;
                right: -3%;
                height: 100%;
                top: 0;
                content: "";
                display: block;
                z-index: -1;
            }
        }
    }
}

// .ul_section li:hover .ul_content li span {
.ul_section li:hover .ul_content li>span {
    transform: translateX(10px);

    @media #{$media-700} {
        transform: translateX(0px);
    }
}

.ul_section li:hover .ul_content li p {
    transform: translateX(-10px);

    @media #{$media-700} {
        transform: translateX(0px);
    }
}

// -----news detail section-------

.detail_section {
    padding-top: 170px;
    background: #ececec;

    .detail_container {
        .share_section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;

            .btn_section {
                display: flex;
                gap: 20px;

                a {
                    border: 1px solid #675aa7;
                    display: flex;
                    align-items: center;
                    min-height: 30px;
                    padding: 0px 9.5px;
                    background: #e2dfed;
                    color: rgba(59, 48, 100, 0.9);
                    font-size: 14px;

                    &:hover {
                        background: #f3efff;
                    }

                    @media #{$media-1024} {
                        min-height: unset;
                        padding: 5px 10px;
                    }

                    @media #{$media-700} {
                        font-size: 13px;
                    }

                    @media #{$media-400} {
                        font-size: 12px;
                    }
                }

                @media #{$media-1024} {
                    gap: 10px;
                }
            }

            .share_icon_sec {
                display: flex;
                align-items: center;
                gap: 32px;

                li {
                    span {
                        color: rgba(59, 48, 100, 0.9);
                        font-size: 14px;
                        font-weight: 400;
                    }

                    .icon {
                        height: 20px;
                        width: 20px;
                        display: block;

                        >img {
                            height: 100%;
                            width: 100%;
                            object-fit: contain;
                            display: block;
                        }

                        @media #{$media-1024} {
                            height: 18px;
                            width: 18px;
                        }

                        @media #{$media-600} {
                            height: 14px;
                            width: 14px;
                        }
                    }
                }

                @media #{$media-1366} {
                    gap: 25px;
                }

                @media #{$media-1024} {
                    gap: 15px;
                }

                @media #{$media-600} {
                    gap: 10px;
                }

                @media #{$media-400} {
                    gap: 7px;
                }
            }

            @media #{$media-1024} {
                margin-top: 15px;
            }
        }

        .content_section {
            h2 {
                color: #3b3064;
                // font-size: 30px;
                font-size: 1.875rem;
                line-height: 35px;
                font-weight: 700;
                margin-top: 35px;

                @media #{$media-1440} {
                    margin-top: 30px;
                }

                @media #{$media-1024} {
                    margin-top: 20px;
                }

                @media #{$media-820} {
                    line-height: 140%;
                    font-size: 1.6rem;
                }
            }

            p {
                color: #3b3064;
                font-size: 15px;
                line-height: 24px;
                font-weight: 400;
                margin-top: 35px;

                @media #{$media-1440} {
                    margin-top: 30px;
                }

                @media #{$media-1024} {
                    margin-top: 15px;
                    font-size: 14px;

                    >br:first-child {
                        display: none;
                    }
                }

                @media #{$media-600} {
                    margin-top: 10px;
                    font-size: 13px;
                    line-height: 21px;
                }

                img {
                    max-width: 100%;
                    height: auto;
                }

            }

            iframe {
                height: 600px;
                width: 100%;

                @media #{$media-700} {
                    height: 500px;

                }

                @media #{$media-500} {
                    height: 400px;

                }
            }



            .detail_img {
                height: auto;
                width: 100%;
                margin-top: 35px;

                img {
                    object-fit: contain;

                    height: auto;
                    width: 100%;
                    display: block;
                }

                @media #{$media-1440} {
                    margin-top: 30px;
                }

                @media #{$media-1024} {
                    margin-top: 15px;
                }
            }

            .like_section {
                display: flex;
                align-items: center;
                margin-top: 35px;
                margin-bottom: 35px;

                li {
                    display: flex;
                    align-items: center;
                    padding: 0 25px;
                    gap: 10px;
                    position: relative;

                    &::after {
                        position: absolute;
                        content: "";

                        border-radius: 50%;
                        top: 50%;
                        left: -3px;
                        transform: translateY(-50%);
                        background: #6758b6;

                        width: 7px;
                        height: 7px;

                        @media #{$media-600} {
                            width: 4px;
                            height: 4px;
                        }
                    }

                    &:first-child {
                        padding-left: 0;

                        &::after {
                            display: none;
                        }
                    }

                    span {
                        color: rgba(59, 48, 100, 0.9);
                        font-size: 14px;
                        font-weight: 400;
                        display: flex;
                        align-items: center;
                        gap: 2px;

                        @media #{$media-600} {
                            font-size: 13px;
                        }
                    }

                    .icon_img {
                        height: auto;
                        width: 22px;

                        >img {
                            height: auto;
                            width: 100%;
                            display: block;
                            object-fit: contain;
                        }

                        &.max_width {
                            width: 17px;

                            @media #{$media-1024} {
                                width: 15px;
                            }

                            @media #{$media-600} {
                                width: 14px;
                            }
                        }

                        @media #{$media-1024} {
                            width: 18px;
                        }

                        @media #{$media-600} {
                            width: 16px;
                        }
                    }

                    @media #{$media-1024} {
                        padding: 0 15px;
                    }

                    @media #{$media-600} {
                        padding: 0 10px;
                        gap: 5px;
                    }
                }

                @media #{$media-1440} {
                    margin-top: 30px;
                }

                @media #{$media-1024} {
                    margin-top: 20px;
                }
            }
        }
    }

    @media #{$media-1440} {
        padding-top: 150px;
    }

    @media #{$media-700} {
        // padding-top: 85px;
        padding-top: 115px;
    }

    @media #{$media-500} {
        padding-top: 95px;
        // padding-top: 115px;
    }
}


:global(body.rtl) .detail_section .detail_container .content_section .like_section li:first-child {
    padding-left: 25px;
    padding-right: 0;
}