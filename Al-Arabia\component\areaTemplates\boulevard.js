import React, { useState, useEffect, useRef } from "react";
import Head from "next/head";
import Link from "next/link";
// ---buttion-import--start
import But<PERSON> from "@/component/button/Buttons";
import Marquee from "@/component/Marquee";
// ---buttion-import--end
import ContactSection from "@/component/ContactSection";
import Image from "next/image";
import InnerBanner from "@/component/InnerBanner";
import rtl from "@/styles/rtl.module.scss";
import Parallax from "@/component/Paralax";
import Map from "@/component/contactmap";
// Import Swiper styles
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/scrollbar";
import {
    Scrollbar,
    Autoplay,
    Navigation,
    Pagination,
    FreeMode,
    Thumbs,
} from "swiper/modules";
import comon from "@/styles/comon.module.scss";
import boulevard from "@/styles/boulevard.module.scss";
import buttion from "@/styles/buttion.module.scss";
import DynamicImage from "@/component/DynamicImage";
import AOS from "aos";
import "aos/dist/aos.css";
import HeadMarquee from "@/component/HeadMarquee";
import CountdownTimer from "@/component/CountdownTimer";
import FlipClockCountdown from "@leenguyen/react-flip-clock-countdown";
import "@leenguyen/react-flip-clock-countdown/dist/index.css";
import StartCampaign from "@/component/StartCampaign";
import Modal from "@mui/material/Modal";
import Box from "@mui/material/Box";
import HoverSlide from "@/component/HoverSlide";
import parse from 'html-react-parser';
import SliderBannerText from "@/component/SliderBannerText";
import { fetchByPost } from "@/lib/api/pageApi";
import { useRouter } from "next/router";
import contactF from "@/styles/contactForm.module.scss";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import RequestSeasonCard from "@/component/form/RequestSeasonCard";
import { usePathname } from "next/navigation";

export default function Projecboulevard({ pageData }) {

    const [dropdown, setDropdown] = useState("1 week");
    const { locale } = useRouter();
    const handleChange = (event) => {
        setDropdown(event.target.value);
    };

    const [open, setOpen] = useState(false);
    const handleOpen = () => {
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
    };
    const [thumbsSwiper, setThumbsSwiper] = useState(null);

    // const [thumbsSwiper, setThumbsSwiper] = useState();

    const [isMobile, setIsMobile] = useState(false);

    const videoRef = useRef(null);
    const [isPlaying, setIsPlaying] = useState(false);

    const togglePlayPause = () => {
        if (videoRef.current) {
            console.log("videoRef.current:", videoRef.current);
            if (videoRef.current.paused) {
                videoRef.current.play();
                setIsPlaying(true);
            } else {
                videoRef.current.pause();
                setIsPlaying(false);
            }
        }
    };

    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth < 768);
        };

        handleResize(); // Check on mount
        window.addEventListener("resize", handleResize);

        return () => window.removeEventListener("resize", handleResize);
    }, []);

    const [isClient, setIsClient] = useState(false);

    // Initialize AOS and set client-side state after the component mounts
    useEffect(() => {
        setIsClient(true);
        AOS.init();
    }, []);

    const router = useRouter();

    useEffect(() => {
        if (!router.isReady) return; // Ensure router is ready

        // Run the effect after a small delay to allow re-renders
        setTimeout(() => {
            const offset = 50;
            const targetElement = document.getElementById("riyadh_season_product");

            if (router.query.id === "product" && targetElement) {
                const topPosition = targetElement.getBoundingClientRect().top + window.scrollY;
                window.scrollTo({ top: topPosition - offset, behavior: "smooth" });
            } else {
                console.log("Target element not found or incorrect query:", router.query);
            }
        }, 300);
    }, [router.query.id, router.isReady]);

    const pathname = usePathname();

    useEffect(() => {
        const hash = window.location.hash;

        if (hash) {
            const scrollToTarget = () => {
                const el = document.querySelector(hash);
                if (el) {
                    el.scrollIntoView({ behavior: "smooth" });
                } else {
                    // Try again if not found yet (element rendered late)
                    setTimeout(scrollToTarget, 100);
                }
            };

            // Delay first try
            setTimeout(scrollToTarget, 100);
        }
    }, [pathname]);



    const formattedContent = pageData.acf.description.slider_details_.map((item) => {
        if (!item.acf_fc_layout) return "";
        if (item.acf_fc_layout === "small_bold_text") {
            return `<b>${item.text_bold}</b>`;
        } else if (item.acf_fc_layout === "regular_big_text") {
            return item.text__reg;
        }
        return "";
    });

    const formattedContent1 = pageData.acf.latest_details.slider_details_.map((item) => {
        if (!item.acf_fc_layout) return "";
        if (item.acf_fc_layout === "small_bold_text") {
            return `<b>${item.text_bold}</b>`;
        } else if (item.acf_fc_layout === "regular_big_text") {
            return item.text__reg;
        }
        return "";
    });

    if (!isClient) {
        return null; // Prevent SSR mismatch by rendering nothing until the component is mounted on the client
    }

    return (
        <>
            {/* <Head>
                <title>Boulevard The Entertainment Hub</title>
                <meta name="description" content="Generated by create next app" />
            </Head> */}
            <HeadMarquee />
            {pageData.acf.banner_details.description_field ? (

                <SliderBannerText
                    title={pageData.acf.banner_details.title}
                    paragraph={pageData.acf.banner_details.description_}
                    details={pageData.acf.banner_details}
                    className={'width_870'}
                    bgColor={true}
                    extra_height_class={true}
                    full_height_banner={true}
                />
            ) : (
                <InnerBanner showLink={false} title={pageData.acf.banner_details.title} details={pageData.acf.banner_details} />
            )}

            <section>
                {pageData.acf.description.video && (
                    <video
                        className={`${comon.video_block} ${comon.video_min_height_section} video_block video_block_home`}
                        autoPlay
                        muted
                        loop
                        playsInline={true}
                        width="100%"
                        height="auto"
                    >
                        <source src={pageData.acf.description.video.url} type="video/mp4" />
                    </video>
                )}
            </section>

            <Marquee
                data-aos="fade-in"
                data-aos-duration="1000"
                content={formattedContent}
                // speed={100000}

                speed={9999}
                direction="left"
            // font_size20={true}
            />

            <section className={`${comon.pt_60} ${comon.pb_30}`}>
                <div className={`${comon.wrap} ${comon.d_flex_wrap}`}>
                    <div
                        data-aos="fade-in"
                        data-aos-duration="1000"
                        className={`${comon.w_50}`}
                    >
                        <div className={`${comon.title_30} ${comon.mob_title_new}`}>
                            <h3>
                                {parse(pageData.acf.details_t.title)}
                            </h3>
                        </div>
                    </div>
                    <div
                        data-aos="fade-in"
                        data-aos-duration="1000"
                        className={`${comon.w_50} ${comon.text_collor}`}
                    >
                        <p>
                            {parse(pageData.acf.details_t.description)}
                        </p>
                    </div>
                </div>
            </section>

            <section
                data-aos="fade-in"
                data-aos-duration="1000"
                className={`${comon.video_section_02}  ${comon.mob_video_hide} ${comon.p_relative}`}
            >
                {/* <div
                    className={`${comon.video_thumbnail}  ${
                        isPlaying == true ? comon.hide : ""
                    } `}
                >
                    <Image
                        className={`${comon.w_100} ${comon.img} ${comon.holder} slide_img`}
                        src={pageData.acf.details_t.thumbnail}
                        width={1000}
                        height={600}
                        quality={100}
                    />
                </div>
                <div
                    className={`${comon.video_play_but}  ${
                        isPlaying == true ? comon.play : ""
                    }`}
                    onClick={togglePlayPause}
                >
                    <Image
                        className={`${comon.w_100} ${comon.img} ${comon.holder} slide_img`}
                        src={"/images/play_icn.svg"}
                        width={17}
                        height={19}
                    />
                </div>

                <div
                    className={`${comon.boulevard_video_sec} ${
                        isPlaying == false ? comon.inactive : ""
                    }`}
                >
                    <video
                        ref={videoRef}
                        // onClick={togglePlayPause}
                        autoPlay
                        muted
                        loop
                        playsInline={true}
                        width="100%"
                        height="auto"
                    >
                        <source src={pageData.acf.details_t.video.url} type="video/mp4" />
                    </video>
                </div> */}

                <video
                    className={`${comon.video_block} ${comon.video_min_height_section} video_block video_block_home`}
                    autoPlay
                    muted
                    loop
                    playsInline={true}
                    width="100%"
                    height="auto"
                    style={{ maxHeight: '100%' }}
                >
                    <source src={pageData.acf.details_t.video.url} type="video/mp4" />
                </video>

            </section>

            <section className={`${comon.pt_60} ${comon.mob_pt_15} ${comon.pb_60}`}>
                <div
                    className={`${comon.wrap} ${comon.d_flex_wrap} ${comon.flex_center}`}
                >
                    <div
                        data-aos="fade-in"
                        data-aos-duration="1000"
                        className={`${boulevard.img_left_block} `}
                    >
                        <Swiper
                            slidesPerView={1}
                            pagination={true}
                            navigation={{
                                nextEl: ".latest_model_swiper.next",
                                prevEl: ".latest_model_swiper.prev",
                            }}
                            modules={[Autoplay, Navigation, Pagination]}
                            className="mySwiper latest_model_swiper"
                        >
                            {pageData.acf.latest_details.image_slider?.length > 1 && (
                                pageData.acf.latest_details.image_slider.map((image, index) => (
                                    <SwiperSlide key={index}>
                                        <div className={`  ${boulevard.latest_model_swiper_img}`}>
                                            <Image
                                                className={`${comon.w_100} ${comon.img} ${comon.holder} slide_img`}
                                                src={image.image}
                                                height={400}
                                                width={700}
                                                alt=""
                                                quality={100}
                                            />
                                        </div>
                                    </SwiperSlide>
                                )))}
                        </Swiper>

                        <div className={`${boulevard.swiper_btn_sec} `}>
                            <div
                                className={`${boulevard.swiper_btn} ${boulevard.prev}  ${rtl.prev_boul}  latest_model_swiper prev `}
                            >
                                <Image
                                    src={"/images/next_icon1.svg"}
                                    height={30}
                                    width={30}
                                    alt=""
                                />
                            </div>
                            <div
                                className={`${boulevard.swiper_btn} ${rtl.swiper_btn_boul} latest_model_swiper next `}
                            >
                                <Image
                                    src={"/images/next_icon1.svg"}
                                    height={30}
                                    width={30}
                                    alt=""
                                />
                            </div>
                        </div>
                    </div>
                    <div
                        data-aos="fade-in"
                        data-aos-duration="1000"
                        className={`${boulevard.txt_right_block}  ${rtl.txt_right_block}   `}
                    >
                        <div className={`${comon.title_30}`}>
                            <h3>{pageData.acf.latest_details.title && parse(pageData.acf.latest_details.title)}</h3>
                        </div>
                        <div className={`${comon.text_collor} ${comon.pt_15}`}>
                            {pageData.acf.latest_details.description && parse(pageData.acf.latest_details.description)}
                        </div>
                    </div>
                </div>
            </section>

            <Marquee
                data-aos="fade-in"
                data-aos-duration="1000"
                content={formattedContent1}
                direction="left"
                font_size20={true}
                // speed={100000}
                speed={9999}
                bg_black={true}
            />

            <section
                className={`${comon.pt_60} ${comon.pb_65}`}
                style={{ backgroundColor: "#000" }}
                // style={{
                //     background:
                //         "linear-gradient(131.38deg, rgba(103, 88, 182, 1) 0%, rgba(59, 48, 100, 1) 100%)",
                // }}
                id="riaydh-seasons"
            >
                <div className={comon.overflow_hide}>
                    <div
                        className={` ${comon.wrap} ${boulevard.season_product_title} ${comon.d_flex_wrap}`}
                    >
                        <div
                            data-aos="fade-in"
                            data-aos-duration="1000"
                            className={`${comon.w_50}`}
                        >
                            <div className={`${comon.title_30}`}>
                                <h3>{pageData.acf.timer.title && parse(pageData.acf.timer.title)} </h3>
                            </div>
                            <div className={`${comon.pt_15}`}>
                                <p>
                                    {pageData.acf.timer.description && parse(pageData.acf.timer.description)}
                                </p>

                                {pageData.acf.timer.button_text &&
                                    <span
                                        className={`${comon.buttion} ${comon.buttion2}  ${comon.blvd_season_btn} ${comon.but_blue}  ${comon.but_white} ${comon.but_h_02}   ${comon.mt_20}`}
                                        onClick={handleOpen}
                                    >
                                        {pageData.acf.timer.button_text}
                                    </span>
                                }
                            </div>
                        </div>
                        <div
                            data-aos="fade-in"
                            data-aos-duration="1000"
                            className={`${comon.w_50} ${comon.d_flex_wrap}`}
                        >
                            <div
                                className={`${boulevard.conter_wrap_outer} ${rtl.conter_wrap_outer}  flipcard_sec`}
                                style={{ direction: 'ltr' }}
                            >

                                {pageData.acf.timer.date && (() => {
                                    const arabicToEnglish = (str) => {
                                        return str
                                            .replace("ص", "am")
                                            .replace("م", "pm")
                                    };
                                    const normalizedDate = arabicToEnglish(pageData.acf.timer.date);
                                    const [day, month, year, time, period] = normalizedDate.match(/(\d{2})\/(\d{2})\/(\d{4}) (\d{1,2}:\d{2}) (am|pm)/i).slice(1);
                                    const formattedDateStr = `${month}/${day}/${year} ${time} ${period}`;
                                    const targetTimestamp = new Date(formattedDateStr).getTime();
                                    const currentTimestamp = new Date().getTime();
                                    if (currentTimestamp < targetTimestamp) {
                                        return (
                                            <>
                                                <h3>{pageData.acf.timer.timer_title}</h3>
                                                <FlipClockCountdown
                                                    to={targetTimestamp}
                                                    labels={locale === 'ar' ? ["أيام", "ساعات", "دقائق", "ثواني"] : ["Days", "Hours", "Minutes", "Seconds"]}
                                                    labelStyle={{
                                                        fontSize: 10,
                                                        fontWeight: 500,
                                                    }}
                                                    style={{ fontFamily: 'Aller', direction: 'ltr' }}
                                                >
                                                    Finished
                                                </FlipClockCountdown>
                                            </>
                                        );
                                    }
                                })()}
                            </div>
                        </div>
                    </div>

                    {/* {!isMobile && (
                        <div
                            className={`${boulevard.season_image_section}  ${comon.mt_40}`}
                        >
                            <ul className={`${boulevard.season_image_ul_new}`}>
                                {season.map((season, index) => (
                                    <li key={index} className={`${boulevard.season_image_item}`}>
                                        <div className={`${boulevard.season_image}`}>
                                            <Image
                                                src={season.image}
                                                fill
                                                style={{ objectFit: "cover" }}
                                            />
                                        </div>

                                        <div className={`${boulevard.text}`}>
                                            <h3>{season.title}</h3>
                                        </div>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    )} */}

                    {/* {isMobile && ( */}

                    {/* <div className={`${comon.wrap} ${comon.mt_40}`}>
                        <Swiper
                            spaceBetween={15}
                            slidesPerView={1.5}
                            pagination={{
                                clickable: true,
                            }}
                            modules={[Autoplay, Navigation]}

                            className="mySwiper riyadh_season_swiper "
                            breakpoints={{


                                600: {
                                    slidesPerView: 3.5,
                                },
                            }}
                        >
                            {season.map((season, index) => (
                                <SwiperSlide
                                    key={index}
                                    className={`${boulevard.season_image_item} ${boulevard.season_image_card} ${boulevard.season_image_item_mob}`}
                                >
                                    <div className={`${boulevard.season_image}`} onClick={handleOpen}>
                                        <Image
                                            src={season.image}
                                            fill
                                            style={{ objectFit: "cover" }}
                                        />
                                    </div>

                                    <div className={`${boulevard.text}`}>
                                        <h3>{season.title}</h3>
                                    </div>
                                </SwiperSlide>
                            ))}
                        </Swiper>
                    </div> */}

                    <HoverSlide
                        season={pageData.acf.gallery_pop}
                        popUpAction={true}
                        extra_width_arrows={true}
                        extra_width_arrows_top_space={true}
                        black_bg_arrows={true}

                    />
                </div>
            </section>

            <StartCampaign />

            <section className={`${comon.pt_50}  `}>
                <ContactSection
                    button="Request a Quote"
                    showAutoMargin={false}
                />
            </section>

            <div>
                <Modal
                    open={open}
                    onClose={handleClose}
                    aria-labelledby="modal-modal-title"
                    aria-describedby="modal-modal-description"
                    data-lenis-prevent="true"
                >
                    <Box
                        // sx={style}
                        className={`${contactF.popup_container} ${rtl.popup_container}`}
                    >
                        <button className={`${contactF.popup_close} ${rtl.popup_close}`} onClick={handleClose}>
                            <Image
                                src={"/images/close_btn.svg"}
                                height={30}
                                width={30}
                                alt=" "
                            />
                        </button>
                        <div className={`${contactF.popup_section}  ${rtl.popup_section}`}>
                            <div className={`${contactF.head_sec} ${rtl.head_sec}`}>
                                <h3>{parse(pageData.acf.timer.popup.form_title)}</h3>
                                <p>
                                    {parse(pageData.acf.timer.popup.description)}
                                </p>
                            </div>

                            <RequestSeasonCard button="Submit" />
                        </div>
                    </Box>
                </Modal>
            </div>
        </>
    );
}