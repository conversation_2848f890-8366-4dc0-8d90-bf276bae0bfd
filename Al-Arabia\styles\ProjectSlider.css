.mySwiper.Project_swiper .swiper-slide {
  width: 5%;
  /* Default for non-active slides */
  transition: width 0.5s ease-in-out;
}

/* 
.mySwiper .swiper-slide-active {
  width: 50% !important;
}

.mySwiper .swiper-slide-prev,
.mySwiper .swiper-slide-next {
  width: 20% !important;
} */

.slider_secton_main {
  height: 600px;
}

.swiper-horizontal {
  height: 100%;
}

/* .swiper-slide img {
  max-width: 100%; 
  width: 100%;
  height: auto;
  transition: all .25s ease-out;
  transform: scale(.25);
  transform-origin: center;
}

.swiper-slide-active img {
  transform: scale(1);
} */

.project_img {
  width: auto;
  height: auto;
}

.max_w_img {
  max-width: 100%;
}

.swiper-slide-active .slide_img_new {
  transform: scale(2);
}

.slider_secton_new {
  height: 800px;
}

.slider_secton_new .swiper-slide-active {
  margin-top: 150px;
}

.slider_secton_new .swiper-slide:not(.swiper-slide-active) {
  margin-top: 130px;
}

.slider_secton_new .swiper-slide-prev {
  margin-left: -80px;
}

.slider_secton_new .swiper-slide-next {
  margin-right: -80px;
}

@media only screen and (max-width: 700px) {
  .slider_secton {
    margin-top: 25px;
  }

  .project_img {
    width: 25%;
  }
}